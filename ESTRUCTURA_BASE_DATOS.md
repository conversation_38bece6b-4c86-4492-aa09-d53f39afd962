# 🗄️ Estructura de la Base de Datos - Opera App

## 📋 **Resumen**

Base de datos PostgreSQL que almacena toda la información del sistema Opera: usuarios, turnos, ubicaciones GPS, identificaciones, novedades y mensajes.

## 🏗️ **Tablas Principales**

### **👥 users**
Almacena información de agentes y supervisores.

```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    folio VARCHAR(80) UNIQUE NOT NULL,           -- Número de identificación del agente
    password_hash VARCHAR(256) NOT NULL,         -- Contraseña hasheada
    name VARCHAR(100),                           -- Nombre completo
    role VARCHAR(20) NOT NULL DEFAULT 'agente',  -- 'agente' o 'comando'
    unit_id VARCHAR(50),                         -- Unidad asignada
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

CREATE INDEX ix_users_folio ON users(folio);
CREATE INDEX ix_users_role ON users(role);
```

### **⏰ shifts**
Registra las jornadas laborales de los agentes.

```sql
CREATE TABLE shifts (
    id SERIAL PRIMARY KEY,
    officer_id INTEGER NOT NULL,                -- FK a users.id
    start_time TIMESTAMP NOT NULL,              -- Inicio del turno
    end_time TIMESTAMP NULL,                    -- Fin del turno (NULL = activo)
    start_latitude REAL,                       -- Ubicación de inicio
    start_longitude REAL,
    end_latitude REAL,                         -- Ubicación de fin
    end_longitude REAL,
    total_distance REAL,                       -- Distancia total recorrida
    total_identifications INTEGER DEFAULT 0,   -- Total de identificaciones
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (officer_id) REFERENCES users(id)
);

CREATE INDEX ix_shifts_officer_id ON shifts(officer_id);
CREATE INDEX ix_shifts_start_time ON shifts(start_time);
CREATE INDEX ix_shifts_end_time ON shifts(end_time);
```

### **📍 gps_track_points**
Puntos GPS registrados durante los turnos.

```sql
CREATE TABLE gps_track_points (
    id SERIAL PRIMARY KEY,
    shift_id INTEGER NOT NULL,                  -- FK a shifts.id
    latitude REAL NOT NULL,                     -- Latitud
    longitude REAL NOT NULL,                    -- Longitud
    accuracy REAL,                             -- Precisión del GPS
    timestamp TIMESTAMP NOT NULL,              -- Momento del registro
    
    FOREIGN KEY (shift_id) REFERENCES shifts(id) ON DELETE CASCADE
);

CREATE INDEX ix_gps_track_points_shift_id ON gps_track_points(shift_id);
CREATE INDEX ix_gps_track_points_timestamp ON gps_track_points(timestamp);
```

### **👤 identified_persons**
Personas identificadas por los agentes.

```sql
CREATE TABLE identified_persons (
    id SERIAL PRIMARY KEY,
    officer_id INTEGER NOT NULL,               -- FK a users.id
    shift_id INTEGER,                          -- FK a shifts.id
    dni_number VARCHAR(20),                    -- Número de DNI
    first_names VARCHAR(100),                  -- Nombres
    last_name VARCHAR(100),                    -- Apellido
    dob DATE,                                  -- Fecha de nacimiento
    location_lat REAL,                         -- Ubicación de la identificación
    location_lon REAL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (officer_id) REFERENCES users(id),
    FOREIGN KEY (shift_id) REFERENCES shifts(id)
);

CREATE INDEX ix_identified_persons_officer_id ON identified_persons(officer_id);
CREATE INDEX ix_identified_persons_dni_number ON identified_persons(dni_number);
CREATE INDEX ix_identified_persons_timestamp ON identified_persons(timestamp);
```

### **🚗 identified_vehicles**
Vehículos identificados por los agentes.

```sql
CREATE TABLE identified_vehicles (
    id SERIAL PRIMARY KEY,
    officer_id INTEGER NOT NULL,               -- FK a users.id
    shift_id INTEGER,                          -- FK a shifts.id
    license_plate VARCHAR(20),                 -- Patente del vehículo
    brand VARCHAR(50),                         -- Marca
    model VARCHAR(50),                         -- Modelo
    color VARCHAR(30),                         -- Color
    location_lat REAL,                         -- Ubicación de la identificación
    location_lon REAL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (officer_id) REFERENCES users(id),
    FOREIGN KEY (shift_id) REFERENCES shifts(id)
);

CREATE INDEX ix_identified_vehicles_officer_id ON identified_vehicles(officer_id);
CREATE INDEX ix_identified_vehicles_license_plate ON identified_vehicles(license_plate);
CREATE INDEX ix_identified_vehicles_timestamp ON identified_vehicles(timestamp);
```

### **📢 novelties**
Novedades y alertas del sistema.

```sql
CREATE TABLE novelties (
    id SERIAL PRIMARY KEY,
    content TEXT NOT NULL,                      -- Contenido de la novedad
    image_url VARCHAR(255),                     -- URL de imagen (opcional)
    created_by_user_id INTEGER NOT NULL,       -- FK a users.id (quien creó)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by_user_id) REFERENCES users(id)
);

CREATE INDEX ix_novelties_created_by_user_id ON novelties(created_by_user_id);
CREATE INDEX ix_novelties_created_at ON novelties(created_at);
```

### **👁️ novelty_read_status**
Control de lectura de novedades por usuario.

```sql
CREATE TABLE novelty_read_status (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,                  -- FK a users.id
    novelty_id INTEGER NOT NULL,               -- FK a novelties.id
    read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (novelty_id) REFERENCES novelties(id) ON DELETE CASCADE
);

CREATE INDEX ix_novelty_read_status_user_id ON novelty_read_status(user_id);
CREATE INDEX ix_novelty_read_status_novelty_id ON novelty_read_status(novelty_id);
```

## 🔔 **Nuevas Tablas - Sistema de Notificaciones**

### **💬 operator_messages** *(NUEVA)*
Mensajes del operador a los agentes.

```sql
CREATE TABLE operator_messages (
    id SERIAL PRIMARY KEY,
    content TEXT NOT NULL,                      -- Contenido del mensaje
    sender_user_id INTEGER NOT NULL,           -- FK a users.id (supervisor)
    recipient_folio VARCHAR(80) NOT NULL,      -- FK a users.folio (agente)
    message_type VARCHAR(20) NOT NULL DEFAULT 'personal', -- personal/multiple/broadcast
    is_read BOOLEAN DEFAULT FALSE,              -- Estado de lectura
    sent_via_websocket BOOLEAN DEFAULT FALSE,  -- Si se envió por WebSocket
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,                     -- Cuándo se leyó
    
    FOREIGN KEY (sender_user_id) REFERENCES users(id),
    FOREIGN KEY (recipient_folio) REFERENCES users(folio)
);

CREATE INDEX ix_operator_messages_sender_user_id ON operator_messages(sender_user_id);
CREATE INDEX ix_operator_messages_recipient_folio ON operator_messages(recipient_folio);
CREATE INDEX ix_operator_messages_message_type ON operator_messages(message_type);
CREATE INDEX ix_operator_messages_is_read ON operator_messages(is_read);
CREATE INDEX ix_operator_messages_created_at ON operator_messages(created_at);
```

### **🔔 notification_status** *(NUEVA)*
Estado de notificaciones de novedades por usuario.

```sql
CREATE TABLE notification_status (
    id SERIAL PRIMARY KEY,
    user_folio VARCHAR(80) NOT NULL,           -- FK a users.folio
    last_seen_novelty_id INTEGER NULL,         -- FK a novelties.id (última vista)
    last_check_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_notification_at TIMESTAMP NULL,       -- Última notificación enviada
    
    FOREIGN KEY (user_folio) REFERENCES users(folio),
    FOREIGN KEY (last_seen_novelty_id) REFERENCES novelties(id)
);

CREATE INDEX ix_notification_status_user_folio ON notification_status(user_folio);
```

## 🔗 **Relaciones Principales**

```
users (1) ←→ (N) shifts
shifts (1) ←→ (N) gps_track_points
users (1) ←→ (N) identified_persons
users (1) ←→ (N) identified_vehicles
users (1) ←→ (N) novelties (created_by)
users (1) ←→ (N) novelty_read_status
users (1) ←→ (N) operator_messages (sender)
users (1) ←→ (N) operator_messages (recipient)
users (1) ←→ (1) notification_status
```

## 📊 **Estadísticas de Uso**

### **Consultas Frecuentes:**
- Turnos activos: `SELECT * FROM shifts WHERE end_time IS NULL`
- Ubicaciones recientes: `SELECT * FROM gps_track_points WHERE timestamp > NOW() - INTERVAL '30 minutes'`
- Mensajes no leídos: `SELECT * FROM operator_messages WHERE recipient_folio = ? AND is_read = FALSE`
- Novedades nuevas: `SELECT * FROM novelties WHERE id > (SELECT last_seen_novelty_id FROM notification_status WHERE user_folio = ?)`

### **Índices Críticos:**
- `ix_shifts_end_time` - Para encontrar turnos activos
- `ix_gps_track_points_timestamp` - Para ubicaciones recientes
- `ix_operator_messages_is_read` - Para mensajes no leídos
- `ix_novelties_created_at` - Para novedades ordenadas

## 🛠️ **Mantenimiento**

### **Limpieza Automática:**
- GPS antiguos: Eliminar puntos > 30 días
- Mensajes leídos: Archivar mensajes > 90 días
- Logs de identificación: Mantener 1 año

### **Respaldos:**
- Diario: Datos operacionales
- Semanal: Estructura completa
- Mensual: Archivo histórico

---

**Última actualización:** 26 de Mayo, 2025  
**Versión:** 2.0 - Con Sistema de Notificaciones  
**Motor:** PostgreSQL 16.9
