# Implementación de la Opción 1: Endpoint Anonymous

## ✅ Cambios Realizados

### 1. **Ser<PERSON>or (Backend)**
- ✅ Creado endpoint `/api/shifts/track_anonymous` en `app/routes/shifts.py`
- ✅ Acepta folio/password en lugar de JWT token
- ✅ Misma funcionalidad que `/track_simple` pero sin autenticación JWT

### 2. **Android (App Móvil)**
- ✅ Cambiada URL del servidor a `/track_anonymous`
- ✅ Modificado `LocationTrackingService.kt` para enviar folio/password
- ✅ Agregadas constantes para guardar credenciales del usuario
- ✅ Agregado método `getUserCredentials()` para obtener folio/password
- ✅ Modificado `MainActivity.kt` para guardar credenciales
- ✅ Agregado método `saveUserCredentials()` en MainActivity
- ✅ Agregado método `saveCredentials()` en WebAppInterface

### 3. **JavaScript (Frontend)**
- ✅ Modificado `agent_simulator.js` para enviar credenciales a Android
- ✅ Agregada llamada a `AndroidInterface.saveCredentials(folio, password)`

## 🔄 Flujo Actualizado

1. **Usuario se loguea en el simulador**
   - Selecciona folio de la lista
   - Ingresa contraseña
   - JavaScript hace login con `/api/auth/login`

2. **JavaScript recibe respuesta exitosa**
   - Guarda token en localStorage (para el simulador web)
   - **NUEVO:** Envía folio/password a Android via `AndroidInterface.saveCredentials()`

3. **Android guarda credenciales**
   - Guarda folio y password en SharedPreferences
   - LocationTrackingService puede acceder a estas credenciales

4. **LocationTrackingService envía ubicaciones**
   - Usa `/api/shifts/track_anonymous` en lugar de `/track_simple`
   - Envía folio/password en cada request en lugar de token JWT
   - Servidor autentica en cada llamada

## 📋 Formato del Request de Ubicación

**Antes (con token):**
```json
POST /api/shifts/track_simple
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...

{
  "latitude": -40.8091167,
  "longitude": -62.9940436,
  "accuracy": 10.5,
  "timestamp": 1640995200000
}
```

**Ahora (con credenciales):**
```json
POST /api/shifts/track_anonymous

{
  "folio": "usuario123",
  "password": "contraseña",
  "latitude": -40.8091167,
  "longitude": -62.9940436,
  "accuracy": 10.5,
  "timestamp": 1640995200000
}
```

## 🧪 Cómo Probar

### 1. **Probar el endpoint del servidor**
```bash
curl -X POST https://patagoniaservers.com.ar:5005/api/shifts/track_anonymous \
  -H "Content-Type: application/json" \
  -d '{
    "folio": "TU_FOLIO_AQUI",
    "password": "TU_PASSWORD_AQUI",
    "latitude": -40.8091167,
    "longitude": -62.9940436,
    "accuracy": 10.5
  }'
```

### 2. **Probar la app Android**
1. Compilar y ejecutar la app
2. Ir a `https://patagoniaservers.com.ar:5005/_simulate/`
3. Seleccionar un folio de la lista
4. Ingresar contraseña
5. Hacer login
6. Verificar en los logs que se guardan las credenciales
7. Verificar que las ubicaciones se envían correctamente

### 3. **Logs esperados**

**En Android:**
```
D/WebAppInterface: Credenciales recibidas desde JavaScript: folio=usuario123
D/MainActivity: Credenciales de usuario guardadas exitosamente en SharedPreferences
D/LocationTrackingService: 🚀 Iniciando envío de ubicación: -40.8091167, -62.9940436
D/LocationTrackingService: ✅ Ubicación enviada exitosamente a servidor principal: ID=123
```

**En el servidor:**
```
INFO: Punto GPS (app móvil anónimo) registrado para usuario usuario123
```

## ⚠️ Notas Importantes

1. **Seguridad:** Las credenciales se guardan en SharedPreferences (encriptadas por Android)
2. **Compatibilidad:** Mantiene compatibilidad con el simulador web existente
3. **Fallback:** Si no hay credenciales, el servicio falla graciosamente
4. **Testing:** Probar primero el endpoint con curl antes de probar la app completa

## 🔍 Troubleshooting

Si sigues viendo el error "No hay credenciales de usuario disponibles":

1. Verificar que el login en el simulador funciona
2. Verificar que se llama a `AndroidInterface.saveCredentials()`
3. Verificar que las credenciales se guardan en SharedPreferences
4. Verificar que `getUserCredentials()` las puede leer

## 📊 Ventajas de esta Solución

- ✅ **Funciona inmediatamente** - no depende del flujo de tokens
- ✅ **Segura** - requiere credenciales válidas en cada request
- ✅ **Simple** - cambios mínimos en el código existente
- ✅ **Confiable** - no depende de que el token se pase correctamente
- ✅ **Trazable** - cada ubicación se asocia al usuario correcto
