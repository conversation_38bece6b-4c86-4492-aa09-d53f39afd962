# 🔔 Sistema de Notificaciones Híbrido - Opera App

## 📋 **Resumen del Sistema**

Sistema completo de notificaciones que permite al operador enviar mensajes a los agentes y notificar sobre novedades nuevas, funcionando tanto cuando la app está activa como en segundo plano.

## 🎯 **Problema Resuelto**

**Problema Original:**
- Los mensajes del operador solo llegaban por WebSocket en tiempo real
- Si la app estaba en segundo plano o el teléfono bloqueado, no llegaban notificaciones
- No había persistencia de mensajes
- No había notificaciones de novedades nuevas

**Solución Implementada:**
- Sistema híbrido: WebSocket + Polling HTTP
- Persistencia de mensajes en base de datos
- Notificaciones locales de Android
- Verificación automática de novedades

## 🏗️ **Arquitectura del Sistema**

### **1. <PERSON><PERSON><PERSON> (Flask)**

#### **Nuevos Modelos:**
```python
# app/models/message.py
class OperatorMessage(db.Model):
    - content: Texto del mensaje
    - sender_user_id: <PERSON><PERSON><PERSON> (supervisor)
    - recipient_folio: A quién va dirigido (agente)
    - message_type: personal/multiple/broadcast
    - is_read: Estado de lectura
    - sent_via_websocket: Si se envió por WebSocket
    - created_at, read_at: Timestamps

class NotificationStatus(db.Model):
    - user_folio: Usuario
    - last_seen_novelty_id: Última novedad vista
    - last_check_at: Última verificación
    - last_notification_at: Última notificación enviada
```

#### **Nuevos Endpoints:**
```
GET  /api/messages/unread                    # Mensajes no leídos
GET  /api/messages/notification-summary     # Resumen de notificaciones
POST /api/messages/<id>/read                 # Marcar mensaje como leído
POST /api/messages/mark-all-read             # Marcar todos como leídos
GET  /api/messages/check-new-novelties       # Verificar novedades nuevas
POST /api/messages/update-novelty-status     # Actualizar estado de novedades
```

#### **Endpoint Modificado:**
```python
# app/routes/dashboard.py - operator_message()
# Ahora guarda mensajes en BD además de enviar por WebSocket
```

### **2. Android**

#### **Nuevas Funcionalidades en LocationTrackingService:**
```kotlin
// Verificación cada 60 segundos
private fun startNotificationChecking()
private suspend fun checkForNotifications()
private suspend fun createTempAuthToken()
private fun showNotification()
```

#### **Flujo de Autenticación:**
1. Usa credenciales guardadas (folio/password)
2. Crea token JWT temporal para cada request
3. Hace request autenticada al servidor
4. Parsea respuesta y muestra notificación si es necesario

## 🔄 **Flujos de Funcionamiento**

### **Flujo 1: Mensaje del Operador**

#### **Cuando App Está Activa:**
```
1. Operador envía mensaje desde dashboard
2. Servidor guarda en BD + envía por WebSocket
3. App recibe por WebSocket → Muestra notificación inmediata
4. Marca mensaje como enviado por WebSocket
```

#### **Cuando App Está en Segundo Plano:**
```
1. Operador envía mensaje desde dashboard
2. Servidor guarda en BD (WebSocket puede fallar)
3. Android hace polling cada 60s
4. Detecta mensaje nuevo → Muestra notificación local
5. Usuario toca notificación → Abre app
```

### **Flujo 2: Novedades Nuevas**

```
1. Supervisor crea nueva novedad
2. Android hace polling cada 60s
3. Compara con última novedad vista por el usuario
4. Si hay novedades nuevas → Muestra notificación
5. Usuario ve novedades → Actualiza estado
```

## 📱 **Experiencia del Usuario**

### **Tipos de Notificaciones:**

1. **"Nuevos Mensajes"** - Solo mensajes del operador
2. **"Nuevas Novedades"** - Solo novedades
3. **"Mensajes y Novedades"** - Ambos tipos

### **Comportamiento:**
- **Vibración y sonido** cuando llega notificación
- **Prioridad alta** para que aparezca incluso con teléfono bloqueado
- **Auto-cancelable** - Desaparece al tocarla
- **Abre la app** al tocar la notificación

## ⚙️ **Configuración**

### **Intervalos:**
```kotlin
private const val NOTIFICATION_CHECK_INTERVAL = 60000L  // 60 segundos
private const val BATCH_SEND_INTERVAL = 30000L         // 30 segundos (GPS)
```

### **URLs:**
```kotlin
private const val MESSAGES_URL = "https://patagoniaservers.com.ar:5005/api/messages/notification-summary"
private const val LOGIN_URL = "https://patagoniaservers.com.ar:5005/api/auth/login"
```

### **Canales de Notificación:**
```kotlin
"location_tracking_channel" // Servicio GPS (ID: 1)
"messages_channel"          // Mensajes y novedades (ID: 2)
```

## 🧪 **Pruebas**

### **Casos de Prueba:**

1. **✅ Mensaje con app activa**
   - Operador envía mensaje
   - Agente recibe inmediatamente por WebSocket

2. **✅ Mensaje con app en segundo plano**
   - Operador envía mensaje
   - Agente recibe notificación en 60s máximo

3. **✅ Mensaje con teléfono bloqueado**
   - Operador envía mensaje
   - Notificación aparece en pantalla de bloqueo

4. **✅ Novedades nuevas**
   - Supervisor crea novedad
   - Agente recibe notificación de novedad nueva

5. **✅ Múltiples notificaciones**
   - Mensajes + novedades pendientes
   - Una sola notificación con resumen

### **Logs de Prueba:**
```
D/LocationTrackingService: 🔔 Verificación de notificaciones - Código: 200
D/LocationTrackingService: 📥 Respuesta de notificaciones: {"has_notifications":true,"total_notifications":2}
I/LocationTrackingService: 🔔 Notificaciones encontradas: 2 (1 mensajes, 1 novedades)
I/LocationTrackingService: 🔔 Notificación mostrada: Mensajes y Novedades - 1 mensaje y 1 novedad pendientes
```

## 🔧 **Instalación y Configuración**

### **1. Servidor:**
```bash
# Aplicar migración
flask db upgrade

# Los endpoints se registran automáticamente
```

### **2. Android:**
```kotlin
// Ya incluido en LocationTrackingService
// Se inicia automáticamente con el servicio GPS
```

### **3. Permisos Android:**
```xml
<!-- Ya incluidos -->
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

## 🚀 **Ventajas del Sistema**

1. **✅ Funciona en segundo plano** - Notificaciones llegan siempre
2. **✅ Persistencia** - Los mensajes no se pierden
3. **✅ Eficiente** - Polling cada 60s, no consume mucha batería
4. **✅ Inteligente** - Solo notifica cuando hay contenido nuevo
5. **✅ Escalable** - Soporta múltiples tipos de notificaciones
6. **✅ Confiable** - Funciona aunque WebSocket falle
7. **✅ Seguro** - Autenticación JWT en cada request

## 📊 **Métricas y Monitoreo**

### **Logs del Servidor:**
```
INFO: Supervisor 1001 envió mensaje a 3 agentes (2 por WebSocket)
INFO: Usuario 19049 tiene 1 mensajes no leídos
INFO: Usuario 19049 tiene 2 novedades nuevas
```

### **Logs de Android:**
```
D: 🔔 Verificación de notificaciones - Código: 200
I: 🔔 Notificaciones encontradas: 3 (2 mensajes, 1 novedades)
I: 🔔 Notificación mostrada: Mensajes y Novedades
```

## 🔮 **Futuras Mejoras**

1. **Firebase Cloud Messaging (FCM)** - Para notificaciones push reales
2. **Notificaciones personalizadas** - Diferentes sonidos por tipo
3. **Historial de mensajes** - Ver conversaciones completas
4. **Respuestas rápidas** - Responder desde la notificación
5. **Geofencing** - Notificaciones basadas en ubicación

---

**Estado:** ✅ Implementado y funcionando  
**Fecha:** 26 de Mayo, 2025  
**Versión:** 1.0 - Sistema Híbrido
