# Soluciones del Lado del Servidor para el Error de Token

## Problema Identificado

La aplicación Android está fallando al enviar ubicaciones GPS porque no tiene un token de autenticación válido. El error específico es:

```
E  No hay token de autenticación disponible
```

## Análisis del Flujo Actual

1. **Usuario se loguea** en el WebView (`https://patagoniaservers.com.ar:5005/_simulate/`)
2. **JavaScript debería capturar** el token y enviarlo a Android via `AndroidInterface.onLoginSuccess(token)`
3. **Android debería guardar** el token en SharedPreferences
4. **LocationTrackingService** usa ese token para enviar ubicaciones a `/api/shifts/track_simple`

**El problema:** El paso 2 o 3 está fallando, por lo que Android no tiene token.

## Soluciones del Lado del Servidor (Sin Modificar Android)

### ✅ **Opción 1: Endpoint Sin Autenticación JWT (IMPLEMENTADA)**

**Ventajas:**
- No requiere modificar la app Android
- Funciona inmediatamente
- Mantiene seguridad con folio/password

**Implementación:**
- Nuevo endpoint: `POST /api/shifts/track_anonymous`
- Usa folio/password en lugar de JWT token
- Misma funcionalidad que `/track_simple`

**Formato del Request:**
```json
{
    "folio": "usuario123",
    "password": "contraseña",
    "latitude": -40.8091167,
    "longitude": -62.9940436,
    "accuracy": 10.5,
    "timestamp": 1640995200000
}
```

**Para usar esta solución:**
1. Cambiar la URL en Android de:
   ```kotlin
   private const val SERVER_URL = "https://patagoniaservers.com.ar:5005/api/shifts/track_simple"
   ```
   a:
   ```kotlin
   private const val SERVER_URL = "https://patagoniaservers.com.ar:5005/api/shifts/track_anonymous"
   ```

2. Modificar el método `tryServerConnection` para enviar folio/password en lugar del token Bearer.

### 🔧 **Opción 2: Endpoint con Token en Body (Alternativa)**

Si prefieres mantener algún tipo de token, puedes crear un endpoint que acepte el token en el body del request en lugar del header Authorization.

### 🔧 **Opción 3: Endpoint con API Key (Alternativa)**

Crear un endpoint que use una API key fija para la aplicación móvil, sin requerir autenticación de usuario individual.

## Recomendación

**Usar la Opción 1** porque:
- Es la más segura (requiere credenciales válidas)
- Es la más simple de implementar
- No requiere cambios complejos en Android
- Mantiene trazabilidad por usuario

## Próximos Pasos

1. **Probar el nuevo endpoint** con Postman o curl:
   ```bash
   curl -X POST https://patagoniaservers.com.ar:5005/api/shifts/track_anonymous \
     -H "Content-Type: application/json" \
     -d '{
       "folio": "tu_folio",
       "password": "tu_password", 
       "latitude": -40.8091167,
       "longitude": -62.9940436,
       "accuracy": 10.5
     }'
   ```

2. **Modificar Android** para usar el nuevo endpoint (cambio mínimo de URL y formato de datos)

3. **Monitorear logs** del servidor para confirmar que las ubicaciones se están recibiendo correctamente

## Logs Esperados Después de la Solución

En lugar de:
```
E  No hay token de autenticación disponible
```

Deberías ver:
```
I  Punto GPS (app móvil anónimo) registrado para usuario FOLIO_USUARIO
```
