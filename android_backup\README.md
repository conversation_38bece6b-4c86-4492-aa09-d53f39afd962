# Android App - Backup de Archivos Modificados

## 📋 Resumen de Cambios

Este backup contiene los archivos de la aplicación Android modificados para implementar la **Opción 1: Endpoint Anonymous** que resuelve el problema del token JWT.

### 🎯 Problema Resuelto

**Error Original:**
```
E  No hay token de autenticación disponible
```

**Solución Implementada:**
- Uso de credenciales folio/password en lugar de token JWT
- Nuevo endpoint `/api/shifts/track_anonymous` en el servidor
- Comunicación JavaScript → Android para pasar credenciales

## 📁 Archivos Modificados

### 1. `LocationTrackingService.kt`

**Cambios principales:**
- ✅ **URL del servidor:** Cambiada a `/track_anonymous`
- ✅ **Autenticación:** Usa folio/password en lugar de token JWT
- ✅ **Nuevas constantes:** `KEY_USER_FOLIO`, `KEY_USER_PASSWORD`
- ✅ **Nuevo método:** `getUserCredentials()` para obtener credenciales
- ✅ **JSON modificado:** Incluye folio/password en cada request

**Líneas clave modificadas:**
- Línea 47: `SERVER_URL = "...track_anonymous"`
- Líneas 51-52: Nuevas constantes para credenciales
- Líneas 376-383: Método `tryServerConnection()` modificado
- Líneas 406-415: JSON con credenciales
- Líneas 478-483: Método `getUserCredentials()`

### 2. `MainActivity.kt`

**Cambios principales:**
- ✅ **Nuevas constantes:** Para guardar credenciales del usuario
- ✅ **Nuevo método:** `saveUserCredentials()` para guardar folio/password
- ✅ **WebAppInterface ampliado:** Método `saveCredentials()` para recibir desde JavaScript

**Líneas clave modificadas:**
- Líneas 34-35: Nuevas constantes para credenciales
- Líneas 383-395: Método `saveUserCredentials()`
- Líneas 418-427: Método `saveCredentials()` en WebAppInterface

## 🔄 Flujo de Funcionamiento

### 1. **Login en el Simulador**
```
Usuario → Selecciona folio → Ingresa contraseña → Login exitoso
```

### 2. **JavaScript → Android**
```javascript
// En agent_simulator.js (líneas 291-302)
if (typeof AndroidInterface !== 'undefined') {
    AndroidInterface.saveCredentials(folio, password);
}
```

### 3. **Android Guarda Credenciales**
```kotlin
// En MainActivity.kt
fun saveUserCredentials(folio: String, password: String) {
    // Guarda en SharedPreferences
}
```

### 4. **Envío de Ubicaciones**
```kotlin
// En LocationTrackingService.kt
val jsonData = JSONObject().apply {
    put("folio", userCredentials.first)
    put("password", userCredentials.second)
    put("latitude", location.latitude)
    put("longitude", location.longitude)
    // ...
}
```

### 5. **Servidor Procesa**
```
POST /api/shifts/track_anonymous
{
  "folio": "19049",
  "password": "123456",
  "latitude": -40.8091195,
  "longitude": -62.9940258,
  "accuracy": 15.654
}
```

## ✅ Resultados de Pruebas

### **Logs de Éxito:**
```
[LOGIN] AndroidInterface disponible, métodos: onLoginSuccess,saveCredentials,showToast
[LOGIN] Enviando credenciales a Android...
D/WebAppInterface: Credenciales recibidas desde JavaScript: folio=19049
D/MainActivity: Credenciales de usuario guardadas exitosamente en SharedPreferences
D/LocationTrackingService: ✅ Ubicación enviada exitosamente a servidor principal: ID=2
D/LocationTrackingService: 📥 Respuesta del servidor principal: {
  "msg": "Ubicación registrada exitosamente",
  "point_id": 8553,
  "status": "success"
}
```

### **Pruebas Realizadas:**
- ✅ **Folio 19049, password 123456** → Éxito
- ✅ **Folio 7115, password 123456** → Éxito
- ❌ **Credenciales inválidas** → Rechazado correctamente
- ✅ **App en primer plano** → Funcionando
- ✅ **Envío automático cada 30s** → Funcionando

## 🚀 Implementación Completa

### **Estado del Proyecto:**
- ✅ **Servidor:** Endpoint `/track_anonymous` funcionando
- ✅ **Android:** Usando credenciales en lugar de token
- ✅ **JavaScript:** Enviando credenciales a Android
- ✅ **Base de datos:** Guardando y enviando ubicaciones
- ✅ **Autenticación:** Validación folio/password exitosa

### **Próximos Pasos:**
1. Probar con app en segundo plano (teléfono bloqueado)
2. Probar con diferentes usuarios/folios
3. Monitorear logs del servidor para validación
4. Deploy a producción

## 📊 Ventajas de la Solución

1. **✅ Funciona inmediatamente** - No depende del flujo de tokens
2. **✅ Segura** - Requiere credenciales válidas en cada request
3. **✅ Simple** - Cambios mínimos en el código existente
4. **✅ Confiable** - No depende de que el token se pase correctamente
5. **✅ Multi-usuario** - Cada agente usa sus propias credenciales
6. **✅ Trazable** - Cada ubicación se asocia al usuario correcto

## 🔧 Configuración

### **Variables importantes:**
- `SERVER_URL`: `https://patagoniaservers.com.ar:5005/api/shifts/track_anonymous`
- `BATCH_SEND_INTERVAL`: 30 segundos
- `MAX_RETRY_ATTEMPTS`: 4 intentos
- `CONNECTION_TIMEOUT`: 15 segundos

### **SharedPreferences keys:**
- `user_folio`: Folio del usuario
- `user_password`: Contraseña del usuario
- `access_token`: Token JWT (mantenido para compatibilidad)

## 🔔 **NUEVO: Sistema de Notificaciones Híbrido**

### **📱 Funcionalidades Agregadas:**

#### **1. Notificaciones de Mensajes del Operador**
- **Polling cada 60 segundos** para verificar mensajes pendientes
- **Notificaciones locales** cuando hay mensajes nuevos
- **Persistencia en BD** - Los mensajes se guardan aunque el WebSocket falle

#### **2. Notificaciones de Novedades**
- **Verificación automática** de novedades nuevas
- **Seguimiento de estado** - Sabe qué novedades ya vio el usuario
- **Notificaciones inteligentes** - Solo notifica novedades realmente nuevas

#### **3. Sistema Híbrido**
- **WebSocket + Notificaciones locales** (cuando app activa)
- **Polling HTTP** (cuando app en segundo plano)
- **Token JWT temporal** para autenticación en polling

### **🔧 Nuevos Endpoints del Servidor:**

1. **`GET /api/messages/unread`** - Mensajes no leídos
2. **`GET /api/messages/notification-summary`** - Resumen de notificaciones
3. **`POST /api/messages/<id>/read`** - Marcar mensaje como leído
4. **`GET /api/messages/check-new-novelties`** - Verificar novedades nuevas
5. **`POST /api/messages/update-novelty-status`** - Actualizar estado de novedades

### **📊 Nuevas Tablas en BD:**

1. **`operator_messages`** - Almacena mensajes del operador
2. **`notification_status`** - Rastrea estado de novedades por usuario

### **🔄 Flujo de Notificaciones:**

#### **Mensajes del Operador:**
```
Operador envía mensaje → Guarda en BD + WebSocket → Si WebSocket falla → Polling lo detecta → Notificación local
```

#### **Novedades:**
```
Nueva novedad creada → Polling verifica estado → Compara con última vista → Notificación si hay nuevas
```

### **⚙️ Configuración Android:**

- **Intervalo de polling:** 60 segundos
- **Canal de notificación:** "Mensajes y Novedades" (prioridad alta)
- **Autenticación:** Token JWT temporal creado automáticamente
- **Notificación ID:** 2 (diferente del servicio GPS que usa 1)

---

**Fecha de implementación:** 26 de Mayo, 2025
**Estado:** ✅ Completamente funcional (GPS + Notificaciones)
**Probado con:** Folios 19049 y 7115
