package com.example.opera

import android.content.ContentValues
import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import android.util.Log
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

/**
 * Helper para manejar la base de datos local de puntos GPS
 */
class LocationDatabaseHelper(context: Context) : SQLiteOpenHelper(context, DATABASE_NAME, null, DATABASE_VERSION) {

    companion object {
        private const val DATABASE_NAME = "location_cache.db"
        private const val DATABASE_VERSION = 1
        private const val TABLE_LOCATIONS = "cached_locations"

        // Columnas de la tabla
        private const val COLUMN_ID = "id"
        private const val COLUMN_LATITUDE = "latitude"
        private const val COLUMN_LONGITUDE = "longitude"
        private const val COLUMN_ACCURACY = "accuracy"
        private const val COLUMN_TIMESTAMP = "timestamp"
        private const val COLUMN_SENT = "sent"
        private const val COLUMN_RETRY_COUNT = "retry_count"

        private const val TAG = "LocationDB"
    }

    override fun onCreate(db: SQLiteDatabase) {
        val createTable = """
            CREATE TABLE $TABLE_LOCATIONS (
                $COLUMN_ID INTEGER PRIMARY KEY AUTOINCREMENT,
                $COLUMN_LATITUDE REAL NOT NULL,
                $COLUMN_LONGITUDE REAL NOT NULL,
                $COLUMN_ACCURACY REAL,
                $COLUMN_TIMESTAMP TEXT NOT NULL,
                $COLUMN_SENT INTEGER DEFAULT 0,
                $COLUMN_RETRY_COUNT INTEGER DEFAULT 0
            )
        """.trimIndent()

        db.execSQL(createTable)
        Log.d(TAG, "Tabla de ubicaciones creada")
    }

    override fun onUpgrade(db: SQLiteDatabase, oldVersion: Int, newVersion: Int) {
        db.execSQL("DROP TABLE IF EXISTS $TABLE_LOCATIONS")
        onCreate(db)
    }

    /**
     * Guarda un punto GPS en la base de datos local
     */
    fun saveLocationPoint(latitude: Double, longitude: Double, accuracy: Float?, timestamp: Long): Long {
        val db = writableDatabase
        val values = ContentValues().apply {
            put(COLUMN_LATITUDE, latitude)
            put(COLUMN_LONGITUDE, longitude)
            put(COLUMN_ACCURACY, accuracy)
            put(COLUMN_TIMESTAMP, formatTimestamp(timestamp))
            put(COLUMN_SENT, 0) // No enviado inicialmente
            put(COLUMN_RETRY_COUNT, 0)
        }

        val id = db.insert(TABLE_LOCATIONS, null, values)
        Log.d(TAG, "Punto GPS guardado localmente: ID=$id, Lat=$latitude, Lng=$longitude")
        return id
    }

    /**
     * Obtiene todos los puntos no enviados
     */
    fun getUnsentLocations(): List<LocationPoint> {
        val locations = mutableListOf<LocationPoint>()
        val db = readableDatabase

        val cursor = db.query(
            TABLE_LOCATIONS,
            null,
            "$COLUMN_SENT = ?",
            arrayOf("0"),
            null,
            null,
            "$COLUMN_TIMESTAMP ASC"
        )

        cursor.use {
            while (it.moveToNext()) {
                val location = LocationPoint(
                    id = it.getLong(it.getColumnIndexOrThrow(COLUMN_ID)),
                    latitude = it.getDouble(it.getColumnIndexOrThrow(COLUMN_LATITUDE)),
                    longitude = it.getDouble(it.getColumnIndexOrThrow(COLUMN_LONGITUDE)),
                    accuracy = if (it.isNull(it.getColumnIndexOrThrow(COLUMN_ACCURACY))) null
                              else it.getFloat(it.getColumnIndexOrThrow(COLUMN_ACCURACY)),
                    timestamp = it.getString(it.getColumnIndexOrThrow(COLUMN_TIMESTAMP)),
                    sent = it.getInt(it.getColumnIndexOrThrow(COLUMN_SENT)) == 1,
                    retryCount = it.getInt(it.getColumnIndexOrThrow(COLUMN_RETRY_COUNT))
                )
                locations.add(location)
            }
        }

        Log.d(TAG, "Obtenidos ${locations.size} puntos no enviados")
        return locations
    }

    /**
     * Marca un punto como enviado
     */
    fun markLocationAsSent(id: Long): Boolean {
        val db = writableDatabase
        val values = ContentValues().apply {
            put(COLUMN_SENT, 1)
        }

        val rowsUpdated = db.update(TABLE_LOCATIONS, values, "$COLUMN_ID = ?", arrayOf(id.toString()))
        val success = rowsUpdated > 0

        if (success) {
            Log.d(TAG, "Punto marcado como enviado: ID=$id")
        } else {
            Log.w(TAG, "No se pudo marcar como enviado: ID=$id")
        }

        return success
    }

    /**
     * Incrementa el contador de reintentos para un punto
     */
    fun incrementRetryCount(id: Long): Boolean {
        val db = writableDatabase
        val updateSql = "UPDATE $TABLE_LOCATIONS SET $COLUMN_RETRY_COUNT = $COLUMN_RETRY_COUNT + 1 WHERE $COLUMN_ID = ?"

        return try {
            db.execSQL(updateSql, arrayOf(id.toString()))
            Log.d(TAG, "Contador de reintentos incrementado para ID=$id")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error incrementando contador de reintentos para ID=$id", e)
            false
        }
    }

    /**
     * Elimina puntos enviados más antiguos que X días
     */
    fun cleanOldSentLocations(daysOld: Int = 7): Int {
        val db = writableDatabase
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -daysOld)
        val cutoffDate = formatTimestamp(calendar.timeInMillis)

        val deletedRows = db.delete(
            TABLE_LOCATIONS,
            "$COLUMN_SENT = ? AND $COLUMN_TIMESTAMP < ?",
            arrayOf("1", cutoffDate)
        )

        Log.d(TAG, "Eliminados $deletedRows puntos antiguos enviados")
        return deletedRows
    }

    /**
     * Obtiene el número de puntos pendientes de envío
     */
    fun getUnsentCount(): Int {
        val db = readableDatabase
        val cursor = db.rawQuery("SELECT COUNT(*) FROM $TABLE_LOCATIONS WHERE $COLUMN_SENT = 0", null)

        cursor.use {
            if (it.moveToFirst()) {
                return it.getInt(0)
            }
        }
        return 0
    }

    /**
     * Obtiene puntos no enviados como Map (para compatibilidad con el servicio)
     */
    fun getUnsentLocationPoints(): List<Map<String, Any?>> {
        val locations = mutableListOf<Map<String, Any?>>()
        val db = readableDatabase

        val cursor = db.query(
            TABLE_LOCATIONS,
            null,
            "$COLUMN_SENT = ?",
            arrayOf("0"),
            null,
            null,
            "$COLUMN_TIMESTAMP ASC"
        )

        cursor.use {
            while (it.moveToNext()) {
                val accuracyValue: Any? = if (it.isNull(it.getColumnIndexOrThrow(COLUMN_ACCURACY))) {
                    null
                } else {
                    it.getFloat(it.getColumnIndexOrThrow(COLUMN_ACCURACY))
                }

                val locationMap = mapOf<String, Any?>(
                    "id" to it.getLong(it.getColumnIndexOrThrow(COLUMN_ID)),
                    "latitude" to it.getDouble(it.getColumnIndexOrThrow(COLUMN_LATITUDE)),
                    "longitude" to it.getDouble(it.getColumnIndexOrThrow(COLUMN_LONGITUDE)),
                    "accuracy" to accuracyValue,
                    "timestamp" to parseTimestamp(it.getString(it.getColumnIndexOrThrow(COLUMN_TIMESTAMP))),
                    "sent" to (it.getInt(it.getColumnIndexOrThrow(COLUMN_SENT)) == 1),
                    "retryCount" to it.getInt(it.getColumnIndexOrThrow(COLUMN_RETRY_COUNT))
                )
                locations.add(locationMap)
            }
        }

        Log.d(TAG, "Obtenidos ${locations.size} puntos no enviados")
        return locations
    }

    /**
     * Alias para limpiar puntos antiguos (compatibilidad)
     */
    fun cleanOldSentPoints(daysOld: Int = 7): Int {
        return cleanOldSentLocations(daysOld)
    }

    /**
     * Convierte timestamp string a Long
     */
    private fun parseTimestamp(timestampStr: String): Long {
        return try {
            val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US)
            sdf.timeZone = TimeZone.getTimeZone("UTC")
            sdf.parse(timestampStr)?.time ?: System.currentTimeMillis()
        } catch (e: Exception) {
            Log.w(TAG, "Error parseando timestamp: $timestampStr", e)
            System.currentTimeMillis()
        }
    }

    /**
     * Formatea un timestamp a string ISO 8601
     */
    private fun formatTimestamp(timestamp: Long): String {
        val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US)
        sdf.timeZone = TimeZone.getTimeZone("UTC")
        return sdf.format(Date(timestamp))
    }
}

/**
 * Clase de datos para representar un punto de ubicación
 */
data class LocationPoint(
    val id: Long,
    val latitude: Double,
    val longitude: Double,
    val accuracy: Float?,
    val timestamp: String,
    val sent: Boolean,
    val retryCount: Int
)
