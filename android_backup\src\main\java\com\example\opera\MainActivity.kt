package com.example.opera

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.webkit.JavascriptInterface
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

class MainActivity : AppCompatActivity() {

    private lateinit var webView: WebView
    private val requiredPermissions = arrayOf(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )

    // Constantes para SharedPreferences (deben coincidir con LocationTrackingService)
    private val PREFS_NAME = "opera_prefs"
    private val KEY_ACCESS_TOKEN = "access_token"
    private val KEY_USER_FOLIO = "user_folio"
    private val KEY_USER_PASSWORD = "user_password"

    // Launcher para solicitar permisos
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            Log.d("MainActivity", "Todos los permisos concedidos")
            startLocationService()
        } else {
            Log.w("MainActivity", "Algunos permisos fueron denegados")
            showPermissionDeniedDialog()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        Log.d("MainActivity", "MainActivity creada")

        // Configurar WebView
        setupWebView()

        // Verificar y solicitar permisos
        checkAndRequestPermissions()
    }

    /**
     * Configura el WebView con todas las opciones necesarias
     */
    private fun setupWebView() {
        webView = findViewById(R.id.webView)

        // Configurar WebSettings
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            databaseEnabled = true
            allowFileAccess = true
            allowContentAccess = true
            setSupportZoom(true)
            builtInZoomControls = true
            displayZoomControls = false
            loadWithOverviewMode = true
            useWideViewPort = true
            
            // Configuraciones adicionales para mejor compatibilidad
            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            allowUniversalAccessFromFileURLs = true
            allowFileAccessFromFileURLs = true
            
            // Configurar User Agent para mejor compatibilidad
            userAgentString = userAgentString + " OperaApp/1.0"
        }

        // Configurar WebViewClient
        webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                Log.d("MainActivity", "Navegando a: $url")
                return false // Permitir navegación normal
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                Log.d("MainActivity", "Página cargada: $url")
            }

            override fun onReceivedError(view: WebView?, errorCode: Int, description: String?, failingUrl: String?) {
                super.onReceivedError(view, errorCode, description, failingUrl)
                Log.e("MainActivity", "Error cargando página: $description")
            }
        }

        // Agregar interfaz JavaScript
        webView.addJavascriptInterface(WebAppInterface(this), "AndroidInterface")
        Log.d("MainActivity", "JavaScript Interface 'AndroidInterface' registrado correctamente")

        // Cargar la URL del simulador
        val url = "https://patagoniaservers.com.ar:5005/_simulate/"
        Log.d("MainActivity", "Cargando URL: $url")
        webView.loadUrl(url)
    }

    /**
     * Verifica y solicita los permisos necesarios
     */
    private fun checkAndRequestPermissions() {
        val missingPermissions = requiredPermissions.filter { permission ->
            ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED
        }

        if (missingPermissions.isEmpty()) {
            Log.d("MainActivity", "Todos los permisos ya están concedidos")
            startLocationService()
        } else {
            Log.d("MainActivity", "Solicitando permisos: ${missingPermissions.joinToString()}")
            permissionLauncher.launch(missingPermissions.toTypedArray())
        }
    }

    /**
     * Muestra un diálogo cuando los permisos son denegados
     */
    private fun showPermissionDeniedDialog() {
        AlertDialog.Builder(this)
            .setTitle("Permisos Requeridos")
            .setMessage("Esta aplicación necesita permisos de ubicación para funcionar correctamente. " +
                    "Por favor, concede los permisos en la configuración de la aplicación.")
            .setPositiveButton("Ir a Configuración") { _, _ ->
                openAppSettings()
            }
            .setNegativeButton("Cancelar") { dialog, _ ->
                dialog.dismiss()
                Toast.makeText(this, "La aplicación no funcionará sin permisos de ubicación", Toast.LENGTH_LONG).show()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * Abre la configuración de la aplicación
     */
    private fun openAppSettings() {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", packageName, null)
        }
        startActivity(intent)
    }

    /**
     * Inicia el servicio de seguimiento de ubicación
     */
    private fun startLocationService() {
        Log.d("MainActivity", "Iniciando servicio de seguimiento de ubicación")
        
        val serviceIntent = Intent(this, LocationTrackingService::class.java)
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(serviceIntent)
        } else {
            startService(serviceIntent)
        }
        
        Toast.makeText(this, "Servicio de ubicación iniciado", Toast.LENGTH_SHORT).show()
    }

    /**
     * Maneja el botón de retroceso
     */
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }

    /**
     * Maneja el resultado de solicitudes de permisos cuando la actividad se reanuda
     */
    override fun onResume() {
        super.onResume()
        
        // Verificar si los permisos fueron concedidos mientras la app estaba en segundo plano
        val allPermissionsGranted = requiredPermissions.all { permission ->
            ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
        }
        
        if (allPermissionsGranted) {
            startLocationService()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d("MainActivity", "MainActivity destruida")
    }

    /**
     * Guarda el token de autenticación en SharedPreferences
     */
    fun saveAuthToken(token: String) {
        try {
            val sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            with(sharedPreferences.edit()) {
                putString(KEY_ACCESS_TOKEN, token)
                apply()
            }
            Log.d("MainActivity", "Token guardado exitosamente en SharedPreferences")
            Toast.makeText(this, "Autenticación exitosa", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e("MainActivity", "Error guardando token en SharedPreferences", e)
        }
    }

    /**
     * Guarda las credenciales del usuario en SharedPreferences
     */
    fun saveUserCredentials(folio: String, password: String) {
        try {
            val sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            with(sharedPreferences.edit()) {
                putString(KEY_USER_FOLIO, folio)
                putString(KEY_USER_PASSWORD, password)
                apply()
            }
            Log.d("MainActivity", "Credenciales de usuario guardadas exitosamente en SharedPreferences")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error guardando credenciales en SharedPreferences", e)
        }
    }
}

/**
 * Interfaz JavaScript para comunicación entre WebView y Android
 */
class WebAppInterface(private val context: MainActivity) {

    /**
     * Método llamado desde JavaScript cuando el login es exitoso
     */
    @JavascriptInterface
    fun onLoginSuccess(token: String) {
        Log.d("WebAppInterface", "Token recibido desde JavaScript: ${token.take(50)}...")
        
        // Ejecutar en el hilo principal de UI
        context.runOnUiThread {
            context.saveAuthToken(token)
        }
    }

    /**
     * Método llamado desde JavaScript para guardar las credenciales del usuario
     */
    @JavascriptInterface
    fun saveCredentials(folio: String, password: String) {
        Log.d("WebAppInterface", "Credenciales recibidas desde JavaScript: folio=$folio")
        
        // Ejecutar en el hilo principal de UI
        context.runOnUiThread {
            context.saveUserCredentials(folio, password)
        }
    }

    /**
     * Método para mostrar mensajes desde JavaScript
     */
    @JavascriptInterface
    fun showToast(message: String) {
        context.runOnUiThread {
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
        }
    }
}
