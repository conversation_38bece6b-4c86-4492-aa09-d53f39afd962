# app/__init__.py

import os
import logging
from flask import Flask, redirect, url_for
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_marshmallow import Marshmallow
from flask_jwt_extended import J<PERSON>TManager
from flask_cors import CORS
from datetime import datetime
from flask_socketio import SocketIO # CORRECTO

# Importar configuración
from app.config import config

# Inicializar extensiones globalmente (SIN .init_app() aquí)
db = SQLAlchemy()
migrate = Migrate()
ma = Marshmallow()
jwt = JWTManager()
socketio = SocketIO() # CORRECTO: Crear instancia globalmente

# --- QUITAR LAS LÍNEAS INCORRECTAS DE AQUÍ ---
# socketio.init_app(app, async_mode='eventlet', cors_allowed_origins=socketio_cors_allowed_origins) # INCORRECTO aquí
# app.logger.info(f"SocketIO inicializado...") # INCORRECTO aquí
# from flask_socketio import socketio_cors_allowed_origins # INCORRECTO: Quitar esta línea

def create_app(config_name=None):
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'default')
    if config_name not in config:
        print(f"Advertencia: Configuración '{config_name}' no encontrada, usando 'default'.")
        config_name = 'default'

    app = Flask(__name__)
    app.config.from_object(config[config_name])
    logging.basicConfig(level=logging.INFO if not app.config.get('DEBUG') else logging.DEBUG)
    app.logger.info(f"Aplicación creada con configuración: {config_name}")

    # Definir variable para CORS de SocketIO *dentro* de la función
    socketio_cors_allowed_origins_config = None # Usar None como default

    # CORREGIDO: Usar 'in' para chequear múltiples valores o solo '==' para uno
    if config_name in ['development', 'default']: # Asume que 'default' se comporta como 'development' para CORS
        CORS(app, resources={r"/api/*": {"origins": "*"}}) # CORS para API HTTP
        app.logger.info("CORS (API) habilitado para '*' (modos development/default).")
        socketio_cors_allowed_origins_config = "*" # Permitir todos para SocketIO en dev/default
    else: # Producción o cualquier otro entorno
        # ¡IMPORTANTE! Debes definir los orígenes permitidos para producción.
        # Puedes hacerlo en tu clase ProductionConfig o directamente aquí.
        # Ejemplo: allowed_origins = ["https://patagoniaservers.com.ar", "https://otro.dominio.com"]
        allowed_origins = app.config.get('ALLOWED_ORIGINS', ["https://patagoniaservers.com.ar"]) # Obtener de config o usar default
        if not allowed_origins:
             app.logger.warning("ALLOWED_ORIGINS no está configurado para producción. CORS podría bloquear conexiones.")

        CORS(app, resources={r"/api/*": {"origins": allowed_origins}}) # CORS para API HTTP
        app.logger.info(f"CORS (API) habilitado para orígenes: {allowed_origins}")
        socketio_cors_allowed_origins_config = allowed_origins # Usar la misma lista para SocketIO en prod

    # Inicializar extensiones CON la app *DENTRO* de la función
    db.init_app(app)
    migrate.init_app(app, db)
    ma.init_app(app)
    jwt.init_app(app)

    # CORRECTO: Inicializar SocketIO aquí, pasando la variable de CORS definida arriba
    socketio.init_app(app, async_mode='eventlet', cors_allowed_origins=socketio_cors_allowed_origins_config)
    app.logger.info(f"SocketIO inicializado con async_mode='eventlet' y CORS origins: {socketio_cors_allowed_origins_config}")

    # Importar modelos dentro del contexto de la app
    with app.app_context():
        from . import models
        app.logger.debug("Modelos importados y registrados con SQLAlchemy.")

    # Registrar Blueprints (sin cambios aparentes necesarios aquí)
    from .routes.auth import auth_bp
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    from .routes.novelties import novelties_bp
    app.register_blueprint(novelties_bp, url_prefix='/api/novelties')
    from .routes.shifts import shifts_bp
    app.register_blueprint(shifts_bp, url_prefix='/api/shifts')
    from .routes.identification import identification_bp
    app.register_blueprint(identification_bp, url_prefix='/api/identifications')
    from .routes.background_check import background_check_bp
    app.register_blueprint(background_check_bp, url_prefix='/api/background-check')
    from .routes.dashboard import dashboard_web_bp, dashboard_api_bp
    app.register_blueprint(dashboard_web_bp)  # /dashboard
    app.register_blueprint(dashboard_api_bp)  # /api/dashboard
    from .routes.live_data import live_data_bp
    app.register_blueprint(live_data_bp) # Se registra después de los otros blueprints
    from .routes.messages import messages_bp
    app.register_blueprint(messages_bp, url_prefix='/api/messages')

    # --- Bloque de simulación (Corregida indentación y ubicación) ---
    is_dev_or_testing = app.config.get('DEBUG') or app.config.get('TESTING')
    if is_dev_or_testing:
        try:
            # En desarrollo/testing registramos el simulador
            from .routes.simulation import simulation_bp
            # CORREGIDO: El prefijo original era /_simulate, mantenlo si es intencional
            # o cambia a /simulate si lo prefieres. Usaré /_simulate por convención.
            app.register_blueprint(simulation_bp, url_prefix='/_simulate')
            app.logger.warning("!!! Blueprint de SIMULACIÓN cargado (/_simulate). NO USAR EN PRODUCCIÓN !!!")
        except ImportError:
             app.logger.warning("Blueprint de simulación no encontrado, omitiendo.")
    # No es necesario un 'else: pass' aquí

    # NUEVO Y CORRECTO: Importar los manejadores de SocketIO *DESPUÉS* de socketio.init_app()
    # Esto asegura que los decoradores @socketio.on se registren correctamente.
    from . import sockets
    app.logger.debug("Manejadores de SocketIO importados.")


    # --- Context processor y rutas raíz (sin cambios) ---
    @app.context_processor
    def inject_now():
        return {'now': datetime.utcnow}

    @app.route('/')
    def index_redirect():
        return redirect(url_for('dashboard.login_view'))

    @app.route('/hello')
    def hello():
        return 'Hello, World from OPERA App!'

    return app  # Fin de create_app()