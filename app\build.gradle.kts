plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
}

android {
    namespace = "com.example.opera"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.example.opera"
        minSdk = 24
        targetSdk = 35
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false // Considera habilitarlo (true) para producción con reglas ProGuard adecuadas
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        // Para Java 11 (si usas código Java)
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11" // Asegúrate que coincida con tu JDK y compileOptions
    }
    buildFeatures {
        viewBinding = true // Habilitar ViewBinding
        // dataBinding = true // Descomenta si prefieres DataBinding sobre ViewBinding
    }
}

dependencies {
    // --- Dependencias base de AndroidX y Material Design ---
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material) // Material Design Components
    implementation(libs.androidx.activity) // Para `by viewModels()` y manejo de Activity
    implementation(libs.androidx.constraintlayout) // Para ConstraintLayout

    // --- WebView para cargar la página web ---
    // No necesitamos dependencias adicionales para WebView básico

    // --- Servicios de Ubicación: Google Play Services Location ---
    implementation("com.google.android.gms:play-services-location:21.2.0")

    // --- Networking: Retrofit y Gson ---
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")
    implementation("com.google.code.gson:gson:2.10.1")

    // --- Pruebas ---
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}