// Generated by view binder compiler. Do not edit!
package com.example.opera.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.opera.R;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityLoginBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnLogin;

  @NonNull
  public final TextInputEditText etFolio;

  @NonNull
  public final TextInputEditText etPassword;

  @NonNull
  public final ImageView ivLogo;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextInputLayout tilFolio;

  @NonNull
  public final TextInputLayout tilPassword;

  @NonNull
  public final TextView tvAppName;

  private ActivityLoginBinding(@NonNull ConstraintLayout rootView, @NonNull Button btnLogin,
      @NonNull TextInputEditText etFolio, @NonNull TextInputEditText etPassword,
      @NonNull ImageView ivLogo, @NonNull ProgressBar progressBar,
      @NonNull TextInputLayout tilFolio, @NonNull TextInputLayout tilPassword,
      @NonNull TextView tvAppName) {
    this.rootView = rootView;
    this.btnLogin = btnLogin;
    this.etFolio = etFolio;
    this.etPassword = etPassword;
    this.ivLogo = ivLogo;
    this.progressBar = progressBar;
    this.tilFolio = tilFolio;
    this.tilPassword = tilPassword;
    this.tvAppName = tvAppName;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnLogin;
      Button btnLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnLogin == null) {
        break missingId;
      }

      id = R.id.etFolio;
      TextInputEditText etFolio = ViewBindings.findChildViewById(rootView, id);
      if (etFolio == null) {
        break missingId;
      }

      id = R.id.etPassword;
      TextInputEditText etPassword = ViewBindings.findChildViewById(rootView, id);
      if (etPassword == null) {
        break missingId;
      }

      id = R.id.ivLogo;
      ImageView ivLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivLogo == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tilFolio;
      TextInputLayout tilFolio = ViewBindings.findChildViewById(rootView, id);
      if (tilFolio == null) {
        break missingId;
      }

      id = R.id.tilPassword;
      TextInputLayout tilPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilPassword == null) {
        break missingId;
      }

      id = R.id.tvAppName;
      TextView tvAppName = ViewBindings.findChildViewById(rootView, id);
      if (tvAppName == null) {
        break missingId;
      }

      return new ActivityLoginBinding((ConstraintLayout) rootView, btnLogin, etFolio, etPassword,
          ivLogo, progressBar, tilFolio, tilPassword, tvAppName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
