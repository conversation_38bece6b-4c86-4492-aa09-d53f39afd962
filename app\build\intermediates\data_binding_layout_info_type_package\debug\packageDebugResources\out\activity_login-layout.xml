<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.example.opera" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_login_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="94" endOffset="51"/></Target><Target id="@+id/ivLogo" view="ImageView"><Expressions/><location startLine="9" startOffset="4" endLine="18" endOffset="51"/></Target><Target id="@+id/tvAppName" view="TextView"><Expressions/><location startLine="20" startOffset="4" endLine="30" endOffset="59"/></Target><Target id="@+id/tilFolio" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="32" startOffset="4" endLine="49" endOffset="59"/></Target><Target id="@+id/etFolio" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="43" startOffset="8" endLine="48" endOffset="34"/></Target><Target id="@+id/tilPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="51" startOffset="4" endLine="69" endOffset="59"/></Target><Target id="@+id/etPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="63" startOffset="8" endLine="68" endOffset="34"/></Target><Target id="@+id/btnLogin" view="Button"><Expressions/><location startLine="71" startOffset="4" endLine="81" endOffset="64"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="83" startOffset="4" endLine="92" endOffset="61"/></Target></Targets></Layout>