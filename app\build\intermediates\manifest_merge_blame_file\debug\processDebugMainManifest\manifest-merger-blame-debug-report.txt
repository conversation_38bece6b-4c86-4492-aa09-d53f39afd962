1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.opera"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permisos de Internet: Fundamental para todas las comunicaciones de red -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:6:22-64
13
14    <!-- Permisos de Ubicación: Necesarios para el seguimiento GPS -->
15    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
15-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:9:5-79
15-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:9:22-76
16    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
16-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:10:5-81
16-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:10:22-78
17    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
17-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:11:5-85
17-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:11:22-82
18
19    <!-- Permiso específico para servicio de ubicación en primer plano -->
20    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
20-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:14:5-86
20-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:14:22-83
21
22    <!-- Permiso de Cámara: Para escanear códigos QR y tomar fotos -->
23    <uses-permission android:name="android.permission.CAMERA" />
23-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:17:5-65
23-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:17:22-62
24
25    <!-- Permisos para mantener la aplicación activa en segundo plano -->
26    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
26-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:20:5-77
26-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:20:22-74
27    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
27-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:21:5-89
27-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:21:22-86
28    <uses-permission android:name="android.permission.WAKE_LOCK" />
28-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:22:5-68
28-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:22:22-65
29
30    <!-- Permiso para Notificaciones (Necesario para ForegroundService en Android 13+) -->
31    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
31-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:25:5-76
31-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:25:22-74
32
33    <!-- Permisos de Bluetooth (para evitar advertencias) -->
34    <uses-permission android:name="android.permission.BLUETOOTH" />
34-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:28:5-68
34-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:28:22-65
35    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
35-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:29:5-76
35-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:29:22-73
36
37    <!-- Características de hardware (opcional pero recomendado para filtrar en Play Store) -->
38    <uses-feature
38-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:32:5-90
39        android:name="android.hardware.location.gps"
39-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:32:19-63
40        android:required="true" />
40-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:32:64-87
41    <uses-feature
41-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:33:5-84
42        android:name="android.hardware.camera"
42-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:33:19-57
43        android:required="true" />
43-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:33:58-81
44
45    <permission
45-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
46        android:name="com.example.opera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
46-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
47        android:protectionLevel="signature" />
47-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
48
49    <uses-permission android:name="com.example.opera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
49-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
49-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
50
51    <application
51-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:35:5-75:19
52        android:allowBackup="true"
52-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:36:9-35
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
54        android:dataExtractionRules="@xml/data_extraction_rules"
54-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:37:9-65
55        android:debuggable="true"
56        android:extractNativeLibs="false"
57        android:fullBackupContent="@xml/backup_rules"
57-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:38:9-54
58        android:icon="@mipmap/ic_launcher"
58-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:39:9-43
59        android:label="@string/app_name"
59-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:40:9-41
60        android:networkSecurityConfig="@xml/network_security_config"
60-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:45:9-69
61        android:roundIcon="@mipmap/ic_launcher_round"
61-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:41:9-54
62        android:supportsRtl="true"
62-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:42:9-35
63        android:theme="@style/Theme.Opera"
63-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:43:9-43
64        android:usesCleartextTraffic="true" >
64-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:46:9-44
65
66        <!-- usesCleartextTraffic permite conexiones HTTP, pero usamos HTTPS para producción -->
67
68
69        <!-- Actividad Principal (punto de entrada) -->
70        <activity
70-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:50:9-58:20
71            android:name="com.example.opera.MainActivity"
71-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:51:13-41
72            android:configChanges="orientation|screenSize|keyboardHidden"
72-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:53:13-74
73            android:exported="true" >
73-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:52:13-36
74            <intent-filter>
74-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:54:13-57:29
75                <action android:name="android.intent.action.MAIN" />
75-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:55:17-69
75-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:55:25-66
76
77                <category android:name="android.intent.category.LAUNCHER" />
77-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:56:17-77
77-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:56:27-74
78            </intent-filter>
79        </activity>
80
81        <!-- Servicio para mantener la aplicación activa en segundo plano -->
82        <service
82-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:61:9-67:19
83            android:name="com.example.opera.BackgroundService"
83-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:62:13-46
84            android:exported="false"
84-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:63:13-37
85            android:foregroundServiceType="specialUse" >
85-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:64:13-55
86            <property
86-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:65:13-66:51
87                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
87-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:65:23-82
88                android:value="keepAlive" />
88-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:66:23-48
89        </service>
90
91        <!-- Servicio de seguimiento GPS mejorado -->
92        <service
92-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:70:9-73:56
93            android:name="com.example.opera.LocationTrackingService"
93-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:71:13-52
94            android:exported="false"
94-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:72:13-37
95            android:foregroundServiceType="location" />
95-->C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:73:13-53
96
97        <activity
97-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c1725a107cd988a0012d2ca89c3be54\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
98            android:name="com.google.android.gms.common.api.GoogleApiActivity"
98-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c1725a107cd988a0012d2ca89c3be54\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:19-85
99            android:exported="false"
99-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c1725a107cd988a0012d2ca89c3be54\transformed\play-services-base-18.3.0\AndroidManifest.xml:22:19-43
100            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
100-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c1725a107cd988a0012d2ca89c3be54\transformed\play-services-base-18.3.0\AndroidManifest.xml:21:19-78
101
102        <meta-data
102-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea16d2272a061f1e000c22cd3a27cb04\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
103            android:name="com.google.android.gms.version"
103-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea16d2272a061f1e000c22cd3a27cb04\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
104            android:value="@integer/google_play_services_version" />
104-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea16d2272a061f1e000c22cd3a27cb04\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
105
106        <provider
106-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
107            android:name="androidx.startup.InitializationProvider"
107-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
108            android:authorities="com.example.opera.androidx-startup"
108-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
109            android:exported="false" >
109-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
110            <meta-data
110-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
111                android:name="androidx.emoji2.text.EmojiCompatInitializer"
111-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
112                android:value="androidx.startup" />
112-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
113            <meta-data
113-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a0a494460687dc2f027abe1f8262dc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
114                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
114-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a0a494460687dc2f027abe1f8262dc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
115                android:value="androidx.startup" />
115-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a0a494460687dc2f027abe1f8262dc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
116            <meta-data
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
117                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
117-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
118                android:value="androidx.startup" />
118-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
119        </provider>
120
121        <receiver
121-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
122            android:name="androidx.profileinstaller.ProfileInstallReceiver"
122-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
123            android:directBootAware="false"
123-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
124            android:enabled="true"
124-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
125            android:exported="true"
125-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
126            android:permission="android.permission.DUMP" >
126-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
128                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
129            </intent-filter>
130            <intent-filter>
130-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
131                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
131-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
131-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
132            </intent-filter>
133            <intent-filter>
133-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
134                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
134-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
134-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
135            </intent-filter>
136            <intent-filter>
136-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
137                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
137-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
137-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
138            </intent-filter>
139        </receiver>
140    </application>
141
142</manifest>
