{"logs": [{"outputFile": "com.example.opera.app-mergeDebugResources-33:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\643bf35482294389b559adcaf5700b1d\\transformed\\material-1.12.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,360,440,526,631,727,829,957,1038,1100,1165,1260,1330,1393,1486,1550,1622,1685,1759,1823,1879,1997,2055,2117,2173,2253,2387,2476,2552,2650,2731,2812,2953,3034,3114,3265,3355,3432,3488,3544,3610,3689,3771,3842,3931,4004,4081,4151,4228,4334,4423,4497,4591,4693,4765,4846,4950,5003,5088,5155,5248,5337,5399,5463,5526,5594,5705,5816,5918,6023,6083,6143,6226,6309,6385", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,79,85,104,95,101,127,80,61,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,75,97,80,80,140,80,79,150,89,76,55,55,65,78,81,70,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82,82,75,76", "endOffsets": "273,355,435,521,626,722,824,952,1033,1095,1160,1255,1325,1388,1481,1545,1617,1680,1754,1818,1874,1992,2050,2112,2168,2248,2382,2471,2547,2645,2726,2807,2948,3029,3109,3260,3350,3427,3483,3539,3605,3684,3766,3837,3926,3999,4076,4146,4223,4329,4418,4492,4586,4688,4760,4841,4945,4998,5083,5150,5243,5332,5394,5458,5521,5589,5700,5811,5913,6018,6078,6138,6221,6304,6380,6457"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3044,3126,3206,3292,3397,4225,4327,4455,6865,6927,6992,7087,7157,7220,7313,7377,7449,7512,7586,7650,7706,7824,7882,7944,8000,8080,8214,8303,8379,8477,8558,8639,8780,8861,8941,9092,9182,9259,9315,9371,9437,9516,9598,9669,9758,9831,9908,9978,10055,10161,10250,10324,10418,10520,10592,10673,10777,10830,10915,10982,11075,11164,11226,11290,11353,11421,11532,11643,11745,11850,11910,11970,12136,12219,12295", "endLines": "5,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,131,132,133", "endColumns": "12,81,79,85,104,95,101,127,80,61,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,75,97,80,80,140,80,79,150,89,76,55,55,65,78,81,70,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82,82,75,76", "endOffsets": "323,3121,3201,3287,3392,3488,4322,4450,4531,6922,6987,7082,7152,7215,7308,7372,7444,7507,7581,7645,7701,7819,7877,7939,7995,8075,8209,8298,8374,8472,8553,8634,8775,8856,8936,9087,9177,9254,9310,9366,9432,9511,9593,9664,9753,9826,9903,9973,10050,10156,10245,10319,10413,10515,10587,10668,10772,10825,10910,10977,11070,11159,11221,11285,11348,11416,11527,11638,11740,11845,11905,11965,12048,12214,12290,12367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\74379aaf95c75674bc5035ef49ea8a78\\transformed\\appcompat-1.7.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,448,557,665,750,852,968,1053,1133,1224,1317,1412,1506,1605,1698,1797,1893,1984,2075,2157,2264,2363,2462,2570,2678,2785,2944,12053", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "443,552,660,745,847,963,1048,1128,1219,1312,1407,1501,1600,1693,1792,1888,1979,2070,2152,2259,2358,2457,2565,2673,2780,2939,3039,12131"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\97c6cb53daeb20508ef2213053da0e6d\\transformed\\core-1.16.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,134", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3493,3592,3694,3794,3892,3999,4105,12372", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3587,3689,3789,3887,3994,4100,4220,12468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8c1725a107cd988a0012d2ca89c3be54\\transformed\\play-services-base-18.3.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,301,463,591,695,852,979,1098,1200,1367,1472,1638,1767,1940,2114,2180,2238", "endColumns": "103,161,127,103,156,126,118,101,166,104,165,128,172,173,65,57,73", "endOffsets": "300,462,590,694,851,978,1097,1199,1366,1471,1637,1766,1939,2113,2179,2237,2311"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4536,4644,4810,4942,5050,5211,5342,5465,5717,5888,5997,6167,6300,6477,6655,6725,6787", "endColumns": "107,165,131,107,160,130,122,105,170,108,169,132,176,177,69,61,77", "endOffsets": "4639,4805,4937,5045,5206,5337,5460,5566,5883,5992,6162,6295,6472,6650,6720,6782,6860"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ea16d2272a061f1e000c22cd3a27cb04\\transformed\\play-services-basement-18.3.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "141", "endOffsets": "340"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5571", "endColumns": "145", "endOffsets": "5712"}}]}]}