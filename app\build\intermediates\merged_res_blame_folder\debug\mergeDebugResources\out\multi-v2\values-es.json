{"logs": [{"outputFile": "com.example.opera.app-mergeDebugResources-33:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ea16d2272a061f1e000c22cd3a27cb04\\transformed\\play-services-basement-18.3.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5578", "endColumns": "159", "endOffsets": "5733"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\97c6cb53daeb20508ef2213053da0e6d\\transformed\\core-1.16.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,134", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3503,3602,3704,3804,3902,4009,4115,12392", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3597,3699,3799,3897,4004,4110,4230,12488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\74379aaf95c75674bc5035ef49ea8a78\\transformed\\appcompat-1.7.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,12073", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,12151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\643bf35482294389b559adcaf5700b1d\\transformed\\material-1.12.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1091,1156,1251,1332,1395,1484,1548,1617,1680,1754,1818,1875,1993,2051,2113,2170,2250,2389,2478,2554,2649,2730,2812,2953,3034,3114,3265,3355,3435,3491,3547,3613,3692,3774,3845,3934,4008,4085,4155,4234,4334,4418,4502,4594,4694,4768,4849,4951,5004,5089,5156,5249,5338,5400,5464,5527,5595,5708,5815,5919,6020,6080,6140,6223,6306,6382", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "273,354,433,520,621,717,821,943,1024,1086,1151,1246,1327,1390,1479,1543,1612,1675,1749,1813,1870,1988,2046,2108,2165,2245,2384,2473,2549,2644,2725,2807,2948,3029,3109,3260,3350,3430,3486,3542,3608,3687,3769,3840,3929,4003,4080,4150,4229,4329,4413,4497,4589,4689,4763,4844,4946,4999,5084,5151,5244,5333,5395,5459,5522,5590,5703,5810,5914,6015,6075,6135,6218,6301,6377,6454"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3059,3140,3219,3306,3407,4235,4339,4461,6879,6941,7006,7101,7182,7245,7334,7398,7467,7530,7604,7668,7725,7843,7901,7963,8020,8100,8239,8328,8404,8499,8580,8662,8803,8884,8964,9115,9205,9285,9341,9397,9463,9542,9624,9695,9784,9858,9935,10005,10084,10184,10268,10352,10444,10544,10618,10699,10801,10854,10939,11006,11099,11188,11250,11314,11377,11445,11558,11665,11769,11870,11930,11990,12156,12239,12315", "endLines": "5,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,131,132,133", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "323,3135,3214,3301,3402,3498,4334,4456,4537,6936,7001,7096,7177,7240,7329,7393,7462,7525,7599,7663,7720,7838,7896,7958,8015,8095,8234,8323,8399,8494,8575,8657,8798,8879,8959,9110,9200,9280,9336,9392,9458,9537,9619,9690,9779,9853,9930,10000,10079,10179,10263,10347,10439,10539,10613,10694,10796,10849,10934,11001,11094,11183,11245,11309,11372,11440,11553,11660,11764,11865,11925,11985,12068,12234,12310,12387"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8c1725a107cd988a0012d2ca89c3be54\\transformed\\play-services-base-18.3.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4542,4650,4813,4944,5052,5213,5346,5468,5738,5930,6039,6204,6336,6501,6658,6725,6794", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "4645,4808,4939,5047,5208,5341,5463,5573,5925,6034,6199,6331,6496,6653,6720,6789,6874"}}]}]}