{"logs": [{"outputFile": "com.example.opera.app-mergeDebugResources-33:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ea16d2272a061f1e000c22cd3a27cb04\\transformed\\play-services-basement-18.3.0\\res\\values-uk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5629", "endColumns": "145", "endOffsets": "5770"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\97c6cb53daeb20508ef2213053da0e6d\\transformed\\core-1.16.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "40,41,42,43,44,45,46,136", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3596,3696,3798,3899,4000,4105,4210,12245", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "3691,3793,3894,3995,4100,4205,4318,12341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\643bf35482294389b559adcaf5700b1d\\transformed\\material-1.12.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,374,452,530,618,726,817,913,1029,1112,1184,1251,1342,1408,1471,1559,1621,1688,1746,1817,1876,1930,2044,2104,2167,2221,2294,2413,2499,2575,2666,2747,2830,2969,3054,3141,3274,3362,3440,3497,3548,3614,3686,3762,3833,3916,3989,4066,4148,4222,4331,4421,4500,4591,4687,4761,4842,4937,4991,5073,5139,5226,5312,5374,5438,5501,5574,5681,5791,5889,5995,6056,6111,6193,6278,6354", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,87,107,90,95,115,82,71,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,132,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81,84,75,76", "endOffsets": "369,447,525,613,721,812,908,1024,1107,1179,1246,1337,1403,1466,1554,1616,1683,1741,1812,1871,1925,2039,2099,2162,2216,2289,2408,2494,2570,2661,2742,2825,2964,3049,3136,3269,3357,3435,3492,3543,3609,3681,3757,3828,3911,3984,4061,4143,4217,4326,4416,4495,4586,4682,4756,4837,4932,4986,5068,5134,5221,5307,5369,5433,5496,5569,5676,5786,5884,5990,6051,6106,6188,6273,6349,6426"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3153,3231,3309,3397,3505,4323,4419,4535,6844,6916,6983,7074,7140,7203,7291,7353,7420,7478,7549,7608,7662,7776,7836,7899,7953,8026,8145,8231,8307,8398,8479,8562,8701,8786,8873,9006,9094,9172,9229,9280,9346,9418,9494,9565,9648,9721,9798,9880,9954,10063,10153,10232,10323,10419,10493,10574,10669,10723,10805,10871,10958,11044,11106,11170,11233,11306,11413,11523,11621,11727,11788,11843,12007,12092,12168", "endLines": "7,35,36,37,38,39,47,48,49,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,133,134,135", "endColumns": "12,77,77,87,107,90,95,115,82,71,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,132,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81,84,75,76", "endOffsets": "419,3226,3304,3392,3500,3591,4414,4530,4613,6911,6978,7069,7135,7198,7286,7348,7415,7473,7544,7603,7657,7771,7831,7894,7948,8021,8140,8226,8302,8393,8474,8557,8696,8781,8868,9001,9089,9167,9224,9275,9341,9413,9489,9560,9643,9716,9793,9875,9949,10058,10148,10227,10318,10414,10488,10569,10664,10718,10800,10866,10953,11039,11101,11165,11228,11301,11408,11518,11616,11722,11783,11838,11920,12087,12163,12240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\74379aaf95c75674bc5035ef49ea8a78\\transformed\\appcompat-1.7.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,816,898,989,1082,1177,1271,1371,1464,1559,1654,1745,1836,1935,2041,2147,2245,2352,2459,2564,2734,2834", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,811,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829,2911"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "424,533,635,743,829,934,1052,1135,1217,1308,1401,1496,1590,1690,1783,1878,1973,2064,2155,2254,2360,2466,2564,2671,2778,2883,3053,11925", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "528,630,738,824,929,1047,1130,1212,1303,1396,1491,1585,1685,1778,1873,1968,2059,2150,2249,2355,2461,2559,2666,2773,2878,3048,3148,12002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8c1725a107cd988a0012d2ca89c3be54\\transformed\\play-services-base-18.3.0\\res\\values-uk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,579,685,835,960,1071,1172,1336,1438,1596,1717,1860,1998,2064,2121", "endColumns": "103,158,122,105,149,124,110,100,163,101,157,120,142,137,65,56,83", "endOffsets": "296,455,578,684,834,959,1070,1171,1335,1437,1595,1716,1859,1997,2063,2120,2204"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4618,4726,4889,5016,5126,5280,5409,5524,5775,5943,6049,6211,6336,6483,6625,6695,6756", "endColumns": "107,162,126,109,153,128,114,104,167,105,161,124,146,141,69,60,87", "endOffsets": "4721,4884,5011,5121,5275,5404,5519,5624,5938,6044,6206,6331,6478,6620,6690,6751,6839"}}]}]}