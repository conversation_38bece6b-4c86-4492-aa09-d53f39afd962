<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Configuración para permitir tráfico HTTP en desarrollo -->
    <debug-overrides>
        <trust-anchors>
            <!-- Confiar en los certificados del sistema -->
            <certificates src="system" />
            <!-- Confiar en certificados autofirmados en modo debug -->
            <certificates src="user" />
        </trust-anchors>
    </debug-overrides>

    <!-- Configuración base para todas las conexiones -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system" />
        </trust-anchors>
    </base-config>

    <!-- Configuración específica para el servidor de producción -->
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">patagoniaservers.com.ar</domain>
        <trust-anchors>
            <certificates src="system" />
        </trust-anchors>
    </domain-config>
</network-security-config>
