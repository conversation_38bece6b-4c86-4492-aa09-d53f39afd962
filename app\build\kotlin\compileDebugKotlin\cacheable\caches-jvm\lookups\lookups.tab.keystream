  Manifest android  ACCESS_BAC<PERSON>GROUND_LOCATION android.Manifest.permission  ACCESS_COARSE_LOCATION android.Manifest.permission  ACCESS_FINE_LOCATION android.Manifest.permission  CAMERA android.Manifest.permission  POST_NOTIFICATIONS android.Manifest.permission  Build android.app  Context android.app  CoroutineScope android.app  Dispatchers android.app  	Exception android.app  IBinder android.app  Int android.app  Intent android.app  MainActivity android.app  NOTIFICATION_ID android.app  Notification android.app  NotificationChannel android.app  NotificationCompat android.app  NotificationManager android.app  
PendingIntent android.app  PowerManager android.app  R android.app  START_STICKY android.app  Service android.app  
SupervisorJob android.app  android android.app  apply android.app  cancel android.app  createNotification android.app  createNotificationChannel android.app  getSystemService android.app  java android.app  launch android.app  startForeground android.app  stopSelf android.app  wakeLock android.app  withContext android.app  ActivityResultContracts android.app.Activity  AlertDialog android.app.Activity  Boolean android.app.Activity  Build android.app.Activity  Context android.app.Activity  
ContextCompat android.app.Activity  	Exception android.app.Activity  Int android.app.Activity  Intent android.app.Activity  KEY_ACCESS_TOKEN android.app.Activity  KEY_USER_FOLIO android.app.Activity  KEY_USER_PASSWORD android.app.Activity  LocationTrackingService android.app.Activity  Log android.app.Activity  Manifest android.app.Activity  PackageManager android.app.Activity  R android.app.Activity  String android.app.Activity  Toast android.app.Activity  WebAppInterface android.app.Activity  WebSettings android.app.Activity  WebView android.app.Activity  
WebViewClient android.app.Activity  all android.app.Activity  apply android.app.Activity  arrayOf android.app.Activity  currentInstance android.app.Activity  filter android.app.Activity  isEmpty android.app.Activity  java android.app.Activity  joinToString android.app.Activity  
mutableListOf android.app.Activity  onCreate android.app.Activity  
runOnUiThread android.app.Activity  toTypedArray android.app.Activity  
trimIndent android.app.Activity  with android.app.Activity  apply android.app.NotificationChannel  description android.app.NotificationChannel  enableLights android.app.NotificationChannel  enableVibration android.app.NotificationChannel  setShowBadge android.app.NotificationChannel  IMPORTANCE_HIGH android.app.NotificationManager  IMPORTANCE_LOW android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  notify android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getActivity android.app.PendingIntent  WakeLock android.app.PowerManager  ActivityCompat android.app.Service  BATCH_SEND_INTERVAL android.app.Service  BufferedReader android.app.Service  Build android.app.Service  
CHANNEL_ID android.app.Service  CONNECTION_TIMEOUT android.app.Service  Context android.app.Service  CoroutineScope android.app.Service  Date android.app.Service  Dispatchers android.app.Service  	Exception android.app.Service  HttpURLConnection android.app.Service  InputStreamReader android.app.Service  Intent android.app.Service  
JSONObject android.app.Service  KEY_ACCESS_TOKEN android.app.Service  KEY_USER_FOLIO android.app.Service  KEY_USER_PASSWORD android.app.Service  Locale android.app.Service  Location android.app.Service  LocationDatabaseHelper android.app.Service  LocationListener android.app.Service  LocationManager android.app.Service  Log android.app.Service  Long android.app.Service  Looper android.app.Service  MAX_ACCURACY_METERS_ACCEPTABLE android.app.Service  MAX_ACCURACY_METERS_EXCELLENT android.app.Service  MAX_ACCURACY_METERS_GOOD android.app.Service  MAX_JUMP_DISTANCE_METERS android.app.Service  MAX_POINTS_PER_BATCH android.app.Service  
MAX_SPEED_KMH android.app.Service  MESSAGES_URL android.app.Service  MIN_DISTANCE_CHANGE android.app.Service  MIN_STABILITY_POINTS android.app.Service  MIN_TIME_BETWEEN_POINTS android.app.Service  MIN_TIME_BETWEEN_UPDATES android.app.Service  MainActivity android.app.Service  Manifest android.app.Service  Math android.app.Service  NOTIFICATION_CHECK_INTERVAL android.app.Service  NOTIFICATION_ID android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  OutputStreamWriter android.app.Service  
PREFS_NAME android.app.Service  PackageManager android.app.Service  Pair android.app.Service  
PendingIntent android.app.Service  PowerManager android.app.Service  R android.app.Service  READ_TIMEOUT android.app.Service  
SERVER_URL android.app.Service  SMOOTHING_FACTOR android.app.Service  START_STICKY android.app.Service  STATS_LOG_INTERVAL android.app.Service  SimpleDateFormat android.app.Service  String android.app.Service  
SupervisorJob android.app.Service  System android.app.Service  TAG android.app.Service  TimeZone android.app.Service  URL android.app.Service  android android.app.Service  apply android.app.Service  applySmoothingToLocation android.app.Service  average android.app.Service  buildString android.app.Service  cancel android.app.Service  checkForNotifications android.app.Service  chunked android.app.Service  createNotification android.app.Service  createNotificationChannel android.app.Service  delay android.app.Service  format android.app.Service  getSystemService android.app.Service  handleNewLocation android.app.Service  isActive android.app.Service  isLocationValid android.app.Service  
isNotEmpty android.app.Service  
isNullOrEmpty android.app.Service  java android.app.Service  lastValidLocation android.app.Service  lastValidTimestamp android.app.Service  launch android.app.Service  let android.app.Service  locationDatabaseHelper android.app.Service  locationManager android.app.Service  logFilteringStats android.app.Service  map android.app.Service  
mutableListOf android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  readText android.app.Service  restartLocationTracking android.app.Service  sendLocationToWebView android.app.Service  sendPendingLocations android.app.Service  smoothedLatitude android.app.Service  smoothedLongitude android.app.Service  startForeground android.app.Service  stopSelf android.app.Service  updateLocationAvailability android.app.Service  updateLocationFromService android.app.Service  wakeLock android.app.Service  withContext android.app.Service  	withIndex android.app.Service  
ComponentName android.content  
ContentValues android.content  Context android.content  Intent android.content  COLUMN_ACCURACY android.content.ContentValues  COLUMN_LATITUDE android.content.ContentValues  COLUMN_LONGITUDE android.content.ContentValues  COLUMN_RETRY_COUNT android.content.ContentValues  COLUMN_SENT android.content.ContentValues  COLUMN_TIMESTAMP android.content.ContentValues  apply android.content.ContentValues  formatTimestamp android.content.ContentValues  put android.content.ContentValues  ActivityCompat android.content.Context  ActivityResultContracts android.content.Context  AlertDialog android.content.Context  BATCH_SEND_INTERVAL android.content.Context  Boolean android.content.Context  BufferedReader android.content.Context  Build android.content.Context  
CHANNEL_ID android.content.Context  CONNECTION_TIMEOUT android.content.Context  Context android.content.Context  
ContextCompat android.content.Context  CoroutineScope android.content.Context  Date android.content.Context  Dispatchers android.content.Context  	Exception android.content.Context  HttpURLConnection android.content.Context  InputStreamReader android.content.Context  Int android.content.Context  Intent android.content.Context  
JSONObject android.content.Context  KEY_ACCESS_TOKEN android.content.Context  KEY_USER_FOLIO android.content.Context  KEY_USER_PASSWORD android.content.Context  LOCATION_SERVICE android.content.Context  Locale android.content.Context  Location android.content.Context  LocationDatabaseHelper android.content.Context  LocationListener android.content.Context  LocationManager android.content.Context  LocationTrackingService android.content.Context  Log android.content.Context  Long android.content.Context  Looper android.content.Context  MAX_ACCURACY_METERS_ACCEPTABLE android.content.Context  MAX_ACCURACY_METERS_EXCELLENT android.content.Context  MAX_ACCURACY_METERS_GOOD android.content.Context  MAX_JUMP_DISTANCE_METERS android.content.Context  MAX_POINTS_PER_BATCH android.content.Context  
MAX_SPEED_KMH android.content.Context  MESSAGES_URL android.content.Context  MIN_DISTANCE_CHANGE android.content.Context  MIN_STABILITY_POINTS android.content.Context  MIN_TIME_BETWEEN_POINTS android.content.Context  MIN_TIME_BETWEEN_UPDATES android.content.Context  MODE_PRIVATE android.content.Context  MainActivity android.content.Context  Manifest android.content.Context  Math android.content.Context  NOTIFICATION_CHECK_INTERVAL android.content.Context  NOTIFICATION_ID android.content.Context  NOTIFICATION_SERVICE android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  OutputStreamWriter android.content.Context  
POWER_SERVICE android.content.Context  
PREFS_NAME android.content.Context  PackageManager android.content.Context  Pair android.content.Context  
PendingIntent android.content.Context  PowerManager android.content.Context  R android.content.Context  READ_TIMEOUT android.content.Context  
SERVER_URL android.content.Context  SMOOTHING_FACTOR android.content.Context  START_STICKY android.content.Context  STATS_LOG_INTERVAL android.content.Context  SimpleDateFormat android.content.Context  String android.content.Context  
SupervisorJob android.content.Context  System android.content.Context  TAG android.content.Context  TimeZone android.content.Context  Toast android.content.Context  URL android.content.Context  WebAppInterface android.content.Context  WebSettings android.content.Context  WebView android.content.Context  
WebViewClient android.content.Context  all android.content.Context  android android.content.Context  apply android.content.Context  applySmoothingToLocation android.content.Context  arrayOf android.content.Context  average android.content.Context  buildString android.content.Context  cancel android.content.Context  checkForNotifications android.content.Context  chunked android.content.Context  createNotification android.content.Context  createNotificationChannel android.content.Context  currentInstance android.content.Context  delay android.content.Context  filter android.content.Context  format android.content.Context  getSystemService android.content.Context  handleNewLocation android.content.Context  isActive android.content.Context  isEmpty android.content.Context  isLocationValid android.content.Context  
isNotEmpty android.content.Context  
isNullOrEmpty android.content.Context  java android.content.Context  joinToString android.content.Context  lastValidLocation android.content.Context  lastValidTimestamp android.content.Context  launch android.content.Context  let android.content.Context  locationDatabaseHelper android.content.Context  locationManager android.content.Context  logFilteringStats android.content.Context  map android.content.Context  
mutableListOf android.content.Context  readText android.content.Context  restartLocationTracking android.content.Context  sendLocationToWebView android.content.Context  sendPendingLocations android.content.Context  smoothedLatitude android.content.Context  smoothedLongitude android.content.Context  startForeground android.content.Context  stopSelf android.content.Context  toTypedArray android.content.Context  
trimIndent android.content.Context  updateLocationAvailability android.content.Context  updateLocationFromService android.content.Context  wakeLock android.content.Context  with android.content.Context  withContext android.content.Context  	withIndex android.content.Context  ActivityCompat android.content.ContextWrapper  ActivityResultContracts android.content.ContextWrapper  AlertDialog android.content.ContextWrapper  BATCH_SEND_INTERVAL android.content.ContextWrapper  Boolean android.content.ContextWrapper  BufferedReader android.content.ContextWrapper  Build android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  CONNECTION_TIMEOUT android.content.ContextWrapper  Context android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  Date android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  	Exception android.content.ContextWrapper  HttpURLConnection android.content.ContextWrapper  InputStreamReader android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  
JSONObject android.content.ContextWrapper  KEY_ACCESS_TOKEN android.content.ContextWrapper  KEY_USER_FOLIO android.content.ContextWrapper  KEY_USER_PASSWORD android.content.ContextWrapper  Locale android.content.ContextWrapper  Location android.content.ContextWrapper  LocationDatabaseHelper android.content.ContextWrapper  LocationListener android.content.ContextWrapper  LocationManager android.content.ContextWrapper  LocationTrackingService android.content.ContextWrapper  Log android.content.ContextWrapper  Long android.content.ContextWrapper  Looper android.content.ContextWrapper  MAX_ACCURACY_METERS_ACCEPTABLE android.content.ContextWrapper  MAX_ACCURACY_METERS_EXCELLENT android.content.ContextWrapper  MAX_ACCURACY_METERS_GOOD android.content.ContextWrapper  MAX_JUMP_DISTANCE_METERS android.content.ContextWrapper  MAX_POINTS_PER_BATCH android.content.ContextWrapper  
MAX_SPEED_KMH android.content.ContextWrapper  MESSAGES_URL android.content.ContextWrapper  MIN_DISTANCE_CHANGE android.content.ContextWrapper  MIN_STABILITY_POINTS android.content.ContextWrapper  MIN_TIME_BETWEEN_POINTS android.content.ContextWrapper  MIN_TIME_BETWEEN_UPDATES android.content.ContextWrapper  MainActivity android.content.ContextWrapper  Manifest android.content.ContextWrapper  Math android.content.ContextWrapper  NOTIFICATION_CHECK_INTERVAL android.content.ContextWrapper  NOTIFICATION_ID android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  OutputStreamWriter android.content.ContextWrapper  
PREFS_NAME android.content.ContextWrapper  PackageManager android.content.ContextWrapper  Pair android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  PowerManager android.content.ContextWrapper  R android.content.ContextWrapper  READ_TIMEOUT android.content.ContextWrapper  
SERVER_URL android.content.ContextWrapper  SMOOTHING_FACTOR android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  STATS_LOG_INTERVAL android.content.ContextWrapper  SimpleDateFormat android.content.ContextWrapper  String android.content.ContextWrapper  
SupervisorJob android.content.ContextWrapper  System android.content.ContextWrapper  TAG android.content.ContextWrapper  TimeZone android.content.ContextWrapper  Toast android.content.ContextWrapper  URL android.content.ContextWrapper  WebAppInterface android.content.ContextWrapper  WebSettings android.content.ContextWrapper  WebView android.content.ContextWrapper  
WebViewClient android.content.ContextWrapper  all android.content.ContextWrapper  android android.content.ContextWrapper  apply android.content.ContextWrapper  applySmoothingToLocation android.content.ContextWrapper  arrayOf android.content.ContextWrapper  average android.content.ContextWrapper  buildString android.content.ContextWrapper  cancel android.content.ContextWrapper  checkForNotifications android.content.ContextWrapper  chunked android.content.ContextWrapper  createNotification android.content.ContextWrapper  createNotificationChannel android.content.ContextWrapper  currentInstance android.content.ContextWrapper  delay android.content.ContextWrapper  filter android.content.ContextWrapper  format android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  getSystemService android.content.ContextWrapper  handleNewLocation android.content.ContextWrapper  isActive android.content.ContextWrapper  isEmpty android.content.ContextWrapper  isLocationValid android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
isNullOrEmpty android.content.ContextWrapper  java android.content.ContextWrapper  joinToString android.content.ContextWrapper  lastValidLocation android.content.ContextWrapper  lastValidTimestamp android.content.ContextWrapper  launch android.content.ContextWrapper  let android.content.ContextWrapper  locationDatabaseHelper android.content.ContextWrapper  locationManager android.content.ContextWrapper  logFilteringStats android.content.ContextWrapper  map android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  readText android.content.ContextWrapper  restartLocationTracking android.content.ContextWrapper  sendLocationToWebView android.content.ContextWrapper  sendPendingLocations android.content.ContextWrapper  smoothedLatitude android.content.ContextWrapper  smoothedLongitude android.content.ContextWrapper  startForeground android.content.ContextWrapper  startForegroundService android.content.ContextWrapper  startService android.content.ContextWrapper  stopSelf android.content.ContextWrapper  toTypedArray android.content.ContextWrapper  
trimIndent android.content.ContextWrapper  updateLocationAvailability android.content.ContextWrapper  updateLocationFromService android.content.ContextWrapper  wakeLock android.content.ContextWrapper  with android.content.ContextWrapper  withContext android.content.ContextWrapper  	withIndex android.content.ContextWrapper  OnClickListener android.content.DialogInterface  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  FLAG_ACTIVITY_CLEAR_TOP android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  Intent android.content.Intent  apply android.content.Intent  flags android.content.Intent  edit !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Cursor android.database  getColumnIndexOrThrow android.database.Cursor  	getDouble android.database.Cursor  getFloat android.database.Cursor  getInt android.database.Cursor  getLong android.database.Cursor  	getString android.database.Cursor  isNull android.database.Cursor  moveToFirst android.database.Cursor  
moveToNext android.database.Cursor  use android.database.Cursor  SQLiteDatabase android.database.sqlite  SQLiteOpenHelper android.database.sqlite  delete &android.database.sqlite.SQLiteDatabase  execSQL &android.database.sqlite.SQLiteDatabase  insert &android.database.sqlite.SQLiteDatabase  query &android.database.sqlite.SQLiteDatabase  rawQuery &android.database.sqlite.SQLiteDatabase  update &android.database.sqlite.SQLiteDatabase  Any (android.database.sqlite.SQLiteOpenHelper  COLUMN_ACCURACY (android.database.sqlite.SQLiteOpenHelper  	COLUMN_ID (android.database.sqlite.SQLiteOpenHelper  COLUMN_LATITUDE (android.database.sqlite.SQLiteOpenHelper  COLUMN_LONGITUDE (android.database.sqlite.SQLiteOpenHelper  COLUMN_RETRY_COUNT (android.database.sqlite.SQLiteOpenHelper  COLUMN_SENT (android.database.sqlite.SQLiteOpenHelper  COLUMN_TIMESTAMP (android.database.sqlite.SQLiteOpenHelper  Calendar (android.database.sqlite.SQLiteOpenHelper  
ContentValues (android.database.sqlite.SQLiteOpenHelper  Date (android.database.sqlite.SQLiteOpenHelper  	Exception (android.database.sqlite.SQLiteOpenHelper  Locale (android.database.sqlite.SQLiteOpenHelper  
LocationPoint (android.database.sqlite.SQLiteOpenHelper  Log (android.database.sqlite.SQLiteOpenHelper  Map (android.database.sqlite.SQLiteOpenHelper  SimpleDateFormat (android.database.sqlite.SQLiteOpenHelper  String (android.database.sqlite.SQLiteOpenHelper  System (android.database.sqlite.SQLiteOpenHelper  TABLE_LOCATIONS (android.database.sqlite.SQLiteOpenHelper  TAG (android.database.sqlite.SQLiteOpenHelper  TimeZone (android.database.sqlite.SQLiteOpenHelper  apply (android.database.sqlite.SQLiteOpenHelper  arrayOf (android.database.sqlite.SQLiteOpenHelper  close (android.database.sqlite.SQLiteOpenHelper  formatTimestamp (android.database.sqlite.SQLiteOpenHelper  mapOf (android.database.sqlite.SQLiteOpenHelper  
mutableListOf (android.database.sqlite.SQLiteOpenHelper  readableDatabase (android.database.sqlite.SQLiteOpenHelper  to (android.database.sqlite.SQLiteOpenHelper  
trimIndent (android.database.sqlite.SQLiteOpenHelper  use (android.database.sqlite.SQLiteOpenHelper  writableDatabase (android.database.sqlite.SQLiteOpenHelper  Location android.location  LocationListener android.location  LocationManager android.location  accuracy android.location.Location  altitude android.location.Location  apply android.location.Location  bearing android.location.Location  hasAccuracy android.location.Location  hasAltitude android.location.Location  
hasBearing android.location.Location  hasSpeed android.location.Location  latitude android.location.Location  let android.location.Location  	longitude android.location.Location  provider android.location.Location  smoothedLatitude android.location.Location  smoothedLongitude android.location.Location  speed android.location.Location  time android.location.Location  let !android.location.LocationListener  GPS_PROVIDER  android.location.LocationManager  NETWORK_PROVIDER  android.location.LocationManager  isProviderEnabled  android.location.LocationManager  
removeUpdates  android.location.LocationManager  requestLocationUpdates  android.location.LocationManager  Uri android.net  Build 
android.os  Bundle 
android.os  IBinder 
android.os  Looper 
android.os  PowerManager 
android.os  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  
getMainLooper android.os.Looper  PARTIAL_WAKE_LOCK android.os.PowerManager  WakeLock android.os.PowerManager  newWakeLock android.os.PowerManager  acquire  android.os.PowerManager.WakeLock  release  android.os.PowerManager.WakeLock  Settings android.provider  Log android.util  d android.util.Log  e android.util.Log  i android.util.Log  w android.util.Log  ActivityResultContracts  android.view.ContextThemeWrapper  AlertDialog  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  KEY_ACCESS_TOKEN  android.view.ContextThemeWrapper  KEY_USER_FOLIO  android.view.ContextThemeWrapper  KEY_USER_PASSWORD  android.view.ContextThemeWrapper  LocationTrackingService  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  WebAppInterface  android.view.ContextThemeWrapper  WebSettings  android.view.ContextThemeWrapper  WebView  android.view.ContextThemeWrapper  
WebViewClient  android.view.ContextThemeWrapper  all  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  arrayOf  android.view.ContextThemeWrapper  currentInstance  android.view.ContextThemeWrapper  filter  android.view.ContextThemeWrapper  isEmpty  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  joinToString  android.view.ContextThemeWrapper  
mutableListOf  android.view.ContextThemeWrapper  toTypedArray  android.view.ContextThemeWrapper  
trimIndent  android.view.ContextThemeWrapper  with  android.view.ContextThemeWrapper  JavascriptInterface android.webkit  
ValueCallback android.webkit  WebSettings android.webkit  WebView android.webkit  
WebViewClient android.webkit  <SAM-CONSTRUCTOR> android.webkit.ValueCallback  MIXED_CONTENT_ALWAYS_ALLOW android.webkit.WebSettings  WebSettings android.webkit.WebSettings  allowContentAccess android.webkit.WebSettings  allowFileAccess android.webkit.WebSettings  allowFileAccessFromFileURLs android.webkit.WebSettings   allowUniversalAccessFromFileURLs android.webkit.WebSettings  apply android.webkit.WebSettings  builtInZoomControls android.webkit.WebSettings  databaseEnabled android.webkit.WebSettings  displayZoomControls android.webkit.WebSettings  domStorageEnabled android.webkit.WebSettings  javaScriptEnabled android.webkit.WebSettings  loadWithOverviewMode android.webkit.WebSettings  mixedContentMode android.webkit.WebSettings  setSupportZoom android.webkit.WebSettings  useWideViewPort android.webkit.WebSettings  userAgentString android.webkit.WebSettings  addJavascriptInterface android.webkit.WebView  	canGoBack android.webkit.WebView  evaluateJavascript android.webkit.WebView  goBack android.webkit.WebView  loadUrl android.webkit.WebView  settings android.webkit.WebView  
webViewClient android.webkit.WebView  Log android.webkit.WebViewClient  onPageFinished android.webkit.WebViewClient  onReceivedError android.webkit.WebViewClient  Toast android.widget  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  ActivityResultContracts #androidx.activity.ComponentActivity  AlertDialog #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  Context #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  Double #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  Float #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  KEY_ACCESS_TOKEN #androidx.activity.ComponentActivity  KEY_USER_FOLIO #androidx.activity.ComponentActivity  KEY_USER_PASSWORD #androidx.activity.ComponentActivity  LocationTrackingService #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  MainActivity #androidx.activity.ComponentActivity  Manifest #androidx.activity.ComponentActivity  PackageManager #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  WebAppInterface #androidx.activity.ComponentActivity  WebSettings #androidx.activity.ComponentActivity  WebView #androidx.activity.ComponentActivity  
WebViewClient #androidx.activity.ComponentActivity  all #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  arrayOf #androidx.activity.ComponentActivity  currentInstance #androidx.activity.ComponentActivity  filter #androidx.activity.ComponentActivity  isEmpty #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  joinToString #androidx.activity.ComponentActivity  
mutableListOf #androidx.activity.ComponentActivity  
onBackPressed #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  toTypedArray #androidx.activity.ComponentActivity  
trimIndent #androidx.activity.ComponentActivity  with #androidx.activity.ComponentActivity  ActivityResultContracts -androidx.activity.ComponentActivity.Companion  AlertDialog -androidx.activity.ComponentActivity.Companion  Build -androidx.activity.ComponentActivity.Companion  Context -androidx.activity.ComponentActivity.Companion  
ContextCompat -androidx.activity.ComponentActivity.Companion  Intent -androidx.activity.ComponentActivity.Companion  KEY_ACCESS_TOKEN -androidx.activity.ComponentActivity.Companion  KEY_USER_FOLIO -androidx.activity.ComponentActivity.Companion  KEY_USER_PASSWORD -androidx.activity.ComponentActivity.Companion  LocationTrackingService -androidx.activity.ComponentActivity.Companion  Log -androidx.activity.ComponentActivity.Companion  Manifest -androidx.activity.ComponentActivity.Companion  PackageManager -androidx.activity.ComponentActivity.Companion  R -androidx.activity.ComponentActivity.Companion  Toast -androidx.activity.ComponentActivity.Companion  WebAppInterface -androidx.activity.ComponentActivity.Companion  WebSettings -androidx.activity.ComponentActivity.Companion  all -androidx.activity.ComponentActivity.Companion  apply -androidx.activity.ComponentActivity.Companion  arrayOf -androidx.activity.ComponentActivity.Companion  currentInstance -androidx.activity.ComponentActivity.Companion  filter -androidx.activity.ComponentActivity.Companion  isEmpty -androidx.activity.ComponentActivity.Companion  java -androidx.activity.ComponentActivity.Companion  joinToString -androidx.activity.ComponentActivity.Companion  
mutableListOf -androidx.activity.ComponentActivity.Companion  toTypedArray -androidx.activity.ComponentActivity.Companion  
trimIndent -androidx.activity.ComponentActivity.Companion  with -androidx.activity.ComponentActivity.Companion  ActivityResultCallback androidx.activity.result  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  RequestMultiplePermissions 9androidx.activity.result.contract.ActivityResultContracts  AlertDialog androidx.appcompat.app  AppCompatActivity androidx.appcompat.app  Builder "androidx.appcompat.app.AlertDialog  
setCancelable *androidx.appcompat.app.AlertDialog.Builder  
setMessage *androidx.appcompat.app.AlertDialog.Builder  setNegativeButton *androidx.appcompat.app.AlertDialog.Builder  setPositiveButton *androidx.appcompat.app.AlertDialog.Builder  setTitle *androidx.appcompat.app.AlertDialog.Builder  show *androidx.appcompat.app.AlertDialog.Builder  ActivityResultContracts (androidx.appcompat.app.AppCompatActivity  AlertDialog (androidx.appcompat.app.AppCompatActivity  Boolean (androidx.appcompat.app.AppCompatActivity  Build (androidx.appcompat.app.AppCompatActivity  Context (androidx.appcompat.app.AppCompatActivity  
ContextCompat (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  KEY_ACCESS_TOKEN (androidx.appcompat.app.AppCompatActivity  KEY_USER_FOLIO (androidx.appcompat.app.AppCompatActivity  KEY_USER_PASSWORD (androidx.appcompat.app.AppCompatActivity  LocationTrackingService (androidx.appcompat.app.AppCompatActivity  Log (androidx.appcompat.app.AppCompatActivity  Manifest (androidx.appcompat.app.AppCompatActivity  PackageManager (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  WebAppInterface (androidx.appcompat.app.AppCompatActivity  WebSettings (androidx.appcompat.app.AppCompatActivity  WebView (androidx.appcompat.app.AppCompatActivity  
WebViewClient (androidx.appcompat.app.AppCompatActivity  all (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  arrayOf (androidx.appcompat.app.AppCompatActivity  currentInstance (androidx.appcompat.app.AppCompatActivity  filter (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  isEmpty (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  joinToString (androidx.appcompat.app.AppCompatActivity  
mutableListOf (androidx.appcompat.app.AppCompatActivity  
onBackPressed (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  onPause (androidx.appcompat.app.AppCompatActivity  onResume (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  toTypedArray (androidx.appcompat.app.AppCompatActivity  
trimIndent (androidx.appcompat.app.AppCompatActivity  with (androidx.appcompat.app.AppCompatActivity  ActivityCompat androidx.core.app  NotificationCompat androidx.core.app  checkSelfPermission  androidx.core.app.ActivityCompat  ActivityResultContracts #androidx.core.app.ComponentActivity  AlertDialog #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Context #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  Double #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  Float #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  KEY_ACCESS_TOKEN #androidx.core.app.ComponentActivity  KEY_USER_FOLIO #androidx.core.app.ComponentActivity  KEY_USER_PASSWORD #androidx.core.app.ComponentActivity  LocationTrackingService #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  MainActivity #androidx.core.app.ComponentActivity  Manifest #androidx.core.app.ComponentActivity  PackageManager #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  WebAppInterface #androidx.core.app.ComponentActivity  WebSettings #androidx.core.app.ComponentActivity  WebView #androidx.core.app.ComponentActivity  
WebViewClient #androidx.core.app.ComponentActivity  all #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  arrayOf #androidx.core.app.ComponentActivity  currentInstance #androidx.core.app.ComponentActivity  filter #androidx.core.app.ComponentActivity  isEmpty #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  joinToString #androidx.core.app.ComponentActivity  
mutableListOf #androidx.core.app.ComponentActivity  toTypedArray #androidx.core.app.ComponentActivity  
trimIndent #androidx.core.app.ComponentActivity  with #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  CATEGORY_MESSAGE $androidx.core.app.NotificationCompat  DEFAULT_ALL $androidx.core.app.NotificationCompat  
PRIORITY_HIGH $androidx.core.app.NotificationCompat  PRIORITY_LOW $androidx.core.app.NotificationCompat  VISIBILITY_PUBLIC $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  
setAutoCancel ,androidx.core.app.NotificationCompat.Builder  setCategory ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setDefaults ,androidx.core.app.NotificationCompat.Builder  
setOngoing ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  
setVisibility ,androidx.core.app.NotificationCompat.Builder  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  ActivityResultContracts &androidx.fragment.app.FragmentActivity  AlertDialog &androidx.fragment.app.FragmentActivity  Boolean &androidx.fragment.app.FragmentActivity  Build &androidx.fragment.app.FragmentActivity  Context &androidx.fragment.app.FragmentActivity  
ContextCompat &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  KEY_ACCESS_TOKEN &androidx.fragment.app.FragmentActivity  KEY_USER_FOLIO &androidx.fragment.app.FragmentActivity  KEY_USER_PASSWORD &androidx.fragment.app.FragmentActivity  LocationTrackingService &androidx.fragment.app.FragmentActivity  Log &androidx.fragment.app.FragmentActivity  Manifest &androidx.fragment.app.FragmentActivity  PackageManager &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  WebAppInterface &androidx.fragment.app.FragmentActivity  WebSettings &androidx.fragment.app.FragmentActivity  WebView &androidx.fragment.app.FragmentActivity  
WebViewClient &androidx.fragment.app.FragmentActivity  all &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  arrayOf &androidx.fragment.app.FragmentActivity  currentInstance &androidx.fragment.app.FragmentActivity  filter &androidx.fragment.app.FragmentActivity  isEmpty &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  joinToString &androidx.fragment.app.FragmentActivity  
mutableListOf &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  onPause &androidx.fragment.app.FragmentActivity  onResume &androidx.fragment.app.FragmentActivity  toTypedArray &androidx.fragment.app.FragmentActivity  
trimIndent &androidx.fragment.app.FragmentActivity  with &androidx.fragment.app.FragmentActivity  ActivityCompat com.example.opera  ActivityResultContracts com.example.opera  AlertDialog com.example.opera  Any com.example.opera  AppCompatActivity com.example.opera  BATCH_SEND_INTERVAL com.example.opera  BackgroundService com.example.opera  Boolean com.example.opera  BufferedReader com.example.opera  Build com.example.opera  Bundle com.example.opera  
CHANNEL_ID com.example.opera  COLUMN_ACCURACY com.example.opera  	COLUMN_ID com.example.opera  COLUMN_LATITUDE com.example.opera  COLUMN_LONGITUDE com.example.opera  COLUMN_RETRY_COUNT com.example.opera  COLUMN_SENT com.example.opera  COLUMN_TIMESTAMP com.example.opera  CONNECTION_TIMEOUT com.example.opera  Calendar com.example.opera  
ContentValues com.example.opera  Context com.example.opera  
ContextCompat com.example.opera  CoroutineContext com.example.opera  CoroutineScope com.example.opera  Date com.example.opera  Dispatchers com.example.opera  Double com.example.opera  	Exception com.example.opera  Float com.example.opera  HttpURLConnection com.example.opera  IBinder com.example.opera  InputStreamReader com.example.opera  Int com.example.opera  Intent com.example.opera  
JSONObject com.example.opera  JavascriptInterface com.example.opera  KEY_ACCESS_TOKEN com.example.opera  KEY_USER_FOLIO com.example.opera  KEY_USER_PASSWORD com.example.opera  List com.example.opera  Locale com.example.opera  Location com.example.opera  LocationDatabaseHelper com.example.opera  LocationListener com.example.opera  LocationManager com.example.opera  
LocationPoint com.example.opera  LocationTrackingService com.example.opera  Log com.example.opera  Long com.example.opera  Looper com.example.opera  MAX_ACCURACY_METERS_ACCEPTABLE com.example.opera  MAX_ACCURACY_METERS_EXCELLENT com.example.opera  MAX_ACCURACY_METERS_GOOD com.example.opera  MAX_JUMP_DISTANCE_METERS com.example.opera  MAX_POINTS_PER_BATCH com.example.opera  
MAX_SPEED_KMH com.example.opera  MESSAGES_URL com.example.opera  MIN_DISTANCE_CHANGE com.example.opera  MIN_STABILITY_POINTS com.example.opera  MIN_TIME_BETWEEN_POINTS com.example.opera  MIN_TIME_BETWEEN_UPDATES com.example.opera  MainActivity com.example.opera  Manifest com.example.opera  Map com.example.opera  Math com.example.opera  NOTIFICATION_CHECK_INTERVAL com.example.opera  NOTIFICATION_ID com.example.opera  Notification com.example.opera  NotificationChannel com.example.opera  NotificationCompat com.example.opera  NotificationManager com.example.opera  OutputStreamWriter com.example.opera  
PREFS_NAME com.example.opera  PackageManager com.example.opera  Pair com.example.opera  
PendingIntent com.example.opera  PowerManager com.example.opera  R com.example.opera  READ_TIMEOUT com.example.opera  
SERVER_URL com.example.opera  SMOOTHING_FACTOR com.example.opera  SQLiteDatabase com.example.opera  SQLiteOpenHelper com.example.opera  START_STICKY com.example.opera  STATS_LOG_INTERVAL com.example.opera  Service com.example.opera  SimpleDateFormat com.example.opera  String com.example.opera  
SupervisorJob com.example.opera  System com.example.opera  TABLE_LOCATIONS com.example.opera  TAG com.example.opera  TimeZone com.example.opera  Toast com.example.opera  URL com.example.opera  WebAppInterface com.example.opera  WebSettings com.example.opera  WebView com.example.opera  
WebViewClient com.example.opera  all com.example.opera  android com.example.opera  apply com.example.opera  applySmoothingToLocation com.example.opera  arrayOf com.example.opera  average com.example.opera  buildString com.example.opera  cancel com.example.opera  checkForNotifications com.example.opera  chunked com.example.opera  createNotification com.example.opera  createNotificationChannel com.example.opera  currentInstance com.example.opera  delay com.example.opera  filter com.example.opera  format com.example.opera  formatTimestamp com.example.opera  getSystemService com.example.opera  handleNewLocation com.example.opera  isActive com.example.opera  isEmpty com.example.opera  isLocationValid com.example.opera  
isNotEmpty com.example.opera  
isNullOrEmpty com.example.opera  java com.example.opera  joinToString com.example.opera  lastValidLocation com.example.opera  lastValidTimestamp com.example.opera  launch com.example.opera  let com.example.opera  locationDatabaseHelper com.example.opera  locationManager com.example.opera  logFilteringStats com.example.opera  map com.example.opera  mapOf com.example.opera  
mutableListOf com.example.opera  readText com.example.opera  restartLocationTracking com.example.opera  sendLocationToWebView com.example.opera  sendPendingLocations com.example.opera  smoothedLatitude com.example.opera  smoothedLongitude com.example.opera  startForeground com.example.opera  stopSelf com.example.opera  take com.example.opera  to com.example.opera  toTypedArray com.example.opera  
trimIndent com.example.opera  updateLocationAvailability com.example.opera  updateLocationFromService com.example.opera  use com.example.opera  wakeLock com.example.opera  with com.example.opera  withContext com.example.opera  	withIndex com.example.opera  Build #com.example.opera.BackgroundService  
CHANNEL_ID #com.example.opera.BackgroundService  Context #com.example.opera.BackgroundService  CoroutineScope #com.example.opera.BackgroundService  Dispatchers #com.example.opera.BackgroundService  Intent #com.example.opera.BackgroundService  MainActivity #com.example.opera.BackgroundService  NOTIFICATION_ID #com.example.opera.BackgroundService  NotificationChannel #com.example.opera.BackgroundService  NotificationCompat #com.example.opera.BackgroundService  NotificationManager #com.example.opera.BackgroundService  
PendingIntent #com.example.opera.BackgroundService  PowerManager #com.example.opera.BackgroundService  R #com.example.opera.BackgroundService  START_STICKY #com.example.opera.BackgroundService  
SupervisorJob #com.example.opera.BackgroundService  android #com.example.opera.BackgroundService  apply #com.example.opera.BackgroundService  cancel #com.example.opera.BackgroundService  createNotification #com.example.opera.BackgroundService  createNotificationChannel #com.example.opera.BackgroundService  getSystemService #com.example.opera.BackgroundService  java #com.example.opera.BackgroundService  launch #com.example.opera.BackgroundService  serviceScope #com.example.opera.BackgroundService  startForeground #com.example.opera.BackgroundService  stopSelf #com.example.opera.BackgroundService  wakeLock #com.example.opera.BackgroundService  withContext #com.example.opera.BackgroundService  Any (com.example.opera.LocationDatabaseHelper  Boolean (com.example.opera.LocationDatabaseHelper  COLUMN_ACCURACY (com.example.opera.LocationDatabaseHelper  	COLUMN_ID (com.example.opera.LocationDatabaseHelper  COLUMN_LATITUDE (com.example.opera.LocationDatabaseHelper  COLUMN_LONGITUDE (com.example.opera.LocationDatabaseHelper  COLUMN_RETRY_COUNT (com.example.opera.LocationDatabaseHelper  COLUMN_SENT (com.example.opera.LocationDatabaseHelper  COLUMN_TIMESTAMP (com.example.opera.LocationDatabaseHelper  Calendar (com.example.opera.LocationDatabaseHelper  
ContentValues (com.example.opera.LocationDatabaseHelper  Context (com.example.opera.LocationDatabaseHelper  
DATABASE_NAME (com.example.opera.LocationDatabaseHelper  DATABASE_VERSION (com.example.opera.LocationDatabaseHelper  Date (com.example.opera.LocationDatabaseHelper  Double (com.example.opera.LocationDatabaseHelper  	Exception (com.example.opera.LocationDatabaseHelper  Float (com.example.opera.LocationDatabaseHelper  Int (com.example.opera.LocationDatabaseHelper  List (com.example.opera.LocationDatabaseHelper  Locale (com.example.opera.LocationDatabaseHelper  
LocationPoint (com.example.opera.LocationDatabaseHelper  Log (com.example.opera.LocationDatabaseHelper  Long (com.example.opera.LocationDatabaseHelper  Map (com.example.opera.LocationDatabaseHelper  SQLiteDatabase (com.example.opera.LocationDatabaseHelper  SimpleDateFormat (com.example.opera.LocationDatabaseHelper  String (com.example.opera.LocationDatabaseHelper  System (com.example.opera.LocationDatabaseHelper  TABLE_LOCATIONS (com.example.opera.LocationDatabaseHelper  TAG (com.example.opera.LocationDatabaseHelper  TimeZone (com.example.opera.LocationDatabaseHelper  apply (com.example.opera.LocationDatabaseHelper  arrayOf (com.example.opera.LocationDatabaseHelper  cleanOldSentLocations (com.example.opera.LocationDatabaseHelper  cleanOldSentPoints (com.example.opera.LocationDatabaseHelper  close (com.example.opera.LocationDatabaseHelper  formatTimestamp (com.example.opera.LocationDatabaseHelper  getUnsentCount (com.example.opera.LocationDatabaseHelper  getUnsentLocationPoints (com.example.opera.LocationDatabaseHelper  mapOf (com.example.opera.LocationDatabaseHelper  markLocationAsSent (com.example.opera.LocationDatabaseHelper  
mutableListOf (com.example.opera.LocationDatabaseHelper  onCreate (com.example.opera.LocationDatabaseHelper  parseTimestamp (com.example.opera.LocationDatabaseHelper  readableDatabase (com.example.opera.LocationDatabaseHelper  saveLocationPoint (com.example.opera.LocationDatabaseHelper  to (com.example.opera.LocationDatabaseHelper  
trimIndent (com.example.opera.LocationDatabaseHelper  use (com.example.opera.LocationDatabaseHelper  writableDatabase (com.example.opera.LocationDatabaseHelper  COLUMN_ACCURACY 2com.example.opera.LocationDatabaseHelper.Companion  	COLUMN_ID 2com.example.opera.LocationDatabaseHelper.Companion  COLUMN_LATITUDE 2com.example.opera.LocationDatabaseHelper.Companion  COLUMN_LONGITUDE 2com.example.opera.LocationDatabaseHelper.Companion  COLUMN_RETRY_COUNT 2com.example.opera.LocationDatabaseHelper.Companion  COLUMN_SENT 2com.example.opera.LocationDatabaseHelper.Companion  COLUMN_TIMESTAMP 2com.example.opera.LocationDatabaseHelper.Companion  Calendar 2com.example.opera.LocationDatabaseHelper.Companion  
ContentValues 2com.example.opera.LocationDatabaseHelper.Companion  
DATABASE_NAME 2com.example.opera.LocationDatabaseHelper.Companion  DATABASE_VERSION 2com.example.opera.LocationDatabaseHelper.Companion  Date 2com.example.opera.LocationDatabaseHelper.Companion  Locale 2com.example.opera.LocationDatabaseHelper.Companion  
LocationPoint 2com.example.opera.LocationDatabaseHelper.Companion  Log 2com.example.opera.LocationDatabaseHelper.Companion  SimpleDateFormat 2com.example.opera.LocationDatabaseHelper.Companion  System 2com.example.opera.LocationDatabaseHelper.Companion  TABLE_LOCATIONS 2com.example.opera.LocationDatabaseHelper.Companion  TAG 2com.example.opera.LocationDatabaseHelper.Companion  TimeZone 2com.example.opera.LocationDatabaseHelper.Companion  apply 2com.example.opera.LocationDatabaseHelper.Companion  arrayOf 2com.example.opera.LocationDatabaseHelper.Companion  formatTimestamp 2com.example.opera.LocationDatabaseHelper.Companion  mapOf 2com.example.opera.LocationDatabaseHelper.Companion  
mutableListOf 2com.example.opera.LocationDatabaseHelper.Companion  to 2com.example.opera.LocationDatabaseHelper.Companion  
trimIndent 2com.example.opera.LocationDatabaseHelper.Companion  use 2com.example.opera.LocationDatabaseHelper.Companion  ActivityCompat )com.example.opera.LocationTrackingService  Any )com.example.opera.LocationTrackingService  BATCH_SEND_INTERVAL )com.example.opera.LocationTrackingService  Boolean )com.example.opera.LocationTrackingService  BufferedReader )com.example.opera.LocationTrackingService  Build )com.example.opera.LocationTrackingService  
CHANNEL_ID )com.example.opera.LocationTrackingService  CONNECTION_TIMEOUT )com.example.opera.LocationTrackingService  	Companion )com.example.opera.LocationTrackingService  Context )com.example.opera.LocationTrackingService  CoroutineContext )com.example.opera.LocationTrackingService  Date )com.example.opera.LocationTrackingService  Dispatchers )com.example.opera.LocationTrackingService  Double )com.example.opera.LocationTrackingService  	Exception )com.example.opera.LocationTrackingService  Float )com.example.opera.LocationTrackingService  HttpURLConnection )com.example.opera.LocationTrackingService  IBinder )com.example.opera.LocationTrackingService  InputStreamReader )com.example.opera.LocationTrackingService  Int )com.example.opera.LocationTrackingService  Intent )com.example.opera.LocationTrackingService  
JSONObject )com.example.opera.LocationTrackingService  KEY_ACCESS_TOKEN )com.example.opera.LocationTrackingService  KEY_USER_FOLIO )com.example.opera.LocationTrackingService  KEY_USER_PASSWORD )com.example.opera.LocationTrackingService  List )com.example.opera.LocationTrackingService  Locale )com.example.opera.LocationTrackingService  Location )com.example.opera.LocationTrackingService  LocationDatabaseHelper )com.example.opera.LocationTrackingService  LocationListener )com.example.opera.LocationTrackingService  LocationManager )com.example.opera.LocationTrackingService  Log )com.example.opera.LocationTrackingService  Long )com.example.opera.LocationTrackingService  Looper )com.example.opera.LocationTrackingService  MAX_ACCURACY_METERS_ACCEPTABLE )com.example.opera.LocationTrackingService  MAX_ACCURACY_METERS_EXCELLENT )com.example.opera.LocationTrackingService  MAX_ACCURACY_METERS_GOOD )com.example.opera.LocationTrackingService  MAX_JUMP_DISTANCE_METERS )com.example.opera.LocationTrackingService  MAX_POINTS_PER_BATCH )com.example.opera.LocationTrackingService  
MAX_SPEED_KMH )com.example.opera.LocationTrackingService  MESSAGES_URL )com.example.opera.LocationTrackingService  MIN_DISTANCE_CHANGE )com.example.opera.LocationTrackingService  MIN_STABILITY_POINTS )com.example.opera.LocationTrackingService  MIN_TIME_BETWEEN_POINTS )com.example.opera.LocationTrackingService  MIN_TIME_BETWEEN_UPDATES )com.example.opera.LocationTrackingService  MainActivity )com.example.opera.LocationTrackingService  Manifest )com.example.opera.LocationTrackingService  Map )com.example.opera.LocationTrackingService  Math )com.example.opera.LocationTrackingService  NOTIFICATION_CHECK_INTERVAL )com.example.opera.LocationTrackingService  NOTIFICATION_ID )com.example.opera.LocationTrackingService  Notification )com.example.opera.LocationTrackingService  NotificationChannel )com.example.opera.LocationTrackingService  NotificationCompat )com.example.opera.LocationTrackingService  NotificationManager )com.example.opera.LocationTrackingService  OutputStreamWriter )com.example.opera.LocationTrackingService  
PREFS_NAME )com.example.opera.LocationTrackingService  PackageManager )com.example.opera.LocationTrackingService  Pair )com.example.opera.LocationTrackingService  
PendingIntent )com.example.opera.LocationTrackingService  R )com.example.opera.LocationTrackingService  READ_TIMEOUT )com.example.opera.LocationTrackingService  
SERVER_URL )com.example.opera.LocationTrackingService  SMOOTHING_FACTOR )com.example.opera.LocationTrackingService  START_STICKY )com.example.opera.LocationTrackingService  STATS_LOG_INTERVAL )com.example.opera.LocationTrackingService  SimpleDateFormat )com.example.opera.LocationTrackingService  String )com.example.opera.LocationTrackingService  
SupervisorJob )com.example.opera.LocationTrackingService  System )com.example.opera.LocationTrackingService  TAG )com.example.opera.LocationTrackingService  TimeZone )com.example.opera.LocationTrackingService  URL )com.example.opera.LocationTrackingService  apply )com.example.opera.LocationTrackingService  applySmoothingToLocation )com.example.opera.LocationTrackingService  average )com.example.opera.LocationTrackingService  buildString )com.example.opera.LocationTrackingService  calculateDistance )com.example.opera.LocationTrackingService  calculateVariance )com.example.opera.LocationTrackingService  checkForNotifications )com.example.opera.LocationTrackingService  chunked )com.example.opera.LocationTrackingService  createNotification )com.example.opera.LocationTrackingService  createNotificationChannel )com.example.opera.LocationTrackingService  createTempAuthToken )com.example.opera.LocationTrackingService  delay )com.example.opera.LocationTrackingService  format )com.example.opera.LocationTrackingService  formatTimestamp )com.example.opera.LocationTrackingService  getSharedPreferences )com.example.opera.LocationTrackingService  getSystemService )com.example.opera.LocationTrackingService  getUserCredentials )com.example.opera.LocationTrackingService  handleNewLocation )com.example.opera.LocationTrackingService  isActive )com.example.opera.LocationTrackingService  isLocationStable )com.example.opera.LocationTrackingService  isLocationValid )com.example.opera.LocationTrackingService  
isNotEmpty )com.example.opera.LocationTrackingService  
isNullOrEmpty )com.example.opera.LocationTrackingService  
isTracking )com.example.opera.LocationTrackingService  java )com.example.opera.LocationTrackingService  job )com.example.opera.LocationTrackingService  lastValidLocation )com.example.opera.LocationTrackingService  lastValidTimestamp )com.example.opera.LocationTrackingService  launch )com.example.opera.LocationTrackingService  let )com.example.opera.LocationTrackingService  locationDatabaseHelper )com.example.opera.LocationTrackingService  locationListener )com.example.opera.LocationTrackingService  locationManager )com.example.opera.LocationTrackingService  locationsAccepted )com.example.opera.LocationTrackingService  locationsRejectedByAccuracy )com.example.opera.LocationTrackingService  locationsRejectedByJump )com.example.opera.LocationTrackingService  locationsRejectedByProvider )com.example.opera.LocationTrackingService  locationsRejectedBySpeed )com.example.opera.LocationTrackingService  locationsRejectedByStability )com.example.opera.LocationTrackingService  locationsRejectedByTime )com.example.opera.LocationTrackingService  locationsSmoothed )com.example.opera.LocationTrackingService  logFilteringStats )com.example.opera.LocationTrackingService  map )com.example.opera.LocationTrackingService  
mutableListOf )com.example.opera.LocationTrackingService  readText )com.example.opera.LocationTrackingService  recentLocations )com.example.opera.LocationTrackingService  restartLocationTracking )com.example.opera.LocationTrackingService  sendLocationToWebView )com.example.opera.LocationTrackingService  sendPendingLocations )com.example.opera.LocationTrackingService  showNotification )com.example.opera.LocationTrackingService  smoothedLatitude )com.example.opera.LocationTrackingService  smoothedLongitude )com.example.opera.LocationTrackingService  startBatchSending )com.example.opera.LocationTrackingService  startForeground )com.example.opera.LocationTrackingService  startLocationTracking )com.example.opera.LocationTrackingService  startNotificationChecking )com.example.opera.LocationTrackingService  startStatsLogging )com.example.opera.LocationTrackingService  stopLocationTracking )com.example.opera.LocationTrackingService  totalLocationsReceived )com.example.opera.LocationTrackingService  tryServerConnection )com.example.opera.LocationTrackingService  updateLocationAvailability )com.example.opera.LocationTrackingService  updateLocationFromService )com.example.opera.LocationTrackingService  	withIndex )com.example.opera.LocationTrackingService  ActivityCompat 3com.example.opera.LocationTrackingService.Companion  BATCH_SEND_INTERVAL 3com.example.opera.LocationTrackingService.Companion  BufferedReader 3com.example.opera.LocationTrackingService.Companion  Build 3com.example.opera.LocationTrackingService.Companion  
CHANNEL_ID 3com.example.opera.LocationTrackingService.Companion  CONNECTION_TIMEOUT 3com.example.opera.LocationTrackingService.Companion  Context 3com.example.opera.LocationTrackingService.Companion  Date 3com.example.opera.LocationTrackingService.Companion  Dispatchers 3com.example.opera.LocationTrackingService.Companion  HttpURLConnection 3com.example.opera.LocationTrackingService.Companion  InputStreamReader 3com.example.opera.LocationTrackingService.Companion  Intent 3com.example.opera.LocationTrackingService.Companion  
JSONObject 3com.example.opera.LocationTrackingService.Companion  KEY_ACCESS_TOKEN 3com.example.opera.LocationTrackingService.Companion  KEY_USER_FOLIO 3com.example.opera.LocationTrackingService.Companion  KEY_USER_PASSWORD 3com.example.opera.LocationTrackingService.Companion  Locale 3com.example.opera.LocationTrackingService.Companion  Location 3com.example.opera.LocationTrackingService.Companion  LocationDatabaseHelper 3com.example.opera.LocationTrackingService.Companion  LocationManager 3com.example.opera.LocationTrackingService.Companion  Log 3com.example.opera.LocationTrackingService.Companion  Looper 3com.example.opera.LocationTrackingService.Companion  MAX_ACCURACY_METERS_ACCEPTABLE 3com.example.opera.LocationTrackingService.Companion  MAX_ACCURACY_METERS_EXCELLENT 3com.example.opera.LocationTrackingService.Companion  MAX_ACCURACY_METERS_GOOD 3com.example.opera.LocationTrackingService.Companion  MAX_JUMP_DISTANCE_METERS 3com.example.opera.LocationTrackingService.Companion  MAX_POINTS_PER_BATCH 3com.example.opera.LocationTrackingService.Companion  
MAX_SPEED_KMH 3com.example.opera.LocationTrackingService.Companion  MESSAGES_URL 3com.example.opera.LocationTrackingService.Companion  MIN_DISTANCE_CHANGE 3com.example.opera.LocationTrackingService.Companion  MIN_STABILITY_POINTS 3com.example.opera.LocationTrackingService.Companion  MIN_TIME_BETWEEN_POINTS 3com.example.opera.LocationTrackingService.Companion  MIN_TIME_BETWEEN_UPDATES 3com.example.opera.LocationTrackingService.Companion  MainActivity 3com.example.opera.LocationTrackingService.Companion  Manifest 3com.example.opera.LocationTrackingService.Companion  Math 3com.example.opera.LocationTrackingService.Companion  NOTIFICATION_CHECK_INTERVAL 3com.example.opera.LocationTrackingService.Companion  NOTIFICATION_ID 3com.example.opera.LocationTrackingService.Companion  NotificationChannel 3com.example.opera.LocationTrackingService.Companion  NotificationCompat 3com.example.opera.LocationTrackingService.Companion  NotificationManager 3com.example.opera.LocationTrackingService.Companion  OutputStreamWriter 3com.example.opera.LocationTrackingService.Companion  
PREFS_NAME 3com.example.opera.LocationTrackingService.Companion  PackageManager 3com.example.opera.LocationTrackingService.Companion  Pair 3com.example.opera.LocationTrackingService.Companion  
PendingIntent 3com.example.opera.LocationTrackingService.Companion  R 3com.example.opera.LocationTrackingService.Companion  READ_TIMEOUT 3com.example.opera.LocationTrackingService.Companion  
SERVER_URL 3com.example.opera.LocationTrackingService.Companion  SMOOTHING_FACTOR 3com.example.opera.LocationTrackingService.Companion  START_STICKY 3com.example.opera.LocationTrackingService.Companion  STATS_LOG_INTERVAL 3com.example.opera.LocationTrackingService.Companion  SimpleDateFormat 3com.example.opera.LocationTrackingService.Companion  String 3com.example.opera.LocationTrackingService.Companion  
SupervisorJob 3com.example.opera.LocationTrackingService.Companion  System 3com.example.opera.LocationTrackingService.Companion  TAG 3com.example.opera.LocationTrackingService.Companion  TimeZone 3com.example.opera.LocationTrackingService.Companion  URL 3com.example.opera.LocationTrackingService.Companion  apply 3com.example.opera.LocationTrackingService.Companion  applySmoothingToLocation 3com.example.opera.LocationTrackingService.Companion  average 3com.example.opera.LocationTrackingService.Companion  buildString 3com.example.opera.LocationTrackingService.Companion  checkForNotifications 3com.example.opera.LocationTrackingService.Companion  chunked 3com.example.opera.LocationTrackingService.Companion  delay 3com.example.opera.LocationTrackingService.Companion  format 3com.example.opera.LocationTrackingService.Companion  handleNewLocation 3com.example.opera.LocationTrackingService.Companion  isActive 3com.example.opera.LocationTrackingService.Companion  isLocationValid 3com.example.opera.LocationTrackingService.Companion  
isNotEmpty 3com.example.opera.LocationTrackingService.Companion  
isNullOrEmpty 3com.example.opera.LocationTrackingService.Companion  java 3com.example.opera.LocationTrackingService.Companion  lastValidLocation 3com.example.opera.LocationTrackingService.Companion  lastValidTimestamp 3com.example.opera.LocationTrackingService.Companion  launch 3com.example.opera.LocationTrackingService.Companion  let 3com.example.opera.LocationTrackingService.Companion  locationDatabaseHelper 3com.example.opera.LocationTrackingService.Companion  locationManager 3com.example.opera.LocationTrackingService.Companion  logFilteringStats 3com.example.opera.LocationTrackingService.Companion  map 3com.example.opera.LocationTrackingService.Companion  
mutableListOf 3com.example.opera.LocationTrackingService.Companion  readText 3com.example.opera.LocationTrackingService.Companion  restartLocationTracking 3com.example.opera.LocationTrackingService.Companion  sendLocationToWebView 3com.example.opera.LocationTrackingService.Companion  sendPendingLocations 3com.example.opera.LocationTrackingService.Companion  smoothedLatitude 3com.example.opera.LocationTrackingService.Companion  smoothedLongitude 3com.example.opera.LocationTrackingService.Companion  updateLocationAvailability 3com.example.opera.LocationTrackingService.Companion  updateLocationFromService 3com.example.opera.LocationTrackingService.Companion  	withIndex 3com.example.opera.LocationTrackingService.Companion  ActivityResultContracts com.example.opera.MainActivity  AlertDialog com.example.opera.MainActivity  Boolean com.example.opera.MainActivity  Build com.example.opera.MainActivity  Bundle com.example.opera.MainActivity  	Companion com.example.opera.MainActivity  Context com.example.opera.MainActivity  
ContextCompat com.example.opera.MainActivity  Double com.example.opera.MainActivity  	Exception com.example.opera.MainActivity  Float com.example.opera.MainActivity  Int com.example.opera.MainActivity  Intent com.example.opera.MainActivity  KEY_ACCESS_TOKEN com.example.opera.MainActivity  KEY_USER_FOLIO com.example.opera.MainActivity  KEY_USER_PASSWORD com.example.opera.MainActivity  LocationTrackingService com.example.opera.MainActivity  Log com.example.opera.MainActivity  MainActivity com.example.opera.MainActivity  Manifest com.example.opera.MainActivity  
PREFS_NAME com.example.opera.MainActivity  PackageManager com.example.opera.MainActivity  R com.example.opera.MainActivity  String com.example.opera.MainActivity  Toast com.example.opera.MainActivity  WebAppInterface com.example.opera.MainActivity  WebSettings com.example.opera.MainActivity  WebView com.example.opera.MainActivity  
WebViewClient com.example.opera.MainActivity  additionalPermissionLauncher com.example.opera.MainActivity  additionalPermissions com.example.opera.MainActivity  all com.example.opera.MainActivity  apply com.example.opera.MainActivity  arrayOf com.example.opera.MainActivity  backgroundLocationLauncher com.example.opera.MainActivity  backgroundLocationPermission com.example.opera.MainActivity  basicPermissionLauncher com.example.opera.MainActivity  basicPermissions com.example.opera.MainActivity  checkAndRequestPermissions com.example.opera.MainActivity  currentInstance com.example.opera.MainActivity  filter com.example.opera.MainActivity  findViewById com.example.opera.MainActivity  getSharedPreferences com.example.opera.MainActivity  isEmpty com.example.opera.MainActivity  java com.example.opera.MainActivity  joinToString com.example.opera.MainActivity  
mutableListOf com.example.opera.MainActivity  registerForActivityResult com.example.opera.MainActivity  requestAdditionalPermissions com.example.opera.MainActivity  #requestBackgroundLocationPermission com.example.opera.MainActivity  requestBasicPermissions com.example.opera.MainActivity  
runOnUiThread com.example.opera.MainActivity  
saveAuthToken com.example.opera.MainActivity  saveUserCredentials com.example.opera.MainActivity  setContentView com.example.opera.MainActivity  setupWebView com.example.opera.MainActivity  !showBackgroundLocationExplanation com.example.opera.MainActivity  showPermissionDeniedDialog com.example.opera.MainActivity  startForegroundService com.example.opera.MainActivity  startLocationService com.example.opera.MainActivity  startService com.example.opera.MainActivity  toTypedArray com.example.opera.MainActivity  
trimIndent com.example.opera.MainActivity  updateLocationFromService com.example.opera.MainActivity  updateWebViewLocation com.example.opera.MainActivity  webView com.example.opera.MainActivity  with com.example.opera.MainActivity  ActivityResultContracts (com.example.opera.MainActivity.Companion  AlertDialog (com.example.opera.MainActivity.Companion  Build (com.example.opera.MainActivity.Companion  Context (com.example.opera.MainActivity.Companion  
ContextCompat (com.example.opera.MainActivity.Companion  Intent (com.example.opera.MainActivity.Companion  KEY_ACCESS_TOKEN (com.example.opera.MainActivity.Companion  KEY_USER_FOLIO (com.example.opera.MainActivity.Companion  KEY_USER_PASSWORD (com.example.opera.MainActivity.Companion  LocationTrackingService (com.example.opera.MainActivity.Companion  Log (com.example.opera.MainActivity.Companion  Manifest (com.example.opera.MainActivity.Companion  PackageManager (com.example.opera.MainActivity.Companion  R (com.example.opera.MainActivity.Companion  Toast (com.example.opera.MainActivity.Companion  WebAppInterface (com.example.opera.MainActivity.Companion  WebSettings (com.example.opera.MainActivity.Companion  all (com.example.opera.MainActivity.Companion  apply (com.example.opera.MainActivity.Companion  arrayOf (com.example.opera.MainActivity.Companion  currentInstance (com.example.opera.MainActivity.Companion  filter (com.example.opera.MainActivity.Companion  isEmpty (com.example.opera.MainActivity.Companion  java (com.example.opera.MainActivity.Companion  joinToString (com.example.opera.MainActivity.Companion  
mutableListOf (com.example.opera.MainActivity.Companion  toTypedArray (com.example.opera.MainActivity.Companion  
trimIndent (com.example.opera.MainActivity.Companion  updateLocationFromService (com.example.opera.MainActivity.Companion  with (com.example.opera.MainActivity.Companion  WakeLock com.example.opera.PowerManager  ic_launcher_foreground com.example.opera.R.drawable  webView com.example.opera.R.id  
activity_main com.example.opera.R.layout  Log !com.example.opera.WebAppInterface  Toast !com.example.opera.WebAppInterface  context !com.example.opera.WebAppInterface  take !com.example.opera.WebAppInterface  BufferedReader java.io  InputStreamReader java.io  OutputStreamWriter java.io  close java.io.BufferedReader  readText java.io.BufferedReader  close java.io.OutputStreamWriter  flush java.io.OutputStreamWriter  write java.io.OutputStreamWriter  write java.io.Writer  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  
StringBuilder 	java.lang  message java.lang.Exception  atan2 java.lang.Math  cos java.lang.Math  sin java.lang.Math  sqrt java.lang.Math  	toRadians java.lang.Math  <SAM-CONSTRUCTOR> java.lang.Runnable  append java.lang.StringBuilder  currentTimeMillis java.lang.System  HttpURLConnection java.net  URL java.net  CONNECTION_TIMEOUT java.net.HttpURLConnection  HTTP_CREATED java.net.HttpURLConnection  HTTP_OK java.net.HttpURLConnection  READ_TIMEOUT java.net.HttpURLConnection  apply java.net.HttpURLConnection  connectTimeout java.net.HttpURLConnection  
disconnect java.net.HttpURLConnection  doInput java.net.HttpURLConnection  doOutput java.net.HttpURLConnection  errorStream java.net.HttpURLConnection  inputStream java.net.HttpURLConnection  outputStream java.net.HttpURLConnection  readTimeout java.net.HttpURLConnection  
requestMethod java.net.HttpURLConnection  responseCode java.net.HttpURLConnection  setRequestProperty java.net.HttpURLConnection  openConnection java.net.URL  connectTimeout java.net.URLConnection  doInput java.net.URLConnection  doOutput java.net.URLConnection  inputStream java.net.URLConnection  outputStream java.net.URLConnection  readTimeout java.net.URLConnection  setRequestProperty java.net.URLConnection  SimpleDateFormat 	java.text  format java.text.DateFormat  parse java.text.DateFormat  timeZone java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  parse java.text.SimpleDateFormat  timeZone java.text.SimpleDateFormat  ActivityCompat 	java.util  Any 	java.util  BATCH_SEND_INTERVAL 	java.util  Boolean 	java.util  BufferedReader 	java.util  Build 	java.util  
CHANNEL_ID 	java.util  COLUMN_ACCURACY 	java.util  	COLUMN_ID 	java.util  COLUMN_LATITUDE 	java.util  COLUMN_LONGITUDE 	java.util  COLUMN_RETRY_COUNT 	java.util  COLUMN_SENT 	java.util  COLUMN_TIMESTAMP 	java.util  CONNECTION_TIMEOUT 	java.util  Calendar 	java.util  
ContentValues 	java.util  Context 	java.util  CoroutineContext 	java.util  CoroutineScope 	java.util  Date 	java.util  Dispatchers 	java.util  Double 	java.util  	Exception 	java.util  Float 	java.util  HttpURLConnection 	java.util  IBinder 	java.util  InputStreamReader 	java.util  Int 	java.util  Intent 	java.util  
JSONObject 	java.util  KEY_ACCESS_TOKEN 	java.util  KEY_USER_FOLIO 	java.util  KEY_USER_PASSWORD 	java.util  List 	java.util  Locale 	java.util  Location 	java.util  LocationDatabaseHelper 	java.util  LocationListener 	java.util  LocationManager 	java.util  
LocationPoint 	java.util  Log 	java.util  Long 	java.util  Looper 	java.util  MAX_ACCURACY_METERS_ACCEPTABLE 	java.util  MAX_ACCURACY_METERS_EXCELLENT 	java.util  MAX_ACCURACY_METERS_GOOD 	java.util  MAX_JUMP_DISTANCE_METERS 	java.util  MAX_POINTS_PER_BATCH 	java.util  
MAX_SPEED_KMH 	java.util  MESSAGES_URL 	java.util  MIN_DISTANCE_CHANGE 	java.util  MIN_STABILITY_POINTS 	java.util  MIN_TIME_BETWEEN_POINTS 	java.util  MIN_TIME_BETWEEN_UPDATES 	java.util  MainActivity 	java.util  Manifest 	java.util  Map 	java.util  Math 	java.util  NOTIFICATION_CHECK_INTERVAL 	java.util  NOTIFICATION_ID 	java.util  Notification 	java.util  NotificationChannel 	java.util  NotificationCompat 	java.util  NotificationManager 	java.util  OutputStreamWriter 	java.util  
PREFS_NAME 	java.util  PackageManager 	java.util  Pair 	java.util  
PendingIntent 	java.util  R 	java.util  READ_TIMEOUT 	java.util  
SERVER_URL 	java.util  SMOOTHING_FACTOR 	java.util  SQLiteDatabase 	java.util  SQLiteOpenHelper 	java.util  START_STICKY 	java.util  STATS_LOG_INTERVAL 	java.util  Service 	java.util  SimpleDateFormat 	java.util  String 	java.util  
SupervisorJob 	java.util  System 	java.util  TABLE_LOCATIONS 	java.util  TAG 	java.util  TimeZone 	java.util  URL 	java.util  apply 	java.util  applySmoothingToLocation 	java.util  arrayOf 	java.util  average 	java.util  buildString 	java.util  checkForNotifications 	java.util  chunked 	java.util  delay 	java.util  format 	java.util  formatTimestamp 	java.util  handleNewLocation 	java.util  isActive 	java.util  isLocationValid 	java.util  
isNotEmpty 	java.util  
isNullOrEmpty 	java.util  java 	java.util  lastValidLocation 	java.util  lastValidTimestamp 	java.util  launch 	java.util  let 	java.util  locationDatabaseHelper 	java.util  locationManager 	java.util  logFilteringStats 	java.util  map 	java.util  mapOf 	java.util  
mutableListOf 	java.util  readText 	java.util  restartLocationTracking 	java.util  sendLocationToWebView 	java.util  sendPendingLocations 	java.util  smoothedLatitude 	java.util  smoothedLongitude 	java.util  to 	java.util  
trimIndent 	java.util  updateLocationAvailability 	java.util  updateLocationFromService 	java.util  use 	java.util  	withIndex 	java.util  DAY_OF_YEAR java.util.Calendar  add java.util.Calendar  getInstance java.util.Calendar  timeInMillis java.util.Calendar  time java.util.Date  US java.util.Locale  
getDefault java.util.Locale  getTimeZone java.util.TimeZone  Array kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  Pair kotlin  Result kotlin  String kotlin  apply kotlin  arrayOf kotlin  let kotlin  map kotlin  to kotlin  use kotlin  with kotlin  all kotlin.Array  filter kotlin.Array  isEmpty kotlin.Array  not kotlin.Boolean  	compareTo 
kotlin.Double  div 
kotlin.Double  minus 
kotlin.Double  plus 
kotlin.Double  times 
kotlin.Double  	compareTo kotlin.Float  times kotlin.Float  toString kotlin.Float  	compareTo 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  times 
kotlin.Int  toDouble 
kotlin.Int  
unaryMinus 
kotlin.Int  	compareTo kotlin.Long  div kotlin.Long  minus kotlin.Long  times kotlin.Long  toString kotlin.Long  first kotlin.Pair  second kotlin.Pair  	Companion 
kotlin.String  format 
kotlin.String  
isNullOrEmpty 
kotlin.String  plus 
kotlin.String  take 
kotlin.String  to 
kotlin.String  
trimIndent 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  
Collection kotlin.collections  IndexedValue kotlin.collections  Iterable kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  all kotlin.collections  average kotlin.collections  chunked kotlin.collections  filter kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  joinToString kotlin.collections  map kotlin.collections  mapOf kotlin.collections  
mutableListOf kotlin.collections  take kotlin.collections  toTypedArray kotlin.collections  	withIndex kotlin.collections  all kotlin.collections.Collection  
component1 kotlin.collections.IndexedValue  
component2 kotlin.collections.IndexedValue  iterator kotlin.collections.Iterable  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  average kotlin.collections.List  chunked kotlin.collections.List  isEmpty kotlin.collections.List  iterator kotlin.collections.List  joinToString kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  toTypedArray kotlin.collections.List  	withIndex kotlin.collections.List  get kotlin.collections.Map  values kotlin.collections.Map  Build kotlin.collections.MutableList  Manifest kotlin.collections.MutableList  add kotlin.collections.MutableList  apply kotlin.collections.MutableList  map kotlin.collections.MutableList  removeAt kotlin.collections.MutableList  size kotlin.collections.MutableList  toTypedArray kotlin.collections.MutableList  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  plus "kotlin.coroutines.CoroutineContext  readText 	kotlin.io  use 	kotlin.io  java 
kotlin.jvm  java kotlin.reflect.KClass  Sequence kotlin.sequences  all kotlin.sequences  average kotlin.sequences  chunked kotlin.sequences  filter kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  take kotlin.sequences  	withIndex kotlin.sequences  all kotlin.text  buildString kotlin.text  chunked kotlin.text  filter kotlin.text  format kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  map kotlin.text  take kotlin.text  
trimIndent kotlin.text  	withIndex kotlin.text  ActivityCompat kotlinx.coroutines  Any kotlinx.coroutines  BATCH_SEND_INTERVAL kotlinx.coroutines  Boolean kotlinx.coroutines  BufferedReader kotlinx.coroutines  Build kotlinx.coroutines  
CHANNEL_ID kotlinx.coroutines  CONNECTION_TIMEOUT kotlinx.coroutines  CompletableJob kotlinx.coroutines  Context kotlinx.coroutines  CoroutineContext kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Date kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  Double kotlinx.coroutines  	Exception kotlinx.coroutines  Float kotlinx.coroutines  HttpURLConnection kotlinx.coroutines  IBinder kotlinx.coroutines  InputStreamReader kotlinx.coroutines  Int kotlinx.coroutines  Intent kotlinx.coroutines  
JSONObject kotlinx.coroutines  Job kotlinx.coroutines  KEY_ACCESS_TOKEN kotlinx.coroutines  KEY_USER_FOLIO kotlinx.coroutines  KEY_USER_PASSWORD kotlinx.coroutines  List kotlinx.coroutines  Locale kotlinx.coroutines  Location kotlinx.coroutines  LocationDatabaseHelper kotlinx.coroutines  LocationListener kotlinx.coroutines  LocationManager kotlinx.coroutines  Log kotlinx.coroutines  Long kotlinx.coroutines  Looper kotlinx.coroutines  MAX_ACCURACY_METERS_ACCEPTABLE kotlinx.coroutines  MAX_ACCURACY_METERS_EXCELLENT kotlinx.coroutines  MAX_ACCURACY_METERS_GOOD kotlinx.coroutines  MAX_JUMP_DISTANCE_METERS kotlinx.coroutines  MAX_POINTS_PER_BATCH kotlinx.coroutines  
MAX_SPEED_KMH kotlinx.coroutines  MESSAGES_URL kotlinx.coroutines  MIN_DISTANCE_CHANGE kotlinx.coroutines  MIN_STABILITY_POINTS kotlinx.coroutines  MIN_TIME_BETWEEN_POINTS kotlinx.coroutines  MIN_TIME_BETWEEN_UPDATES kotlinx.coroutines  MainActivity kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  Manifest kotlinx.coroutines  Map kotlinx.coroutines  Math kotlinx.coroutines  NOTIFICATION_CHECK_INTERVAL kotlinx.coroutines  NOTIFICATION_ID kotlinx.coroutines  Notification kotlinx.coroutines  NotificationChannel kotlinx.coroutines  NotificationCompat kotlinx.coroutines  NotificationManager kotlinx.coroutines  OutputStreamWriter kotlinx.coroutines  
PREFS_NAME kotlinx.coroutines  PackageManager kotlinx.coroutines  Pair kotlinx.coroutines  
PendingIntent kotlinx.coroutines  PowerManager kotlinx.coroutines  R kotlinx.coroutines  READ_TIMEOUT kotlinx.coroutines  
SERVER_URL kotlinx.coroutines  SMOOTHING_FACTOR kotlinx.coroutines  START_STICKY kotlinx.coroutines  STATS_LOG_INTERVAL kotlinx.coroutines  Service kotlinx.coroutines  SimpleDateFormat kotlinx.coroutines  String kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  System kotlinx.coroutines  TAG kotlinx.coroutines  TimeZone kotlinx.coroutines  URL kotlinx.coroutines  android kotlinx.coroutines  apply kotlinx.coroutines  applySmoothingToLocation kotlinx.coroutines  average kotlinx.coroutines  buildString kotlinx.coroutines  cancel kotlinx.coroutines  checkForNotifications kotlinx.coroutines  chunked kotlinx.coroutines  createNotification kotlinx.coroutines  createNotificationChannel kotlinx.coroutines  delay kotlinx.coroutines  format kotlinx.coroutines  getSystemService kotlinx.coroutines  handleNewLocation kotlinx.coroutines  isActive kotlinx.coroutines  isLocationValid kotlinx.coroutines  
isNotEmpty kotlinx.coroutines  
isNullOrEmpty kotlinx.coroutines  java kotlinx.coroutines  lastValidLocation kotlinx.coroutines  lastValidTimestamp kotlinx.coroutines  launch kotlinx.coroutines  let kotlinx.coroutines  locationDatabaseHelper kotlinx.coroutines  locationManager kotlinx.coroutines  logFilteringStats kotlinx.coroutines  map kotlinx.coroutines  
mutableListOf kotlinx.coroutines  readText kotlinx.coroutines  restartLocationTracking kotlinx.coroutines  sendLocationToWebView kotlinx.coroutines  sendPendingLocations kotlinx.coroutines  smoothedLatitude kotlinx.coroutines  smoothedLongitude kotlinx.coroutines  startForeground kotlinx.coroutines  stopSelf kotlinx.coroutines  updateLocationAvailability kotlinx.coroutines  updateLocationFromService kotlinx.coroutines  wakeLock kotlinx.coroutines  withContext kotlinx.coroutines  	withIndex kotlinx.coroutines  cancel !kotlinx.coroutines.CompletableJob  plus &kotlinx.coroutines.CoroutineDispatcher  BATCH_SEND_INTERVAL !kotlinx.coroutines.CoroutineScope  Context !kotlinx.coroutines.CoroutineScope  Dispatchers !kotlinx.coroutines.CoroutineScope  LocationManager !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  NOTIFICATION_CHECK_INTERVAL !kotlinx.coroutines.CoroutineScope  NOTIFICATION_ID !kotlinx.coroutines.CoroutineScope  PowerManager !kotlinx.coroutines.CoroutineScope  STATS_LOG_INTERVAL !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  android !kotlinx.coroutines.CoroutineScope  applySmoothingToLocation !kotlinx.coroutines.CoroutineScope  cancel !kotlinx.coroutines.CoroutineScope  checkForNotifications !kotlinx.coroutines.CoroutineScope  createNotification !kotlinx.coroutines.CoroutineScope  createNotificationChannel !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  getSystemService !kotlinx.coroutines.CoroutineScope  isActive !kotlinx.coroutines.CoroutineScope  isLocationValid !kotlinx.coroutines.CoroutineScope  lastValidLocation !kotlinx.coroutines.CoroutineScope  lastValidTimestamp !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  locationDatabaseHelper !kotlinx.coroutines.CoroutineScope  locationManager !kotlinx.coroutines.CoroutineScope  logFilteringStats !kotlinx.coroutines.CoroutineScope  restartLocationTracking !kotlinx.coroutines.CoroutineScope  sendLocationToWebView !kotlinx.coroutines.CoroutineScope  sendPendingLocations !kotlinx.coroutines.CoroutineScope  startForeground !kotlinx.coroutines.CoroutineScope  stopSelf !kotlinx.coroutines.CoroutineScope  wakeLock !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  cancel kotlinx.coroutines.Job  WakeLock kotlinx.coroutines.PowerManager  
JSONObject org.json  
JSONObject org.json.JSONObject  NULL org.json.JSONObject  System org.json.JSONObject  apply org.json.JSONObject  
optBoolean org.json.JSONObject  optInt org.json.JSONObject  	optString org.json.JSONObject  put org.json.JSONObject  toString org.json.JSONObject                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  