-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:2:1-77:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:2:1-77:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:2:1-77:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:2:1-77:12
MERGED from [androidx.databinding:viewbinding:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2c44861391596d80bd67a07e6c83940\transformed\viewbinding-8.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\643bf35482294389b559adcaf5700b1d\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d6c8a9777c639f6101ac44cb5cb0a92\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5d15ca2605a417743393fa2051d50fc\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74379aaf95c75674bc5035ef49ea8a78\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3860b6f089fd72f1833a91f6b0bdd1dc\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4e2cb35b1a2d5e85b18f9f7613bced8\transformed\play-services-location-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c1725a107cd988a0012d2ca89c3be54\transformed\play-services-base-18.3.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1196f2fbdf8a67fb4c152d5cdcd80879\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea16d2272a061f1e000c22cd3a27cb04\transformed\play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03a26953245ab2ba6a9c0421fa884afc\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c305f39ed4b7a95ed8b0ac8ecdfa216\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2114d1501b033487f8f7b1f3a920567e\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9fd68caebdf5fd6dca166a72f8e8973\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\990411c5775e0c0046d966d13574aa62\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85c58d5bafc93b6d378026b8eb0f3daf\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aeb5589d9ada176af4e4db71b26d9385\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fa808d656ba81658208c4c51aaa650c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35c862af6b09019a11107bb57e0b94cc\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7a002aeb6af52f46643db1cc98b572c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324edac2fd681078dcdccb07b4a75791\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5e9f9a190ef9e4f0ccb19c5a40bc202\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e904dfb5e600b899bf538f7edf260f6d\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a50cd795d2559eb03113169397181a9\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06d8dfc469d9bf677fb46fadd448c803\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\021aa026a05b249151db43b54db81793\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1764b3a7bfea23fa8855db30904ed41\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a0a494460687dc2f027abe1f8262dc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a25e57c98fed237f2d19ac0a40e481d\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5d7a539bf5a9679d45fc624ea9dd76\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fe7059ea9787f60426bd0424520121e\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37d09f9ffb0efd9e3ac0e9f0608f9309\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e1facdb6ba3c667b8e94d5b732c552b\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b954304e86e1a77bf0f40b3afebecb89\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d12b11e02b3c59e5242e6751bb5344ec\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d60d2788316f54b2ce418a493d05edf5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c982b0bf732a0f8e199d9f32f350363\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ab3ec8f60c063778b991f9815a4d66f\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f787a03729a33667bd661826a5958e82\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07e996ef8f620b19e8779f04c1311bd5\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2383de3c461806f794bd390540db73ee\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d82031bdfdf8f124b4c1e1dc6bb2a6f9\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ad8354d43e79dc7dc92349196ebbcff\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0f329682f0eab29e91196cb257f03cc\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:9:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:9:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:10:5-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:11:5-85
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:11:22-82
uses-permission#android.permission.FOREGROUND_SERVICE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:14:5-86
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:14:22-83
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:17:5-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:17:22-62
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:20:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:20:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:21:5-89
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:21:22-86
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:22:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:22:22-65
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:25:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:25:22-74
uses-permission#android.permission.BLUETOOTH
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:28:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:28:22-65
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:29:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:29:22-73
uses-feature#android.hardware.location.gps
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:32:5-90
	android:required
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:32:64-87
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:32:19-63
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:33:5-84
	android:required
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:33:58-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:33:19-57
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:35:5-75:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:35:5-75:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\643bf35482294389b559adcaf5700b1d\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\643bf35482294389b559adcaf5700b1d\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d6c8a9777c639f6101ac44cb5cb0a92\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d6c8a9777c639f6101ac44cb5cb0a92\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4e2cb35b1a2d5e85b18f9f7613bced8\transformed\play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4e2cb35b1a2d5e85b18f9f7613bced8\transformed\play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c1725a107cd988a0012d2ca89c3be54\transformed\play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c1725a107cd988a0012d2ca89c3be54\transformed\play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1196f2fbdf8a67fb4c152d5cdcd80879\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1196f2fbdf8a67fb4c152d5cdcd80879\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea16d2272a061f1e000c22cd3a27cb04\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea16d2272a061f1e000c22cd3a27cb04\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a0a494460687dc2f027abe1f8262dc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a0a494460687dc2f027abe1f8262dc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d60d2788316f54b2ce418a493d05edf5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d60d2788316f54b2ce418a493d05edf5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f787a03729a33667bd661826a5958e82\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f787a03729a33667bd661826a5958e82\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:42:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:40:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:38:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:41:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:44:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:39:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:36:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:43:9-43
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:45:9-69
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:37:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:46:9-44
activity#com.example.opera.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:50:9-58:20
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:52:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:53:13-74
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:51:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:54:13-57:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:55:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:55:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:56:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:56:27-74
service#com.example.opera.BackgroundService
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:61:9-67:19
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:63:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:64:13-55
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:62:13-46
property#android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:65:13-66:51
	android:value
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:66:23-48
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:65:23-82
service#com.example.opera.LocationTrackingService
ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:70:9-73:56
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:72:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:73:13-53
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml:71:13-52
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2c44861391596d80bd67a07e6c83940\transformed\viewbinding-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2c44861391596d80bd67a07e6c83940\transformed\viewbinding-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\643bf35482294389b559adcaf5700b1d\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\643bf35482294389b559adcaf5700b1d\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d6c8a9777c639f6101ac44cb5cb0a92\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d6c8a9777c639f6101ac44cb5cb0a92\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5d15ca2605a417743393fa2051d50fc\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5d15ca2605a417743393fa2051d50fc\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74379aaf95c75674bc5035ef49ea8a78\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74379aaf95c75674bc5035ef49ea8a78\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3860b6f089fd72f1833a91f6b0bdd1dc\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3860b6f089fd72f1833a91f6b0bdd1dc\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4e2cb35b1a2d5e85b18f9f7613bced8\transformed\play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4e2cb35b1a2d5e85b18f9f7613bced8\transformed\play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c1725a107cd988a0012d2ca89c3be54\transformed\play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c1725a107cd988a0012d2ca89c3be54\transformed\play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1196f2fbdf8a67fb4c152d5cdcd80879\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1196f2fbdf8a67fb4c152d5cdcd80879\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea16d2272a061f1e000c22cd3a27cb04\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea16d2272a061f1e000c22cd3a27cb04\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03a26953245ab2ba6a9c0421fa884afc\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03a26953245ab2ba6a9c0421fa884afc\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c305f39ed4b7a95ed8b0ac8ecdfa216\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c305f39ed4b7a95ed8b0ac8ecdfa216\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2114d1501b033487f8f7b1f3a920567e\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2114d1501b033487f8f7b1f3a920567e\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9fd68caebdf5fd6dca166a72f8e8973\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9fd68caebdf5fd6dca166a72f8e8973\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\990411c5775e0c0046d966d13574aa62\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\990411c5775e0c0046d966d13574aa62\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85c58d5bafc93b6d378026b8eb0f3daf\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85c58d5bafc93b6d378026b8eb0f3daf\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aeb5589d9ada176af4e4db71b26d9385\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aeb5589d9ada176af4e4db71b26d9385\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fa808d656ba81658208c4c51aaa650c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fa808d656ba81658208c4c51aaa650c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35c862af6b09019a11107bb57e0b94cc\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35c862af6b09019a11107bb57e0b94cc\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7a002aeb6af52f46643db1cc98b572c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7a002aeb6af52f46643db1cc98b572c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324edac2fd681078dcdccb07b4a75791\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324edac2fd681078dcdccb07b4a75791\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5e9f9a190ef9e4f0ccb19c5a40bc202\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5e9f9a190ef9e4f0ccb19c5a40bc202\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e904dfb5e600b899bf538f7edf260f6d\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e904dfb5e600b899bf538f7edf260f6d\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a50cd795d2559eb03113169397181a9\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a50cd795d2559eb03113169397181a9\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06d8dfc469d9bf677fb46fadd448c803\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06d8dfc469d9bf677fb46fadd448c803\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\021aa026a05b249151db43b54db81793\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\021aa026a05b249151db43b54db81793\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1764b3a7bfea23fa8855db30904ed41\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1764b3a7bfea23fa8855db30904ed41\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a0a494460687dc2f027abe1f8262dc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a0a494460687dc2f027abe1f8262dc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a25e57c98fed237f2d19ac0a40e481d\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a25e57c98fed237f2d19ac0a40e481d\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5d7a539bf5a9679d45fc624ea9dd76\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5d7a539bf5a9679d45fc624ea9dd76\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fe7059ea9787f60426bd0424520121e\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fe7059ea9787f60426bd0424520121e\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37d09f9ffb0efd9e3ac0e9f0608f9309\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37d09f9ffb0efd9e3ac0e9f0608f9309\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e1facdb6ba3c667b8e94d5b732c552b\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e1facdb6ba3c667b8e94d5b732c552b\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b954304e86e1a77bf0f40b3afebecb89\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b954304e86e1a77bf0f40b3afebecb89\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d12b11e02b3c59e5242e6751bb5344ec\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d12b11e02b3c59e5242e6751bb5344ec\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d60d2788316f54b2ce418a493d05edf5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d60d2788316f54b2ce418a493d05edf5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c982b0bf732a0f8e199d9f32f350363\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c982b0bf732a0f8e199d9f32f350363\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ab3ec8f60c063778b991f9815a4d66f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ab3ec8f60c063778b991f9815a4d66f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f787a03729a33667bd661826a5958e82\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f787a03729a33667bd661826a5958e82\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07e996ef8f620b19e8779f04c1311bd5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07e996ef8f620b19e8779f04c1311bd5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2383de3c461806f794bd390540db73ee\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2383de3c461806f794bd390540db73ee\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d82031bdfdf8f124b4c1e1dc6bb2a6f9\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d82031bdfdf8f124b4c1e1dc6bb2a6f9\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ad8354d43e79dc7dc92349196ebbcff\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ad8354d43e79dc7dc92349196ebbcff\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0f329682f0eab29e91196cb257f03cc\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0f329682f0eab29e91196cb257f03cc\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\opera\app\src\main\AndroidManifest.xml
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c1725a107cd988a0012d2ca89c3be54\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c1725a107cd988a0012d2ca89c3be54\transformed\play-services-base-18.3.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c1725a107cd988a0012d2ca89c3be54\transformed\play-services-base-18.3.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c1725a107cd988a0012d2ca89c3be54\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea16d2272a061f1e000c22cd3a27cb04\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea16d2272a061f1e000c22cd3a27cb04\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea16d2272a061f1e000c22cd3a27cb04\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a0a494460687dc2f027abe1f8262dc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a0a494460687dc2f027abe1f8262dc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d60d2788316f54b2ce418a493d05edf5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d60d2788316f54b2ce418a493d05edf5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83685429465f165f811a287aa49486b5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.opera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.opera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c6cb53daeb20508ef2213053da0e6d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a0a494460687dc2f027abe1f8262dc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a0a494460687dc2f027abe1f8262dc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a0a494460687dc2f027abe1f8262dc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33f69ab34d9d0b15ca9af1efbb481dc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
