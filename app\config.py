# app/config.py

import os
from dotenv import load_dotenv

# Carga las variables de entorno del archivo .env
basedir = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(basedir, '..', '.env')) # Sube un nivel para encontrar .env

class Config:
    """Clase base de configuración."""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'una-clave-secreta-muy-dificil-de-adivinar'
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'otra-clave-jwt-muy-secreta'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    # Añadir otras configuraciones globales aquí

class DevelopmentConfig(Config):
    """Configuración para desarrollo."""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        f"sqlite:///{os.path.join(basedir, 'dev_app.db')}" # Fallback a SQLite para dev fácil
    print(f"INFO: Usando base de datos: {SQLALCHEMY_DATABASE_URI}")


class TestingConfig(Config):
    """Configuración para testing."""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('TEST_DATABASE_URL') or 'sqlite:///:memory:' # BD en memoria para tests
    WTF_CSRF_ENABLED = False # Deshabilitar CSRF en tests

class ProductionConfig(Config):
    """Configuración para producción."""
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    if not SQLALCHEMY_DATABASE_URI:
        raise ValueError("No DATABASE_URL set for production")
    # Configuraciones adicionales de producción (logging, seguridad, etc.)

# Mapeo para seleccionar la configuración basada en FLASK_ENV
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}