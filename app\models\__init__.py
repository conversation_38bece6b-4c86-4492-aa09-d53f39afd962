# app/models/__init__.py

# Importa las clases de modelos para que SQLAlchemy las conozca
# El orden puede ser importante. Importa primero las clases
# que son referenciadas por otras (ej: User antes que Shift o Novelty).
from .user import User
from .novelty import Novelty, NoveltyReadStatus
from .message import OperatorMessage, NotificationStatus
from .shift import Shift
from .location import GPSTrackPoint
from .identification import IdentifiedPerson, IdentifiedVehicle

# Puedes añadir un __all__ si quieres ser explícito sobre qué se exporta,
# pero no es estrictamente necesario para que SQLAlchemy los encuentre.
# __all__ = [
#     'User', 'Novelty', 'NoveltyReadStatus', 'Shift',
#     'GPSTrackPoint', 'IdentifiedPerson', 'IdentifiedVehicle'
# ]