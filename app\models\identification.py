# app/models/identification.py

from datetime import datetime
from app import db

class IdentifiedPerson(db.Model):
    __tablename__ = 'identified_persons'
    id = db.Column(db.Integer, primary_key=True)
    shift_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('shifts.id'), nullable=False, index=True)
    officer_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    location_lat = db.Column(db.Float)
    location_lon = db.Column(db.Float)

    dni_id_tramite = db.Column(db.String(50))
    last_name = db.Column(db.String(100), index=True)
    first_names = db.Column(db.String(150))
    gender = db.Column(db.String(10))
    dni_number = db.Column(db.String(20), index=True)
    dni_copy = db.Column(db.String(10))
    dob = db.Column(db.String(20))
    issue_date = db.Column(db.String(20))
    cuil_or_id = db.Column(db.String(50))
    dni_raw_string = db.Column(db.Text)
    notes = db.Column(db.Text)

    officer = db.relationship('User', back_populates='identifications_person')

    def __repr__(self):
        return f'<IdentifiedPerson {self.id} (DNI: {self.dni_number}) by Officer {self.officer_id}>'

class IdentifiedVehicle(db.Model):
    __tablename__ = 'identified_vehicles'
    id = db.Column(db.Integer, primary_key=True)
    shift_id = db.Column(db.Integer, db.ForeignKey('shifts.id'), nullable=False, index=True)
    officer_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    location_lat = db.Column(db.Float)
    location_lon = db.Column(db.Float)

    plate = db.Column(db.String(20), nullable=False, index=True)
    vehicle_type = db.Column(db.String(50))
    brand = db.Column(db.String(50))
    model = db.Column(db.String(50))
    color = db.Column(db.String(30))
    image_url = db.Column(db.String(255))
    notes = db.Column(db.Text)

    officer = db.relationship('User', back_populates='identifications_vehicle')

    def __repr__(self):
        return f'<IdentifiedVehicle {self.id} (Plate: {self.plate}) by Officer {self.officer_id}>'

class IdentifiedLicense(db.Model):
    __tablename__ = 'identified_licenses'

    id = db.Column(db.Integer, primary_key=True)
    shift_id = db.Column(db.Integer, db.ForeignKey('shifts.id'), nullable=False, index=True)
    officer_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    location_lat = db.Column(db.Float)
    location_lon = db.Column(db.Float)

    license_number = db.Column(db.String(50), nullable=False, index=True)
    category       = db.Column(db.String(20))
    issue_date     = db.Column(db.Date)
    expiry_date    = db.Column(db.Date)
    raw_data       = db.Column(db.Text)
    notes          = db.Column(db.Text)

    # Relaciones inversas
    officer = db.relationship(
        'User',
        back_populates='identifications_license'
    )
    shift = db.relationship(
        'Shift',
        back_populates='identified_licenses'
    )

    def __repr__(self):
        return f'<IdentifiedLicense {self.id} (Lic: {self.license_number})>'