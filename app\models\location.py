# app/models/location.py

from datetime import datetime
from app import db

class GPSTrackPoint(db.Model):
    __tablename__ = 'gps_track_points'
    id = db.Column(db.Integer, primary_key=True)
    shift_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('shifts.id'), nullable=False, index=True)
    latitude = db.Column(db.Float, nullable=False)
    longitude = db.Column(db.Float, nullable=False)
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, index=True)
    accuracy = db.Column(db.Float)

    shift = db.relationship('Shift', back_populates='gps_track_points')

    def __repr__(self):
        return f'<GPSTrackPoint {self.id} for Shift {self.shift_id} at {self.timestamp}>'
