# app/models/message.py

from datetime import datetime
from .. import db

class OperatorMessage(db.Model):
    """Modelo para almacenar mensajes del operador a los agentes"""
    __tablename__ = 'operator_messages'
    
    id = db.Column(db.Integer, primary_key=True)
    # Mensaje
    content = db.Column(db.Text, nullable=False)
    # Quién envió el mensaje
    sender_user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    # A quién va dirigido (folio del agente)
    recipient_folio = db.Column(db.String(80), db.<PERSON><PERSON>ey('users.folio'), nullable=False, index=True)
    # Tipo de mensaje
    message_type = db.Column(db.String(20), nullable=False, default='personal', index=True)  # personal, broadcast, multiple
    # Estado
    is_read = db.Column(db.<PERSON>, default=False, index=True)
    sent_via_websocket = db.Column(db.<PERSON>olean, default=False)
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    read_at = db.Column(db.DateTime)
    
    # Relaciones
    sender = db.relationship('User', foreign_keys=[sender_user_id], backref='sent_messages')
    recipient = db.relationship('User', foreign_keys=[recipient_folio], backref='received_messages')
    
    def __repr__(self):
        return f'<OperatorMessage {self.id}: {self.sender.folio if self.sender else "Unknown"} -> {self.recipient_folio}>'
    
    def to_dict(self):
        """Convierte el mensaje a diccionario para JSON"""
        return {
            'id': self.id,
            'content': self.content,
            'sender_folio': self.sender.folio if self.sender else None,
            'sender_name': self.sender.name if self.sender else None,
            'recipient_folio': self.recipient_folio,
            'message_type': self.message_type,
            'is_read': self.is_read,
            'sent_via_websocket': self.sent_via_websocket,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'read_at': self.read_at.isoformat() if self.read_at else None
        }
    
    @classmethod
    def get_unread_for_user(cls, folio):
        """Obtiene mensajes no leídos para un usuario específico"""
        return cls.query.filter_by(
            recipient_folio=folio,
            is_read=False
        ).order_by(cls.created_at.desc()).all()
    
    @classmethod
    def mark_as_read(cls, message_id, folio):
        """Marca un mensaje como leído"""
        message = cls.query.filter_by(
            id=message_id,
            recipient_folio=folio
        ).first()
        
        if message and not message.is_read:
            message.is_read = True
            message.read_at = datetime.utcnow()
            db.session.commit()
            return True
        return False
    
    @classmethod
    def mark_all_as_read(cls, folio):
        """Marca todos los mensajes como leídos para un usuario"""
        messages = cls.query.filter_by(
            recipient_folio=folio,
            is_read=False
        ).all()
        
        count = 0
        for message in messages:
            message.is_read = True
            message.read_at = datetime.utcnow()
            count += 1
        
        if count > 0:
            db.session.commit()
        
        return count


class NotificationStatus(db.Model):
    """Modelo para rastrear el estado de notificaciones de novedades"""
    __tablename__ = 'notification_status'
    
    id = db.Column(db.Integer, primary_key=True)
    user_folio = db.Column(db.String(80), db.ForeignKey('users.folio'), nullable=False, index=True)
    # Última novedad que el usuario vio
    last_seen_novelty_id = db.Column(db.Integer, db.ForeignKey('novelties.id'), nullable=True)
    # Última vez que se verificaron las novedades
    last_check_at = db.Column(db.DateTime, default=datetime.utcnow)
    # Última vez que se envió una notificación
    last_notification_at = db.Column(db.DateTime)
    
    # Relaciones
    user = db.relationship('User', backref='notification_status')
    last_seen_novelty = db.relationship('Novelty')
    
    def __repr__(self):
        return f'<NotificationStatus {self.user_folio}: last_novelty={self.last_seen_novelty_id}>'
    
    @classmethod
    def get_or_create_for_user(cls, folio):
        """Obtiene o crea el estado de notificaciones para un usuario"""
        status = cls.query.filter_by(user_folio=folio).first()
        if not status:
            status = cls(user_folio=folio)
            db.session.add(status)
            db.session.commit()
        return status
    
    @classmethod
    def update_last_seen_novelty(cls, folio, novelty_id):
        """Actualiza la última novedad vista por el usuario"""
        status = cls.get_or_create_for_user(folio)
        status.last_seen_novelty_id = novelty_id
        status.last_check_at = datetime.utcnow()
        db.session.commit()
        return status
    
    @classmethod
    def should_notify_new_novelties(cls, folio):
        """Determina si se debe notificar sobre nuevas novedades"""
        from .novelty import Novelty
        
        status = cls.get_or_create_for_user(folio)
        
        # Obtener la novedad más reciente
        latest_novelty = Novelty.query.order_by(Novelty.created_at.desc()).first()
        
        if not latest_novelty:
            return False, 0
        
        # Si no hay última novedad vista, hay novedades nuevas
        if not status.last_seen_novelty_id:
            # Contar todas las novedades
            count = Novelty.query.count()
            return count > 0, count
        
        # Contar novedades más recientes que la última vista
        new_count = Novelty.query.filter(
            Novelty.id > status.last_seen_novelty_id
        ).count()
        
        return new_count > 0, new_count
