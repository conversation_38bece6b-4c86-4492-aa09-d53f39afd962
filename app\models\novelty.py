# app/models/novelty.py

import logging
from datetime import datetime
from app import db
# No es necesario importar User aquí si usamos el string 'User' en relationship

logger = logging.getLogger(__name__)

class Novelty(db.Model):
    __tablename__ = 'novelties'
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    image_url = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, index=True, default=datetime.utcnow)
    # Clave foránea al usuario que creó la novedad
    created_by_user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)

    # --- Relación Explícita al Autor ---
    # 'author_user' es el nombre del atributo en este modelo Novelty.
    # 'novelties_created' es el nombre del atributo en el modelo User que apunta de vuelta aquí.
    author_user = db.relationship('User', back_populates='novelties_created')

    # --- Relación con los Estados de Lectura ---
    # 'read_by_users' es el nombre del atributo aquí (lista de estados).
    # 'novelty' es el nombre del atributo en NoveltyReadStatus que apunta de vuelta aquí.
    read_by_users = db.relationship(
        'NoveltyReadStatus',
        back_populates='novelty',
        lazy='dynamic',
        cascade="all, delete-orphan" # Borrar estados si se borra la novedad
    )

    def __repr__(self):
        return f'<Novelty {self.id} by UserID {self.created_by_user_id}>'

class NoveltyReadStatus(db.Model):
    __tablename__ = 'novelty_read_status'
    id = db.Column(db.Integer, primary_key=True)
    # Clave foránea al usuario que leyó
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    # Clave foránea a la novedad leída
    novelty_id = db.Column(db.Integer, db.ForeignKey('novelties.id', ondelete='CASCADE'), nullable=False, index=True) # Borrar si la novedad se borra
    read_at = db.Column(db.DateTime, default=datetime.utcnow)

    # --- Relación explícita de vuelta a User y Novelty ---
    reader = db.relationship('User', back_populates='novelties_read_statuses')
    novelty = db.relationship('Novelty', back_populates='read_by_users')

    # Constraint para asegurar que un usuario no marque la misma novedad dos veces
    __table_args__ = (db.UniqueConstraint('user_id', 'novelty_id', name='_user_novelty_uc'),)

    def __repr__(self):
        return f'<NoveltyReadStatus User {self.user_id} read Novelty {self.novelty_id}>'