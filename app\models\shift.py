# app/models/shift.py

from datetime import datetime
from app import db
# Importar modelos relacionados explícitamente si se usan tipos en relationships
# (No estrictamente necesario aquí porque usamos strings y back_populates)
# from app.models.user import User
# from app.models.location import GPSTrackPoint
# from app.models.identification import IdentifiedPerson, IdentifiedVehicle

class Shift(db.Model):
    __tablename__ = 'shifts'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    start_time = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, index=True)
    end_time = db.Column(db.DateTime, index=True)
    mobility_type = db.Column(db.String(50), nullable=False)
    estimated_duration = db.Column(db.Integer)
    unit_id = db.Column(db.String(50))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    total_distance_km = db.Column(db.Float, default=0.0)
    person_id_count = db.Column(db.Integer, default=0)
    vehicle_id_count = db.Column(db.Integer, default=0)

    # Relaciones (usando back_populates donde sea bidireccional)
    officer = db.relationship('User', back_populates='shifts')
    gps_track_points = db.relationship('GPSTrackPoint', back_populates='shift', lazy='dynamic', cascade="all, delete-orphan")
    # Para estas, si User no tiene back_populates 'identifications_person/vehicle', backref está bien
    identifications_person = db.relationship('IdentifiedPerson', backref='shift', lazy='dynamic')
    identifications_vehicle = db.relationship('IdentifiedVehicle', backref='shift', lazy='dynamic')
    identified_licenses = db.relationship('IdentifiedLicense',back_populates='shift',lazy='dynamic',cascade='all, delete-orphan')
    @property
    def is_active(self):
        return self.end_time is None

    def __repr__(self):
        status = "Active" if self.is_active else f"Ended at {self.end_time}"
        return f'<Shift {self.id} by User {self.user_id} ({status})>'

# --- LÍNEA ELIMINADA ---
# from app.models.location import GPSTrackPoint # <-- Eliminada