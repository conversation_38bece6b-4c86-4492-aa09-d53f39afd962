# app/models/user.py

from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from app import db
# No es necesario importar los otros modelos aquí si usamos strings en relationship

class User(db.Model):
    __tablename__ = 'users'
    id = db.Column(db.Integer, primary_key=True)
    folio = db.Column(db.String(80), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(256), nullable=False) # Verificado que es 256
    name = db.Column(db.String(100))
    role = db.Column(db.String(20), nullable=False, default='agente', index=True)
    unit_id = db.Column(db.String(50))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.<PERSON>, default=True)

    # --- Relaciones Bidireccionales Corregidas ---
    # Relación con los turnos que este usuario realizó
    shifts = db.relationship('Shift', back_populates='officer', lazy='dynamic')

    # Relación con las novedades creadas por este usuario
    novelties_created = db.relationship(
        'Novelty',
        back_populates='author_user', # Coincide con Novelty.author_user
        lazy='dynamic',
        foreign_keys='Novelty.created_by_user_id' # Especificar FK explícitamente
    )

    # Relación con los estados de lectura de novedades de este usuario
    novelties_read_statuses = db.relationship(
        'NoveltyReadStatus',
        back_populates='reader', # Coincide con NoveltyReadStatus.reader
        lazy='dynamic'
        # cascade="all, delete-orphan" # Opcional: borrar estados si se borra el usuario
    )

    # Relación con las identificaciones de personas realizadas por este oficial
    identifications_person = db.relationship(
        'IdentifiedPerson',
        back_populates='officer', # Coincide con IdentifiedPerson.officer
        lazy='dynamic',
        foreign_keys='IdentifiedPerson.officer_id'
    )

    # Relación con las identificaciones de vehículos realizadas por este oficial
    identifications_vehicle = db.relationship(
        'IdentifiedVehicle',
        back_populates='officer', # Coincide con IdentifiedVehicle.officer
        lazy='dynamic',
        foreign_keys='IdentifiedVehicle.officer_id'
    )

        # Relación inversa con IdentifiedLicense
    identifications_license = db.relationship(
        'IdentifiedLicense',
        back_populates='officer',
        cascade="all, delete-orphan"
    )

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.folio} ({self.role})>'

# --- Nota Adicional ---
# Para que los back_populates de 'identifications_person' e 'identifications_vehicle' funcionen,
# DEBES asegurarte de que en app/models/identification.py tengas definidas las relaciones inversas:
# En IdentifiedPerson:
#   officer = db.relationship('User', back_populates='identifications_person')
# En IdentifiedVehicle:
#   officer = db.relationship('User', back_populates='identifications_vehicle')
# También necesitarás relaciones inversas para Shift <-> IdentifiedPerson/Vehicle si usas back_populates en ambos lados.