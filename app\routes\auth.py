# app/routes/auth.py
from flask import Blueprint, request, jsonify
from app import db, jwt
from app.models.user import User
from app.schemas.user_schema import user_schema # Para la respuesta
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from datetime import timedelta

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    if not data or not data.get('folio') or not data.get('password'):
        return jsonify({"msg": "Falta folio o contraseña"}), 400

    user = User.query.filter_by(folio=data['folio']).first()

    if user and user.check_password(data['password']):
        if not user.is_active:
             return jsonify({"msg": "Usuario inactivo"}), 401

        identity_to_store = user.folio

        # Define los claims adicionales ANTES de llamar a create_access_token
        additional_claims = {"role": user.role, "user_id": user.id}

        # --- UNA SOLA LLAMADA A create_access_token ---
        access_token = create_access_token(
            identity=identity_to_store,                 # Argumento 1
            expires_delta=timedelta(hours=8),           # Argumento 2
            additional_claims=additional_claims         # Argumento 3 (SIN coma si es el último)
        )
        # --- FIN DE LA LLAMADA ---

        user_info = user_schema.dump(user)
        return jsonify(access_token=access_token, user=user_info), 200
    else:
        return jsonify({"msg": "Folio o contraseña incorrectos"}), 401

@auth_bp.route('/me', methods=['GET'])
@jwt_required()
def get_me():
    current_user_folio = get_jwt_identity() # Ahora esto es el FOLIO del usuario
    # Buscar usuario por FOLIO
    user = User.query.filter_by(folio=current_user_folio).first()
    if not user:
         return jsonify({"msg": "Usuario no encontrado (token inválido?)"}), 404
    return jsonify(logged_in_as=user_schema.dump(user)), 200