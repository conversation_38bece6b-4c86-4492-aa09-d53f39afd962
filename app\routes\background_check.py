# app/routes/background_check.py
import logging # Añadir logging
from flask import Blueprint, request, jsonify
import requests
from flask_jwt_extended import jwt_required, get_jwt_identity
import random
from datetime import datetime # Importar datetime

# Logger
logger = logging.getLogger(__name__)

background_check_bp = Blueprint('background_check', __name__)

@background_check_bp.route('', methods=['GET'])
@jwt_required()
def check_background():
    """Consulta antecedentes por DNI o Patente."""
    current_user_folio = get_jwt_identity() # Obtiene FOLIO (string)
    officer_folio = current_user_folio # El folio es la identidad ahora

    dni = request.args.get('dni')
    plate = request.args.get('plate')
    if not dni and not plate: return jsonify({"msg": "Se requiere 'dni' o 'plate'"}), 400

    # --- SIMULACIÓN ---
    logger.info(f"Oficial {officer_folio} consultando antecedentes DNI={dni}, Plate={plate}")
    has_record = random.choice([True, False, False])
    record_details = ""
    status_code = 200
    if has_record:
         if dni: record_details = f"Persona con DNI {dni} presenta pedido captura Juzg. {random.randint(1,10)}."
         elif plate: record_details = f"Vehículo dominio {plate} posee pedido secuestro."
    else:
        if dni: record_details = f"Sin antecedentes ni impedimentos para DNI {dni}."
        elif plate: record_details = f"Sin impedimentos para dominio {plate}."

    mock_response = {
        "query": {"dni": dni, "plate": plate}, "timestamp": datetime.utcnow().isoformat(),
        "has_record": has_record, "details": record_details,
        "source": "Sistema Central (SIMULADO)"
    }
    # --- FIN SIMULACIÓN ---

    # Lógica API Real (comentada) iría aquí...

    return jsonify(mock_response), status_code