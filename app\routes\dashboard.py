# app/routes/dashboard.py

import logging
from functools import wraps
from datetime import datetime, timedelta # Importar datetime
from flask import (
    Blueprint, render_template, abort, request, jsonify, current_app, url_for, redirect
)
# Imports absolutos
from app.models.shift import Shift
from app.models.user import User
from app.models.location import GPSTrackPoint
from app.models.identification import Identified<PERSON>erson, IdentifiedVehicle
# Importar shift_schema (singular) y shifts_schema (plural)
from app.schemas.shift_schema import shift_schema, shifts_schema
from app.schemas.location_schema import gps_track_points_schema
from app.schemas.identification_schema import identified_person_schema, identified_vehicle_schema
from app.schemas.user_schema import user_schema, users_schema
from app import db, socketio
from sqlalchemy import func, or_, desc, exc as sqlalchemy_exc
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.utils.stats_calculator import calculate_shift_stats
from flask_socketio import join_room

# Importar dateutil de forma segura
try:
    from dateutil import parser
except ImportError:
    parser = None
    logging.warning("python-dateutil no está instalado. Los filtros de fecha pueden fallar.")

# Configuración del Logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO) # Ajustar a DEBUG si necesitas más detalle

# --- Blueprints ---
dashboard_web_bp = Blueprint(
    'dashboard', __name__, template_folder='../templates', url_prefix='/dashboard'
)
dashboard_api_bp = Blueprint(
    'dashboard_api', __name__, url_prefix='/api/dashboard'
)

# --- Decorador supervisor_required (Usa folio y busca User) ---
def supervisor_required(fn):
    @wraps(fn)
    @jwt_required()
    def wrapper(*args, **kwargs):
        current_user_folio = get_jwt_identity()
        user = User.query.filter_by(folio=current_user_folio).first()
        if not user or user.role not in ['supervisor', 'comando']:
            logger.warning(f"Acceso API denegado. Folio:{current_user_folio}, Rol:{user.role if user else 'N/A'}")
            abort(403)
        kwargs['current_supervisor'] = user
        return fn(*args, **kwargs)
    return wrapper

# --- Función Helper para Formatear Fecha ---
def format_datetime_local(dt_object):
    """Formatea un objeto datetime a string local o devuelve 'N/A'."""
    if dt_object is None:
        return 'N/A'
    try:
        return dt_object.strftime('%d/%m/%Y %H:%M')
    except Exception as e:
        logger.error(f"Error formateando fecha {dt_object}: {e}")
        return str(dt_object)

# --- Rutas de VISTAS WEB ---

@dashboard_web_bp.route('/login')
def login_view():
    return render_template('auth/login.html')

@dashboard_web_bp.route('/')
def index():
    return render_template('dashboard/index.html', user_info=None)

@dashboard_web_bp.route('/mapa-vivo')
def live_map_view():
    return render_template('dashboard/live_map.html', user_info=None)

@dashboard_web_bp.route('/turnos')
def shifts_view():
    return render_template('dashboard/shifts.html', user_info=None)

@dashboard_web_bp.route('/turnos/<int:shift_id>')
def shift_detail_view(shift_id):
    """Vista detallada de un turno específico."""
    try:
        shift = Shift.query.options(db.joinedload(Shift.officer)).get_or_404(shift_id)
        formatted_shift_data = {
            'id': shift.id,
            'officer_folio': shift.officer.folio if shift.officer else 'N/A',
            'officer_name': shift.officer.name if shift.officer else 'N/A',
            'start_time_str': format_datetime_local(shift.start_time),
            'end_time_str': format_datetime_local(shift.end_time) if shift.end_time else 'En curso',
            'mobility_type': shift.mobility_type,
            'unit_id': shift.unit_id or 'N/A',
            'is_active': shift.is_active,
            'total_distance_km': shift.total_distance_km,
            'person_id_count': shift.person_id_count,
            'vehicle_id_count': shift.vehicle_id_count
        }
        return render_template('dashboard/shift_detail.html',
                               shift_data=formatted_shift_data,
                               shift_id=shift_id,
                               user_info=None)
    except Exception as e:
         logger.error(f"Error al cargar detalle turno {shift_id}: {e}", exc_info=True)
         abort(500)

@dashboard_web_bp.route('/identificaciones')
def identifications_view():
    return render_template('dashboard/identifications.html', user_info=None)

@dashboard_web_bp.route('/novedades')
def novelties_view():
    return render_template('dashboard/novelties.html', user_info=None)

@dashboard_web_bp.route('/usuarios')
def users_view():
    return render_template('dashboard/users.html', user_info=None)


# --- Rutas API ---

@dashboard_api_bp.route('/live-agents', methods=['GET'])
@supervisor_required
def get_live_agents_location(current_supervisor):
    """API: Obtiene la última ubicación conocida de los agentes activos."""
    logger.info(f"Supervisor {current_supervisor.folio} obteniendo agentes en vivo...")
    try:
        limit = request.args.get('limit', default=200, type=int)

        latest_point_subq = db.session.query(
            GPSTrackPoint.shift_id,
            func.max(GPSTrackPoint.timestamp).label('latest_timestamp')
        ).group_by(GPSTrackPoint.shift_id).subquery()

        live_data_query = (
            db.session.query(
                User.folio, User.name, Shift.id.label('shift_id'), Shift.mobility_type,
                GPSTrackPoint.latitude, GPSTrackPoint.longitude,
                GPSTrackPoint.timestamp.label('last_update'), GPSTrackPoint.accuracy
            )
            .select_from(Shift)
            .join(User, User.id == Shift.user_id)
            .join(latest_point_subq, Shift.id == latest_point_subq.c.shift_id)
            .join(GPSTrackPoint, (GPSTrackPoint.shift_id == latest_point_subq.c.shift_id) & \
                                  (GPSTrackPoint.timestamp == latest_point_subq.c.latest_timestamp))
            .filter(Shift.end_time.is_(None))
            #.filter(User.unit_id == current_supervisor.unit_id) # Descomentar si es por unidad
            .order_by(GPSTrackPoint.timestamp.desc())
            .limit(limit)
        )

        live_data = live_data_query.all()
        result = [{
            "folio": f, "name": n, "shift_id": sid, "mobility": m,
            "latitude": lat, "longitude": lon,
            "last_update": lu.isoformat() if lu else None, "accuracy": acc
        } for f, n, sid, m, lat, lon, lu, acc in live_data]
        return jsonify(result), 200

    except Exception as e:
        logger.error(f"Error en /live-agents por {current_supervisor.folio}: {e}", exc_info=True)
        return jsonify({"msg": "Error al obtener datos de agentes en vivo"}), 500


@dashboard_api_bp.route('/shifts-history', methods=['GET'])
@supervisor_required
def get_shifts_history(current_supervisor):
    """API: Obtiene un historial paginado de jornadas con filtros."""
    logger.info(f"Supervisor {current_supervisor.folio} obteniendo historial de turnos...")
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 15, type=int)
        folio_filter = request.args.get('folio')
        date_from_str = request.args.get('date_from')
        date_to_str = request.args.get('date_to')
        status_filter = request.args.get('status')

        query = (
            Shift.query
            .join(User, Shift.user_id == User.id)
            .options(db.joinedload(Shift.officer))
            .order_by(Shift.start_time.desc())
        )

        # Aplicar Filtros
        if folio_filter:
            query = query.filter(User.folio.ilike(f"%{folio_filter}%"))

        if date_from_str and parser:
            try:
                date_from = parser.parse(date_from_str).replace(hour=0, minute=0, second=0)
                query = query.filter(Shift.start_time >= date_from)
            except ValueError:
                logger.warning(f"Formato inválido date_from: {date_from_str}")
                return jsonify({"msg": "Formato de fecha 'desde' inválido."}), 400

        if date_to_str and parser:
             try:
                date_to = parser.parse(date_to_str).replace(hour=23, minute=59, second=59)
                query = query.filter(Shift.start_time <= date_to)
             except ValueError:
                logger.warning(f"Formato inválido date_to: {date_to_str}")
                return jsonify({"msg": "Formato de fecha 'hasta' inválido."}), 400

        if status_filter == 'active':
            query = query.filter(Shift.end_time.is_(None))
        elif status_filter == 'finished':
            query = query.filter(Shift.end_time.isnot(None))

        paginated_shifts = query.paginate(page=page, per_page=per_page, error_out=False)
        results = shifts_schema.dump(paginated_shifts.items)

        return jsonify({
            "shifts": results,
            "total": paginated_shifts.total,
            "pages": paginated_shifts.pages,
            "current_page": page
        }), 200
    except Exception as e:
        folio = getattr(current_supervisor, 'folio', 'unknown')
        logger.error(f"Error en /shifts-history por {folio}: {e}", exc_info=True)
        return jsonify({"msg": "Error al obtener historial de turnos"}), 500

@dashboard_api_bp.route('/shifts/<int:shift_id>/trackpoints', methods=['GET'])
@supervisor_required
def get_shift_trackpoints(current_supervisor, shift_id):
    """API: Obtiene los puntos GPS ordenados para un turno específico."""
    logger.info(f"Supervisor {current_supervisor.folio} obteniendo trackpoints para turno {shift_id}...")
    try:
        shift = Shift.query.get_or_404(shift_id)
        trackpoints = GPSTrackPoint.query.filter_by(shift_id=shift_id)\
                                         .order_by(GPSTrackPoint.timestamp.asc()).all()
        logger.info(f"Encontrados {len(trackpoints)} trackpoints para turno {shift_id}")
        return jsonify(gps_track_points_schema.dump(trackpoints)), 200
    except Exception as e:
        logger.error(f"Error en /shifts/{shift_id}/trackpoints por {current_supervisor.folio}: {e}", exc_info=True)
        return jsonify({"msg": f"Error al obtener trackpoints para turno {shift_id}"}), 500

@dashboard_api_bp.route('/identifications-list', methods=['GET'])
@supervisor_required
def get_identifications_list(current_supervisor):
    logger.info(f"Supervisor {current_supervisor.folio} obteniendo lista de identificaciones...")
    try:
        page          = request.args.get('page', 1, type=int)
        per_page      = request.args.get('per_page', 15, type=int)
        type_filter   = request.args.get('type')  # 'person', 'vehicle' o None
        officer_folio = request.args.get('officer_folio', '').strip()
        shift_id      = request.args.get('shift_id', type=int)
        date_from_str = request.args.get('date_from')
        date_to_str   = request.args.get('date_to')

        # <-- Aquí recogemos cualquiera de los dos nombres de campo -->
        doc_or_plate  = (request.args.get('doc_or_plate') or request.args.get('id') or '').strip().lower()
        name_or_brand = (request.args.get('name_or_brand') or request.args.get('name') or '').strip().lower()

        items = []

        # — FILTRADO DE PERSONAS —
        if type_filter in [None, 'person']:
            q = IdentifiedPerson.query.options(db.joinedload(IdentifiedPerson.officer))
            if officer_folio:
                q = q.join(User).filter(User.folio.ilike(f"%{officer_folio}%"))
            if shift_id:
                q = q.filter(IdentifiedPerson.shift_id == shift_id)
            if date_from_str and parser:
                df = parser.parse(date_from_str).replace(hour=0, minute=0, second=0)
                q = q.filter(IdentifiedPerson.timestamp >= df)
            if date_to_str and parser:
                dt = parser.parse(date_to_str).replace(hour=23, minute=59, second=59)
                q = q.filter(IdentifiedPerson.timestamp <= dt)

            for p in q.order_by(desc(IdentifiedPerson.timestamp)).all():
                if doc_or_plate and doc_or_plate not in (p.dni_number or "").lower():
                    continue
                if name_or_brand and not (
                    name_or_brand in (p.first_names or "").lower() or
                    name_or_brand in (p.last_name   or "").lower()
                ):
                    continue
                items.append({"timestamp": p.timestamp, "data": p, "is_person": True})

        # — FILTRADO DE VEHÍCULOS —
        if type_filter in [None, 'vehicle']:
            q = IdentifiedVehicle.query.options(db.joinedload(IdentifiedVehicle.officer))
            if officer_folio:
                q = q.join(User).filter(User.folio.ilike(f"%{officer_folio}%"))
            if shift_id:
                q = q.filter(IdentifiedVehicle.shift_id == shift_id)
            if date_from_str and parser:
                df = parser.parse(date_from_str).replace(hour=0, minute=0, second=0)
                q = q.filter(IdentifiedVehicle.timestamp >= df)
            if date_to_str and parser:
                dt = parser.parse(date_to_str).replace(hour=23, minute=59, second=59)
                q = q.filter(IdentifiedVehicle.timestamp <= dt)

            for v in q.order_by(desc(IdentifiedVehicle.timestamp)).all():
                if doc_or_plate and doc_or_plate not in (v.plate or "").lower():
                    continue
                if name_or_brand and not (
                    name_or_brand in (v.brand or "").lower() or
                    name_or_brand in (v.model or "").lower()
                ):
                    continue
                items.append({"timestamp": v.timestamp, "data": v, "is_person": False})

        # — COMBINAR, PAGINAR Y SERIALIZAR —
        items.sort(key=lambda x: x["timestamp"], reverse=True)
        total     = len(items)
        start     = (page - 1) * per_page
        end       = start + per_page
        paged     = items[start:end]
        pages     = (total + per_page - 1) // per_page if per_page > 0 else 0

        results = []
        for entry in paged:
            schema = identified_person_schema if entry["is_person"] else identified_vehicle_schema
            row    = schema.dump(entry["data"])
            row["is_person"] = entry["is_person"]
            results.append(row)

        return jsonify({
            "identifications": results,
            "total": total,
            "pages": pages,
            "current_page": page
        }), 200

    except Exception as e:
        logger.error(f"Error en /identifications-list por {current_supervisor.folio}: {e}", exc_info=True)
        return jsonify({"msg": "Error al obtener lista de identificaciones"}), 500

@dashboard_api_bp.route('/users', methods=['GET'])
@supervisor_required
def get_users_list(current_supervisor):
    """API: Obtiene una lista paginada de usuarios."""
    logger.info(f"Supervisor {current_supervisor.folio} obteniendo lista de usuarios...")
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 15, type=int)
        query = User.query.order_by(User.created_at.desc())
        paginated_users = query.paginate(page=page, per_page=per_page, error_out=False)
        logger.info(f"Encontrados {paginated_users.total} usuarios. Serializando página {page}...")
        try:
            results = users_schema.dump(paginated_users.items)
        except Exception as dump_error:
            logger.error(f"Error UserSchema dump: {dump_error}", exc_info=True)
            return jsonify({"msg": "Error al serializar datos de usuario."}), 500
        response_data = {"users": results, "total": paginated_users.total, "pages": paginated_users.pages, "current_page": page}
        return jsonify(response_data), 200
    except Exception as e:
        logger.error(f"Exception en GET /users por {current_supervisor.folio}: {type(e).__name__} - {e}", exc_info=True)
        return jsonify({"msg": "Error interno al obtener lista de usuarios"}), 500


@dashboard_api_bp.route('/users', methods=['POST'])
@supervisor_required
def create_dashboard_user(current_supervisor):
    """API: Crea un nuevo usuario desde el dashboard."""
    logger.info(f"Supervisor {current_supervisor.folio} intentando crear usuario...")
    data = request.get_json()
    required_fields = ['folio', 'name', 'password', 'role']
    if not data or not all(field in data and data[field] for field in required_fields):
        logger.warning("Petición incompleta crear usuario.")
        return jsonify({"msg": "Faltan datos requeridos (folio, name, password, role)."}), 400
    folio = data['folio'].strip(); password = data['password']; name = data['name'].strip()
    role = data['role']; unit_id = data.get('unit_id', '').strip() or None
    if role not in ['agente', 'supervisor', 'comando']:
        logger.warning(f"Rol inválido '{role}'.")
        return jsonify({"msg": f"Rol '{role}' inválido."}), 400
    if User.query.filter_by(folio=folio).first():
        logger.warning(f"Folio existente '{folio}'.")
        return jsonify({"msg": f"El folio '{folio}' ya está en uso."}), 409
    new_user = User(folio=folio, name=name, role=role, unit_id=unit_id, is_active=True)
    new_user.set_password(password)
    try:
        db.session.add(new_user)
        db.session.commit()
        logger.info(f"Usuario '{folio}' ({role}) creado por {current_supervisor.folio} (ID: {new_user.id}).")
        try:
            serialized_user = user_schema.dump(new_user)
        except Exception as dump_error:
             logger.error(f"Error dump nuevo usuario '{folio}': {dump_error}", exc_info=True)
             return jsonify({"msg": f"Usuario '{folio}' creado, error al preparar respuesta."}), 201
        return jsonify(serialized_user), 201
    except sqlalchemy_exc.IntegrityError as e:
        db.session.rollback()
        logger.error(f"IntegrityError crear usuario '{folio}': {e}", exc_info=True)
        return jsonify({"msg": "Error BD (folio duplicado?)."}), 409
    except Exception as e:
        db.session.rollback()
        logger.error(f"Exception crear usuario '{folio}': {type(e).__name__} - {e}", exc_info=True)
        return jsonify({"msg": "Error interno al guardar."}), 500

@dashboard_api_bp.route('/users/<int:user_id>', methods=['PUT'])
@supervisor_required
def update_dashboard_user(current_supervisor, user_id):
    """API: Actualiza un usuario existente."""
    user = User.query.get_or_404(user_id)
    data = request.get_json()
    required_fields = ['folio', 'name', 'role']
    if not all(field in data and data[field] for field in required_fields):
        return jsonify({"msg": "Faltan datos requeridos para actualizar."}), 400
    if data['folio'] != user.folio:
        if User.query.filter_by(folio=data['folio']).first():
            return jsonify({"msg": f"El folio '{data['folio']}' ya está en uso."}), 409
    user.folio = data['folio'].strip(); user.name = data['name'].strip();
    user.role = data['role']; user.unit_id = data.get('unit_id', None);
    try:
        db.session.commit()
        logger.info(f"Usuario ID {user_id} actualizado por {current_supervisor.folio}.")
        return jsonify(user_schema.dump(user)), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error actualizar user ID {user_id}: {e}", exc_info=True)
        return jsonify({"msg": "Error interno al actualizar."}), 500

@dashboard_api_bp.route('/users/<int:user_id>/toggle-status', methods=['PUT'])
@supervisor_required
def toggle_user_status(current_supervisor, user_id):
    """Activa o desactiva un usuario."""
    user = User.query.get_or_404(user_id)
    if user.role == 'comando' and current_supervisor.role != 'comando':
        logger.warning(f"Intento no autorizado por {current_supervisor.folio} de cambiar estado a comando {user.folio}.")
        return jsonify({"msg": "No puedes modificar el estado de un comando."}), 403
    user.is_active = not user.is_active;
    new_status = 'activado' if user.is_active else 'desactivado'
    try:
        db.session.commit()
        logger.info(f"Usuario {user.folio} (ID:{user_id}) {new_status} por {current_supervisor.folio}.")
        return jsonify({"msg": f"Usuario {new_status} correctamente."}), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error toggle status user ID {user_id}: {e}", exc_info=True)
        return jsonify({"msg": "Error interno al modificar estado."}), 500

@dashboard_api_bp.route('/users/<int:user_id>', methods=['DELETE'])
@supervisor_required
def delete_dashboard_user(current_supervisor, user_id):
    """Elimina un usuario permanentemente."""
    user = User.query.get_or_404(user_id)
    folio_deleted = user.folio # Guardar folio para log
    if user.role == 'comando' and current_supervisor.role != 'comando':
         logger.warning(f"Intento no autorizado por {current_supervisor.folio} de eliminar comando {user.folio}.")
         return jsonify({"msg": "No puedes eliminar 'comando'."}), 403
    # Podrías añadir verificación para no borrar el último comando/supervisor
    try:
        # Considerar qué pasa con los turnos/identificaciones/novedades de este usuario
        # Podrías anonimizarlos, ponerlos en cascada (peligroso) o impedir el borrado si tiene datos asociados.
        db.session.delete(user)
        db.session.commit()
        logger.info(f"Usuario {folio_deleted} (ID:{user_id}) eliminado por {current_supervisor.folio}.")
        return jsonify({"msg": f"Usuario '{folio_deleted}' eliminado."}), 200
    except sqlalchemy_exc.IntegrityError as e: # Si hay FKs que impiden borrar
         db.session.rollback()
         logger.error(f"IntegrityError al eliminar user ID {user_id}: {e}", exc_info=True)
         return jsonify({"msg": "No se puede eliminar el usuario, probablemente tiene registros asociados (turnos, etc.)."}), 409
    except Exception as e:
         db.session.rollback()
         logger.error(f"Error delete user ID {user_id}: {e}", exc_info=True)
         return jsonify({"msg": "Error interno al eliminar."}), 500

@dashboard_api_bp.route('/nearest-agents', methods=['POST'])
@supervisor_required
def get_nearest_agents(current_supervisor):
    """Devuelve agentes cercanos (requiere PostGIS)."""
    # ... (código sin cambios, ya corregido) ...
    logger.info(f"Supervisor {current_supervisor.folio} consultando agentes cercanos...")
    try:
        data = request.get_json(); lat = data.get('latitude'); lon = data.get('longitude'); max_distance = data.get('max_distance', 500);
        if lat is None or lon is None: return jsonify({"msg": "Faltan coordenadas"}), 400
        try: lat = float(lat); lon = float(lon); max_distance = float(max_distance); assert max_distance > 0
        except (ValueError, TypeError, AssertionError): return jsonify({"msg": "Parámetros inválidos"}), 400
        latest_point_subq = db.session.query(GPSTrackPoint.shift_id, func.max(GPSTrackPoint.timestamp).label('latest_timestamp')).group_by(GPSTrackPoint.shift_id).subquery()
        user_point = func.ST_SetSRID(func.ST_MakePoint(lon, lat), 4326); gps_table_point = func.ST_SetSRID(func.ST_MakePoint(GPSTrackPoint.longitude, GPSTrackPoint.latitude), 4326); distance_expr = func.ST_DistanceSphere(gps_table_point, user_point).label("distance")
        query = (db.session.query(User.folio, User.name, GPSTrackPoint.latitude, GPSTrackPoint.longitude, GPSTrackPoint.timestamp.label("last_update"), distance_expr).join(Shift, Shift.user_id == User.id).join(latest_point_subq, Shift.id == latest_point_subq.c.shift_id).join(GPSTrackPoint, (GPSTrackPoint.shift_id == latest_point_subq.c.shift_id) & (GPSTrackPoint.timestamp == latest_point_subq.c.latest_timestamp)).filter(Shift.end_time.is_(None)).filter(distance_expr <= max_distance).order_by(distance_expr.asc()).limit(10))
        agents = query.all();
        result = [{"folio": f, "name": n, "latitude": la, "longitude": lo, "last_update": lu.isoformat() if lu else None, "distance": round(d, 2) if d is not None else None} for f, n, la, lo, lu, d in agents]
        return jsonify({"agents": result}), 200
    except Exception as e:
        logger.error(f"Error en /nearest-agents: {e}", exc_info=True)
        if "function st_distancesphere" in str(e).lower() or "function st_makepoint" in str(e).lower(): return jsonify({"msg": "Error BD: PostGIS no habilitado?"}), 500
        return jsonify({"msg": "Error interno al obtener agentes cercanos"}), 500

@dashboard_api_bp.route('/shifts/<int:shift_id>/force-end', methods=['PUT'])
@supervisor_required
def force_end_shift(current_supervisor, shift_id):
    """API: Permite a un supervisor forzar la finalización de un turno activo."""
    logger.info(f"Supervisor {current_supervisor.folio} forzando fin de turno ID: {shift_id}")
    shift = Shift.query.get_or_404(shift_id)
    if shift.end_time is not None:
        logger.warning(f"Intento de forzar fin en turno {shift_id} ya finalizado.")
        return jsonify({"msg": "El turno ya estaba finalizado."}), 200

    shift.end_time = datetime.utcnow()
    stats = None
    try:
        logger.info(f"Calculando estadísticas para turno {shift_id} (forzado fin)...")
        stats = calculate_shift_stats(shift_id)
        if stats and hasattr(shift, 'total_distance_km'):
             shift.total_distance_km = stats["total_distance_km"]
             shift.person_id_count = stats["person_identifications"]
             shift.vehicle_id_count = stats["vehicle_identifications"]
             logger.info(f"Estadísticas guardadas para turno {shift_id}: {stats}")
        elif stats:
             logger.warning(f"Campos de stats no encontrados en Shift para turno {shift_id}.")
        else:
             logger.error(f"Fallo al calcular estadísticas para turno {shift_id} (forzado fin).")
    except Exception as stat_err:
         logger.error(f"Error calculando stats al forzar fin turno {shift_id}: {stat_err}", exc_info=True)

    try:
        db.session.add(shift)
        db.session.commit()
        logger.info(f"Turno ID: {shift_id} finalizado forzosamente por {current_supervisor.folio}.")
        try:
            serialized_shift = shift_schema.dump(shift) # Usar schema singular
        except Exception as dump_error:
             logger.error(f"Error dump shift {shift_id} after force-end: {dump_error}", exc_info=True)
             return jsonify({"msg": f"Turno {shift_id} finalizado, pero error al preparar respuesta."}), 200
        return jsonify(serialized_shift), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error DB al forzar fin turno {shift_id}: {e}", exc_info=True)
        return jsonify({"msg": "Error interno al forzar la finalización."}), 500

# --- Ruta agregada para stats de un turno ---
@dashboard_api_bp.route('/shifts/<int:shift_id>/stats', methods=['GET'])
@supervisor_required
def get_shift_stats(current_supervisor, shift_id):
    logger.info(f"Supervisor {current_supervisor.folio} solicitando stats para turno {shift_id}")
    shift = Shift.query.get_or_404(shift_id)

    stats = {
        "total_distance_km": shift.total_distance_km or 0.0,
        "calculated_on": "saved",
        "person_identifications": shift.person_id_count or 0,
        "vehicle_identifications": shift.vehicle_id_count or 0
    }

    return jsonify(stats), 200

@dashboard_api_bp.route('/identifications-24h', methods=['GET'])
def get_last_24h_identifications():
    officer_id = request.args.get('officer_id', type=int)
    exclude_id = request.args.get('exclude_id', type=int)
    if not officer_id:
        return jsonify([])

    desde = datetime.utcnow() - timedelta(hours=24)

    persons = IdentifiedPerson.query.filter(
        IdentifiedPerson.officer_id == officer_id,
        IdentifiedPerson.timestamp >= desde,
        IdentifiedPerson.id != exclude_id
    ).all()

    vehicles = IdentifiedVehicle.query.filter(
        IdentifiedVehicle.officer_id == officer_id,
        IdentifiedVehicle.timestamp >= desde,
        IdentifiedVehicle.id != exclude_id
    ).all()

    all_items = persons + vehicles
    all_items.sort(key=lambda x: x.timestamp, reverse=True)

    return jsonify([
        {
            "id": item.id,
            "location_lat": item.location_lat,
            "location_lon": item.location_lon
        } for item in all_items if item.location_lat and item.location_lon
    ])

@dashboard_web_bp.route('/identifications/person/<int:ident_id>')
def person_details(ident_id):
    ident = IdentifiedPerson.query.get_or_404(ident_id)
    age = None
    if ident.dob:
        try:
            dob_date = ident.dob if isinstance(ident.dob, datetime) else parser.parse(ident.dob).date()
            age = (datetime.utcnow().date() - dob_date).days // 365
        except Exception as e:
            logger.warning(f"No se pudo calcular edad para ID {ident.id}: {e}")
            age = None

    is_recent = ident.timestamp >= datetime.utcnow() - timedelta(hours=24) if ident.timestamp else False

    desde = datetime.utcnow() - timedelta(hours=24)
    other_items = IdentifiedPerson.query.filter(
        IdentifiedPerson.officer_id == ident.officer_id,
        IdentifiedPerson.timestamp >= desde,
        IdentifiedPerson.id != ident.id
    ).all()

    return render_template(
        'dashboard/identifications_details.html',
        identification=ident,
        age=age,
        identified_recently='Sí' if is_recent else 'No',
        other_locations=[{
            "id": i.id,
            "lat": i.location_lat,
            "lon": i.location_lon,
            "timestamp": i.timestamp.isoformat(),
            "officer_folio": i.officer.folio if i.officer else 'N/A'
        } for i in other_items if i.location_lat and i.location_lon]
    )

@dashboard_web_bp.route('/identifications/vehicle/<int:ident_id>')
def vehicle_details(ident_id):
    ident = IdentifiedVehicle.query.get_or_404(ident_id)
    is_recent = ident.timestamp >= datetime.utcnow() - timedelta(hours=24) if ident.timestamp else False

    desde = datetime.utcnow() - timedelta(hours=24)
    other_items = IdentifiedVehicle.query.filter(
        IdentifiedVehicle.officer_id == ident.officer_id,
        IdentifiedVehicle.timestamp >= desde,
        IdentifiedVehicle.id != ident.id
    ).all()

    return render_template(
        'dashboard/identifications_details.html',
        identification=ident,
        age=None,
        identified_recently='Sí' if is_recent else 'No',
        other_locations=[{
            "id": i.id,
            "lat": i.location_lat,
            "lon": i.location_lon,
            "timestamp": i.timestamp.isoformat(),
            "officer_folio": i.officer.folio if i.officer else 'N/A'
        } for i in other_items if i.location_lat and i.location_lon]
    )

@dashboard_api_bp.route('/active-tracks', methods=['GET'])
@supervisor_required
def get_active_tracks(current_supervisor):
    """
    Devuelve para cada agente activo (shift.end_time es None)
    sus puntos GPS de los últimos 30 minutos, en orden ascendente.
    """
    cutoff = datetime.utcnow() - timedelta(minutes=30)

    # Traer shifts activos con su oficial
    active_shifts = (
        Shift.query
        .filter(Shift.end_time.is_(None))
        .join(User, Shift.user_id == User.id)
        .options(db.joinedload(Shift.officer))
        .all()
    )

    result = []
    for shift in active_shifts:
        # Obtener trackpoints recientes
        tps = (
            GPSTrackPoint.query
            .filter_by(shift_id=shift.id)
            .filter(GPSTrackPoint.timestamp >= cutoff)
            .order_by(GPSTrackPoint.timestamp.asc())
            .all()
        )
        track = [
            {
                "latitude": p.latitude,
                "longitude": p.longitude,
                "timestamp": p.timestamp.isoformat()
            } for p in tps
        ]
        result.append({
            "folio": shift.officer.folio,
            "name": shift.officer.name,
            "shift_id": shift.id,
            "mobility": shift.mobility_type,
            "track": track
        })

    return jsonify(result), 200

# ——— Mapa Histórico ————————————————
@dashboard_web_bp.route('/mapa-historico')
def historico_map_view():
    """Vista del Mapa Histórico con filtros de fecha, agentes y tipo de identificación."""
    return render_template('dashboard/historico_map.html', user_info=None)


# ——— Endpoints para Mapa Histórico ————————————————

@dashboard_api_bp.route('/historical-agents', methods=['GET'])
@supervisor_required
def get_historical_agents(current_supervisor):
    """
    Devuelve la lista de agentes (folio+nombre) que tuvieron un turno activo
    en algún momento dentro del rango [date_from, date_to].
    """
    df = request.args.get('date_from')
    dt = request.args.get('date_to')

    # 1) Validación de parámetros
    if not df or not dt:
        return jsonify({"msg": "Faltan parámetros 'date_from' o 'date_to'"}), 400
    if not parser:
        return jsonify({"msg": "dateutil no instalado en servidor"}), 500

    # 2) Parseo seguro
    try:
        date_from = parser.parse(df)
        date_to   = parser.parse(dt)
    except Exception:
        return jsonify({"msg": "Formato de fecha inválido"}), 400

    logger.info(f"Supervisor {current_supervisor.folio} solicitando agentes históricos {df} → {dt}")

    # 3) Query
    subq = (
        db.session.query(User.folio, User.name)
        .join(Shift, Shift.user_id == User.id)
        .filter(Shift.start_time <= date_to)
        .filter(
            or_(
                Shift.end_time.is_(None),
                Shift.end_time >= date_from
            )
        )
        .distinct()
        .all()
    )
    agents = [{"folio": f, "name": n} for f, n in subq]
    return jsonify(agents), 200


@dashboard_api_bp.route('/historical-tracks', methods=['GET'])
@supervisor_required
def get_historical_tracks(current_supervisor):
    """API: Devuelve los recorridos de cada agente en un rango de fecha/hora."""
    df = request.args.get('date_from')
    dt = request.args.get('date_to')

    # 1) Validación de parámetros
    if not df or not dt:
        return jsonify({"msg": "Faltan parámetros 'date_from' o 'date_to'"}), 400

    # 2) Parseo seguro
    try:
        date_from = datetime.fromisoformat(df)
        date_to   = datetime.fromisoformat(dt)
    except ValueError:
        return jsonify({"msg": "Formato de fecha inválido"}), 400

    logger.info(f"Supervisor {current_supervisor.folio} solicitando tracks históricos {df} → {dt}")

    # 3) Query y agrupación
    rows = (
        db.session.query(
            User.folio,
            GPSTrackPoint.latitude,
            GPSTrackPoint.longitude,
            GPSTrackPoint.timestamp
        )
        .join(Shift, Shift.user_id == User.id)
        .join(GPSTrackPoint, GPSTrackPoint.shift_id == Shift.id)
        .filter(GPSTrackPoint.timestamp.between(date_from, date_to))
        .order_by(User.folio, GPSTrackPoint.timestamp)
        .all()
    )
    result = {}
    for folio, lat, lon, ts in rows:
        result.setdefault(folio, []).append({
            'latitude':  lat,
            'longitude': lon,
            'timestamp': ts.isoformat()
        })

    # 4) Formato de salida (lista de {folio, track})
    output = [{'folio': f, 'track': pts} for f, pts in result.items()]
    return jsonify(output), 200


@dashboard_api_bp.route('/historical-identifications', methods=['GET'])
@supervisor_required
def get_historical_identifications(current_supervisor):
    """API: Devuelve todas las identificaciones (person/vehicle) en un rango."""
    df          = request.args.get('date_from')
    dt          = request.args.get('date_to')
    folios_str  = request.args.get('agent_folios', '')
    type_filter = request.args.get('type', 'all')

    # 1) Validación de parámetros
    if not df or not dt:
        return jsonify({"msg": "Faltan parámetros 'date_from' o 'date_to'"}), 400

    # 2) Parseo seguro
    try:
        date_from = datetime.fromisoformat(df)
        date_to   = datetime.fromisoformat(dt)
    except ValueError:
        return jsonify({"msg": "Formato de fecha inválido"}), 400

    folio_list = [f.strip() for f in folios_str.split(',') if f.strip()]
    logger.info(
        f"Supervisor {current_supervisor.folio} solicitando identif. históricas "
        f"{df} → {dt} tipo={type_filter} folios={folio_list}"
    )

    items = []

    # — Personas —
    if type_filter in ('person', 'all'):
        q = IdentifiedPerson.query.join(User).filter(
            IdentifiedPerson.timestamp.between(date_from, date_to)
        )
        if folio_list:
            q = q.filter(User.folio.in_(folio_list))
        for p in q.all():
            if p.location_lat and p.location_lon:
                items.append({
                    'folio':    p.officer.folio,
                    'type':     'person',
                    'latitude': p.location_lat,
                    'longitude': p.location_lon,
                    'timestamp': p.timestamp.isoformat()
                })

    # — Vehículos —
    if type_filter in ('vehicle', 'all'):
        q = IdentifiedVehicle.query.join(User).filter(
            IdentifiedVehicle.timestamp.between(date_from, date_to)
        )
        if folio_list:
            q = q.filter(User.folio.in_(folio_list))
        for v in q.all():
            if v.location_lat and v.location_lon:
                items.append({
                    'folio':    v.officer.folio,
                    'type':     'vehicle',
                    'latitude': v.location_lat,
                    'longitude': v.location_lon,
                    'timestamp': v.timestamp.isoformat()
                })

    return jsonify(items), 200

# ——— Endpoint para enviar mensajes a agentes en vivo —————————
# app/routes/dashboard.py

@dashboard_api_bp.route('/operator-message', methods=['POST'])
@supervisor_required
def operator_message(current_supervisor):
    """
    Envía un mensaje (o mensajes) desde el operador a uno o varios agentes.
    Body JSON: { "folios": ["1234","5678"], "message": "Hola equipo" }
    """
    from ..models.message import OperatorMessage
    from ..models.user import User

    data = request.get_json() or {}
    folios  = data.get('folios') or [ data.get('folio') ]
    message = data.get('message', '').strip()
    if not folios or not message:
        return jsonify({"msg": "Faltan folios o mensaje"}), 400

    # Determinar tipo de mensaje
    message_type = 'broadcast' if len(folios) > 5 else ('multiple' if len(folios) > 1 else 'personal')

    sent_count = 0
    websocket_sent = 0

    for folio in folios:
        try:
            # Verificar que el usuario existe
            recipient = User.query.filter_by(folio=folio).first()
            if not recipient:
                logger.warning(f"Usuario con folio {folio} no encontrado")
                continue

            # Guardar mensaje en base de datos
            operator_msg = OperatorMessage(
                content=message,
                sender_user_id=current_supervisor.id,
                recipient_folio=folio,
                message_type=message_type,
                sent_via_websocket=False  # Se actualizará si el WebSocket funciona
            )
            db.session.add(operator_msg)

            # Intentar enviar por WebSocket
            try:
                socketio.emit(
                    'operator_message',
                    {'message': message, 'from': current_supervisor.folio, 'message_id': operator_msg.id},
                    room=folio,
                    namespace='/live'
                )
                operator_msg.sent_via_websocket = True
                websocket_sent += 1
                logger.info(f"Mensaje enviado por WebSocket a {folio}")
            except Exception as ws_error:
                logger.warning(f"Error enviando WebSocket a {folio}: {ws_error}")
                # El mensaje queda guardado en BD para polling

            sent_count += 1

        except Exception as e:
            logger.error(f"Error procesando mensaje para {folio}: {e}")
            continue

    # Guardar todos los cambios
    try:
        db.session.commit()
        logger.info(f"Supervisor {current_supervisor.folio} envió mensaje a {sent_count} agentes ({websocket_sent} por WebSocket)")
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error guardando mensajes en BD: {e}")
        return jsonify({"msg": "Error guardando mensajes"}), 500

    return jsonify({
        "msg": f"Mensaje enviado a {sent_count} agente(s).",
        "sent_count": sent_count,
        "websocket_sent": websocket_sent,
        "stored_for_polling": sent_count - websocket_sent
    }), 200

@socketio.on('connect', namespace='/live')
@jwt_required()
def live_connect():
    folio = get_jwt_identity()
    join_room(folio)
    current_app.logger.info(f"Agente {folio} se unió a la sala '{folio}'")