# app/routes/identification.py

import logging
from flask import Blueprint, request, jsonify, current_app
from app import db, socketio
from app.models.user import User
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.shift import Shift
from app.models.identification import Identified<PERSON>erson, IdentifiedVehicle
from app.schemas.identification_schema import (
    identified_person_schema,
    identified_vehicle_schema,
    identified_persons_schema,
    identified_vehicles_schema
)
from app.services.dni_parser import parse_dni_string
from app.routes.live_data import fetch_active_identifications
from .novelties import role_required

logger = logging.getLogger(__name__)

identification_bp = Blueprint('identification', __name__)

def get_active_shift_for_user(user_id):
    """Función helper para obtener el turno activo."""
    return Shift.query.filter_by(user_id=user_id, end_time=None).first()

@identification_bp.route('/person', methods=['POST'])
@jwt_required()
def identify_person():
    current_user_folio = get_jwt_identity()
    officer = User.query.filter_by(folio=current_user_folio).first()
    if not officer:
        return jsonify({"msg": "Usuario no encontrado"}), 404

    shift = get_active_shift_for_user(officer.id)
    if not shift:
        return jsonify({"msg": "No hay jornada activa"}), 400

    data = request.get_json() or {}
    latitude = data.get('latitude')
    longitude = data.get('longitude')
    dni_raw = data.get('dni_raw_string')
    parsed_dni = parse_dni_string(dni_raw) if dni_raw else None

    new_ident = IdentifiedPerson(
        shift_id=shift.id,
        officer_id=officer.id,
        location_lat=latitude,
        location_lon=longitude,
        dni_raw_string=dni_raw,
        notes=data.get('notes')
    )

    if parsed_dni:
        new_ident.last_name = parsed_dni.apellido
        new_ident.first_names = parsed_dni.nombres
        new_ident.dni_number = parsed_dni.dni
        new_ident.dob = parsed_dni.fecha_nacimiento
    else:
        new_ident.last_name = data.get('last_name')
        new_ident.first_names = data.get('first_names')
        new_ident.dni_number = data.get('dni_number')

    db.session.add(new_ident)
    try:
        db.session.commit()

        logger.info(f"Identificación PERSONA {new_ident.id} guardada para oficial {officer.folio}")
        # ————— Aquí agregas el emit —————
        socketio.emit(
            'identification_started',
            {
                'folio': officer.folio,
                'type': 'person',
            },
            namespace='/live'
        )
        # ————————————————————————————————
        # <<< --- ELIMINAR LA SIGUIENTE SECCIÓN --- >>>
        # notify_active_identification(officer.folio, {
        #     'folio': officer.folio,
        #     'type': 'person',
        #     'dni_number': new_ident.dni_number,
        #     'first_names': new_ident.first_names,
        #     'last_name': new_ident.last_name,
        #     'status': 'completed' # Esto ya no se envía desde aquí
        # })
        # <<< --- FIN SECCIÓN ELIMINADA --- >>>

        return jsonify(identified_person_schema.dump(new_ident)), 201

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error identify_person: {e}", exc_info=True)
        return jsonify({"msg": "Error interno"}), 500

@identification_bp.route('/vehicle', methods=['POST'])
@jwt_required()
def identify_vehicle():
    current_user_folio = get_jwt_identity()
    officer = User.query.filter_by(folio=current_user_folio).first()
    if not officer:
        return jsonify({"msg": "Usuario no encontrado"}), 404

    shift = get_active_shift_for_user(officer.id)
    if not shift:
        return jsonify({"msg": "No hay jornada activa"}), 400

    data = request.get_json() or {}
    plate = data.get('plate')
    if not plate:
        return jsonify({"msg": "Falta dominio"}), 400

    new_ident = IdentifiedVehicle(
        shift_id=shift.id,
        officer_id=officer.id,
        location_lat=data.get('latitude'),
        location_lon=data.get('longitude'),
        plate=plate.upper(),
        brand=data.get('brand'),
        model=data.get('model'),
        color=data.get('color'),
        notes=data.get('notes')
    )

    db.session.add(new_ident)
    try:
        db.session.commit()

        logger.info(f"Identificación VEHÍCULO {new_ident.id} guardada para oficial {officer.folio}")
        # Emit de inicio de identificación vehicular
        socketio.emit(
            'identification_started',
            {
                'folio': officer.folio,
                'type': 'vehicle',
            },
            namespace='/live'
        )
        # <<< --- ELIMINAR LA SIGUIENTE SECCIÓN --- >>>
        # notify_active_identification(officer.folio, {
        #     'folio': officer.folio,
        #     'type': 'vehicle',
        #     'plate': new_ident.plate,
        #     'brand': new_ident.brand,
        #     'model': new_ident.model,
        #     'status': 'completed' # Esto ya no se envía desde aquí
        # })
        # <<< --- FIN SECCIÓN ELIMINADA --- >>>

        return jsonify(identified_vehicle_schema.dump(new_ident)), 201

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error identify_vehicle: {e}", exc_info=True)
        return jsonify({"msg": "Error interno"}), 500

@identification_bp.route('/persons', methods=['GET'])
@role_required(['supervisor', 'comando', 'agente'])
def get_identified_persons(current_admin_user):
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    query = IdentifiedPerson.query.order_by(IdentifiedPerson.timestamp.desc())
    paginated = query.paginate(page=page, per_page=per_page, error_out=False)
    results = identified_persons_schema.dump(paginated.items)
    return jsonify({
        "identifications": results,
        "total": paginated.total,
        "pages": paginated.pages,
        "current_page": page
    }), 200

@identification_bp.route('/vehicles', methods=['GET'])
@role_required(['supervisor', 'comando', 'agente'])
def get_identified_vehicles(current_admin_user):
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    query = IdentifiedVehicle.query.order_by(IdentifiedVehicle.timestamp.desc())
    paginated = query.paginate(page=page, per_page=per_page, error_out=False)
    results = identified_vehicles_schema.dump(paginated.items)
    return jsonify({
        "identifications": results,
        "total": paginated.total,
        "pages": paginated.pages,
        "current_page": page
    }), 200

@identification_bp.route('/license', methods=['POST'])
@jwt_required()
def identify_license():
    current_folio = get_jwt_identity()
    officer = User.query.filter_by(folio=current_folio).first_or_404()
    shift = get_active_shift_for_user(officer.id)
    if not shift:
        return jsonify({"msg": "No hay jornada activa"}), 400

    data = request.get_json() or {}
    lat = data.get('latitude'); lon = data.get('longitude')
    raw = data.get('raw_data','')
    parsed = parse_driving_license(raw)

    new_l = IdentifiedLicense(
        shift_id=shift.id, officer_id=officer.id,
        location_lat=lat, location_lon=lon,
        raw_data=raw, notes=data.get('notes')
    )
    if parsed:
        new_l.license_number  = parsed.license_number
        new_l.last_name       = parsed.last_name
        new_l.first_names     = parsed.first_names
        new_l.dob             = parsed.dob
        new_l.expiration_date = parsed.expiration_date
        new_l.categories      = parsed.categories

    db.session.add(new_l)
    try:
        db.session.commit()
        socketio.emit('identification_started', {'folio': officer.folio, 'type':'license'}, namespace='/live')
        return jsonify(identified_license_schema.dump(new_l)), 201
    except Exception as e:
        db.session.rollback()
        current_app.logger.error("License ID save error: %s", e, exc_info=True)
        return jsonify({"msg":"Error interno"}), 500
