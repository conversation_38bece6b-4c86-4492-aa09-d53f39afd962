# app/routes/live_data.py
from flask import Blueprint, jsonify, current_app
from app.models.identification import IdentifiedPerson, IdentifiedVehicle
from app.models.user import User
from app import db, socketio

live_data_bp = Blueprint('live_data', __name__, url_prefix='/api/live')

def fetch_active_identifications():
    """Consulta en la BD las identificaciones en curso e incluye el folio del oficial."""
    # Personas
    people_q = (
        db.session.query(IdentifiedPerson, User.folio.label('folio'))
        .join(User, IdentifiedPerson.officer_id == User.id)
        .filter(IdentifiedPerson.location_lat.isnot(None))
        .filter(IdentifiedPerson.location_lon.isnot(None))
        .filter(
            (IdentifiedPerson.dni_number.is_(None)) |
            ((IdentifiedPerson.first_names.is_(None)) & (IdentifiedPerson.last_name.is_(None)))
        )
    )
    people = people_q.all()

    # Vehículos
    vehicles_q = (
        db.session.query(IdentifiedVehicle, User.folio.label('folio'))
        .join(User, IdentifiedVehicle.officer_id == User.id)
        .filter(IdentifiedVehicle.location_lat.isnot(None))
        .filter(IdentifiedVehicle.location_lon.isnot(None))
        .filter(
            (IdentifiedVehicle.plate.is_(None)) &
            (IdentifiedVehicle.brand.is_(None))
        )
    )
    vehicles = vehicles_q.all()

    results = []
    for p, folio in people:
        results.append({
            "type":       "person",
            "id":         p.id,
            "folio":      folio,
            "lat":        p.location_lat,
            "lon":        p.location_lon,
            "dni_number": p.dni_number,
            "first_names": p.first_names,
            "last_name":  p.last_name,
            "status":     "in_progress"
        })
    for v, folio in vehicles:
        results.append({
            "type":   "vehicle",
            "id":     v.id,
            "folio":  folio,
            "lat":    v.location_lat,
            "lon":    v.location_lon,
            "plate":  v.plate,
            "brand":  v.brand,
            "status": "in_progress"
        })

    return results

@live_data_bp.route('/active-identifications', methods=['GET'])
def get_active_identifications():
    """
    API REST + WS: devuelve JSON y emite 'active_identifications' con folio.
    """
    payload = fetch_active_identifications()
    try:
        socketio.emit('active_identifications', payload, namespace='/live')
    except Exception as e:
        current_app.logger.error(f"Error emitiendo WS active_identifications: {e}")
    return jsonify(payload), 200
