# app/routes/messages.py

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from .. import db, socketio
from ..models.message import OperatorMessage, NotificationStatus
from ..models.novelty import Novelty
from ..models.user import User
from ..utils.decorators import supervisor_required
import logging

logger = logging.getLogger(__name__)

# Blueprint para mensajes y notificaciones
messages_bp = Blueprint('messages', __name__)

@messages_bp.route('/unread', methods=['GET'])
@jwt_required()
def get_unread_messages():
    """Obtiene mensajes no leídos para el agente actual"""
    try:
        current_user_folio = get_jwt_identity()
        user = User.query.filter_by(folio=current_user_folio).first()
        
        if not user:
            return jsonify({"msg": "Usuario no encontrado"}), 404
        
        # Obtener mensajes no leídos
        unread_messages = OperatorMessage.get_unread_for_user(current_user_folio)
        
        # Convertir a diccionarios
        messages_data = [msg.to_dict() for msg in unread_messages]
        
        logger.info(f"Usuario {current_user_folio} tiene {len(messages_data)} mensajes no leídos")
        
        return jsonify({
            "messages": messages_data,
            "count": len(messages_data)
        }), 200
        
    except Exception as e:
        logger.error(f"Error obteniendo mensajes no leídos para {current_user_folio}: {e}", exc_info=True)
        return jsonify({"msg": "Error interno del servidor"}), 500


@messages_bp.route('/<int:message_id>/read', methods=['POST'])
@jwt_required()
def mark_message_as_read(message_id):
    """Marca un mensaje específico como leído"""
    try:
        current_user_folio = get_jwt_identity()
        
        success = OperatorMessage.mark_as_read(message_id, current_user_folio)
        
        if success:
            logger.info(f"Mensaje {message_id} marcado como leído por {current_user_folio}")
            return jsonify({"msg": "Mensaje marcado como leído"}), 200
        else:
            return jsonify({"msg": "Mensaje no encontrado o ya leído"}), 404
            
    except Exception as e:
        logger.error(f"Error marcando mensaje {message_id} como leído: {e}", exc_info=True)
        return jsonify({"msg": "Error interno del servidor"}), 500


@messages_bp.route('/mark-all-read', methods=['POST'])
@jwt_required()
def mark_all_messages_as_read():
    """Marca todos los mensajes como leídos para el agente actual"""
    try:
        current_user_folio = get_jwt_identity()
        
        count = OperatorMessage.mark_all_as_read(current_user_folio)
        
        logger.info(f"{count} mensajes marcados como leídos para {current_user_folio}")
        
        return jsonify({
            "msg": f"{count} mensajes marcados como leídos",
            "count": count
        }), 200
        
    except Exception as e:
        logger.error(f"Error marcando todos los mensajes como leídos: {e}", exc_info=True)
        return jsonify({"msg": "Error interno del servidor"}), 500


@messages_bp.route('/check-new-novelties', methods=['GET'])
@jwt_required()
def check_new_novelties():
    """Verifica si hay novedades nuevas para el agente actual"""
    try:
        current_user_folio = get_jwt_identity()
        
        has_new, count = NotificationStatus.should_notify_new_novelties(current_user_folio)
        
        result = {
            "has_new_novelties": has_new,
            "new_novelties_count": count
        }
        
        if has_new:
            logger.info(f"Usuario {current_user_folio} tiene {count} novedades nuevas")
        
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error verificando novedades nuevas para {current_user_folio}: {e}", exc_info=True)
        return jsonify({"msg": "Error interno del servidor"}), 500


@messages_bp.route('/update-novelty-status', methods=['POST'])
@jwt_required()
def update_novelty_status():
    """Actualiza el estado de novedades vistas por el agente"""
    try:
        current_user_folio = get_jwt_identity()
        data = request.get_json() or {}
        
        novelty_id = data.get('novelty_id')
        if not novelty_id:
            return jsonify({"msg": "novelty_id requerido"}), 400
        
        # Verificar que la novedad existe
        novelty = Novelty.query.get(novelty_id)
        if not novelty:
            return jsonify({"msg": "Novedad no encontrada"}), 404
        
        # Actualizar estado
        status = NotificationStatus.update_last_seen_novelty(current_user_folio, novelty_id)
        
        logger.info(f"Estado de novedades actualizado para {current_user_folio}: última vista {novelty_id}")
        
        return jsonify({"msg": "Estado actualizado correctamente"}), 200
        
    except Exception as e:
        logger.error(f"Error actualizando estado de novedades: {e}", exc_info=True)
        return jsonify({"msg": "Error interno del servidor"}), 500


# Endpoint para uso interno - obtener resumen de notificaciones
@messages_bp.route('/notification-summary', methods=['GET'])
@jwt_required()
def get_notification_summary():
    """Obtiene un resumen de todas las notificaciones pendientes"""
    try:
        current_user_folio = get_jwt_identity()
        
        # Mensajes no leídos
        unread_messages = OperatorMessage.get_unread_for_user(current_user_folio)
        messages_count = len(unread_messages)
        
        # Novedades nuevas
        has_new_novelties, novelties_count = NotificationStatus.should_notify_new_novelties(current_user_folio)
        
        summary = {
            "unread_messages_count": messages_count,
            "new_novelties_count": novelties_count if has_new_novelties else 0,
            "total_notifications": messages_count + (novelties_count if has_new_novelties else 0),
            "has_notifications": messages_count > 0 or has_new_novelties
        }
        
        logger.debug(f"Resumen de notificaciones para {current_user_folio}: {summary}")
        
        return jsonify(summary), 200
        
    except Exception as e:
        logger.error(f"Error obteniendo resumen de notificaciones: {e}", exc_info=True)
        return jsonify({"msg": "Error interno del servidor"}), 500
