# app/routes/novelties.py
import logging
from flask import Blueprint, request, jsonify
from app import db
from app.models.user import User # Import absoluto
from app.models.novelty import Novelty, NoveltyReadStatus # Import absoluto
from app.schemas.novelty_schema import novelty_schema, novelties_schema, novelty_read_status_schema # Import absoluto
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from sqlalchemy import not_, func, exc as sqlalchemy_exc # Importar exc
from sqlalchemy.orm import joinedload
from functools import wraps

# Logger para este módulo
logger = logging.getLogger(__name__)

novelties_bp = Blueprint('novelties', __name__)

# --- Decorador para roles (usa folio y busca usuario) ---
def role_required(allowed_roles):
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            current_user_folio = get_jwt_identity() # Obtiene FOLIO
            user = User.query.filter_by(folio=current_user_folio).first()
            if not user or user.role not in allowed_roles:
                logger.warning(f"Acceso a ruta novedad denegado. Folio:{current_user_folio}, Rol:{user.role if user else 'N/A'}")
                return jsonify({"msg": "Permiso denegado: Rol no autorizado"}), 403
            kwargs['current_admin_user'] = user # Pasar usuario a la función
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# --- Rutas para Agentes ---

@novelties_bp.route('/unread', methods=['GET'])
@jwt_required()
def get_unread_novelties():
    """Obtiene las novedades NO leídas por el agente actual."""
    current_user_folio = get_jwt_identity() # Obtiene FOLIO
    user = User.query.filter_by(folio=current_user_folio).first()
    if not user: return jsonify({"msg": "Usuario del token no encontrado"}), 404
    user_id = user.id

    try:
        read_novelty_ids = db.session.query(NoveltyReadStatus.novelty_id).filter_by(user_id=user_id)
        # --- CORREGIDO: USA EL NOMBRE CORRECTO DE LA RELACIÓN ---
        unread_novelties = Novelty.query.options(joinedload(Novelty.author_user))\
                                        .filter(not_(Novelty.id.in_(read_novelty_ids)))\
                                        .order_by(Novelty.created_at.desc()).all()
        # ----------------------------------------------------
        return jsonify(novelties_schema.dump(unread_novelties)), 200
    except Exception as e:
        logger.error(f"Error en get_unread_novelties para {user.folio}: {e}", exc_info=True)
        return jsonify({"msg": "Error al obtener novedades no leídas"}), 500


@novelties_bp.route('/<int:novelty_id>/read', methods=['POST'])
@jwt_required()
def mark_novelty_as_read(novelty_id):
    """Marca una novedad como leída por el agente actual."""
    current_user_folio = get_jwt_identity() # Obtiene FOLIO
    user = User.query.filter_by(folio=current_user_folio).first()
    if not user: return jsonify({"msg": "Usuario no encontrado"}), 404
    user_id = user.id

    # Verificar si novedad existe ANTES de intentar crear el status
    novelty_exists = db.session.query(Novelty.id).filter_by(id=novelty_id).scalar() is not None
    if not novelty_exists: return jsonify({"msg": "Novedad no encontrada"}), 404

    # Usar get_or_create pattern (o similar)
    try:
        read_status = NoveltyReadStatus.query.filter_by(user_id=user_id, novelty_id=novelty_id).first()
        if read_status:
             logger.debug(f"Agente {user.folio} ya había leído novedad {novelty_id}.")
             return jsonify(novelty_read_status_schema.dump(read_status)), 200

        read_status = NoveltyReadStatus(user_id=user_id, novelty_id=novelty_id)
        db.session.add(read_status)
        db.session.commit()
        logger.info(f"Agente {user.folio} marcó novedad {novelty_id} como leída.")
        return jsonify(novelty_read_status_schema.dump(read_status)), 201

    except sqlalchemy_exc.IntegrityError: # Captura error específico si se intenta insertar duplicado
        db.session.rollback()
        logger.warning(f"IntegrityError marcar novedad {novelty_id} leída por {user.folio}.")
        existing = NoveltyReadStatus.query.filter_by(user_id=user_id, novelty_id=novelty_id).first()
        if existing: return jsonify(novelty_read_status_schema.dump(existing)), 200
        return jsonify({"msg": "Error de concurrencia al marcar como leída"}), 409
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error DB mark_novelty_as_read user:{user.folio} novelty:{novelty_id}: {e}", exc_info=True)
        return jsonify({"msg": "Error al marcar como leída", "error": str(e)}), 500


@novelties_bp.route('/all', methods=['GET'])
@jwt_required()
def get_all_novelties():
    """Obtiene todas las novedades (con filtro opcional por texto)."""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 15, type=int)
    search = request.args.get('search', '', type=str).strip()

    try:
        query = Novelty.query.options(joinedload(Novelty.author_user)).order_by(Novelty.created_at.desc())

        if search:
            like_pattern = f"%{search.lower()}%"
            query = query.filter(func.lower(Novelty.content).like(like_pattern))

        paginated = query.paginate(page=page, per_page=per_page, error_out=False)
        results = novelties_schema.dump(paginated.items)
        return jsonify({
            "novelties": results,
            "total": paginated.total,
            "pages": paginated.pages,
            "current_page": page
        }), 200

    except Exception as e:
        logger.error(f"Error get_all_novelties: {e}", exc_info=True)
        return jsonify({"msg": "Error al obtener novedades"}), 500
# --- Rutas para Supervisores/Comando ---

@novelties_bp.route('', methods=['POST'])
@role_required(['supervisor', 'comando'])
def create_novelty(current_admin_user):
    """Crea una nueva novedad."""
    user_id = current_admin_user.id
    data = request.get_json();
    if not data or not data.get('content'): return jsonify({"msg": "Falta contenido"}), 400
    new_novelty = Novelty(content=data['content'], image_url=data.get('image_url'), created_by_user_id=user_id)
    db.session.add(new_novelty);
    try: db.session.commit(); logger.info(f"Novedad creada por {current_admin_user.folio} (ID:{new_novelty.id})"); return jsonify(novelty_schema.dump(new_novelty)), 201
    except Exception as e: db.session.rollback(); logger.error(f"Error create_novelty por {current_admin_user.folio}: {e}", exc_info=True); return jsonify({"msg":"Error al crear", "error":str(e)}), 500


@novelties_bp.route('/<int:novelty_id>', methods=['GET'])
@role_required(['supervisor', 'comando'])
def get_novelty_detail(current_admin_user, novelty_id):
    """Obtiene los detalles de una novedad por ID."""
    # --- CORREGIDO: USA EL NOMBRE CORRECTO DE LA RELACIÓN ---
    novelty = Novelty.query.options(joinedload(Novelty.author_user)).get_or_404(novelty_id)
    # ----------------------------------------------------
    return jsonify(novelty_schema.dump(novelty)), 200


@novelties_bp.route('/<int:novelty_id>', methods=['PUT'])
@role_required(['supervisor', 'comando'])
def update_novelty(current_admin_user, novelty_id):
    """Actualiza una novedad existente."""
    novelty = Novelty.query.get_or_404(novelty_id); data = request.get_json();
    if not data: return jsonify({"msg": "No hay datos para actualizar"}), 400
    if 'content' in data: novelty.content = data['content']
    if 'image_url' in data: novelty.image_url = data['image_url'] # Permite borrarla si viene null/vacío
    try: db.session.commit(); logger.info(f"Novedad {novelty_id} actualizada por {current_admin_user.folio}"); return jsonify(novelty_schema.dump(novelty)), 200
    except Exception as e: db.session.rollback(); logger.error(f"Error update_novelty {novelty_id} por {current_admin_user.folio}: {e}", exc_info=True); return jsonify({"msg":"Error al actualizar", "error":str(e)}), 500


@novelties_bp.route('/<int:novelty_id>', methods=['DELETE'])
@role_required(['supervisor', 'comando'])
def delete_novelty(current_admin_user, novelty_id):
    """Elimina una novedad."""
    novelty = Novelty.query.get_or_404(novelty_id);
    # La relación Novelty.read_by_users con cascade="all, delete-orphan" debería borrar los status automáticamente.
    # NoveltyReadStatus.query.filter_by(novelty_id=novelty_id).delete(synchronize_session=False) # Ya no es necesario si cascade funciona
    db.session.delete(novelty);
    try: db.session.commit(); logger.info(f"Novedad {novelty_id} eliminada por {current_admin_user.folio}"); return '', 204
    except Exception as e: db.session.rollback(); logger.error(f"Error delete_novelty {novelty_id} por {current_admin_user.folio}: {e}", exc_info=True); return jsonify({"msg":"Error al eliminar", "error":str(e)}), 500