# app/routes/shifts.py
import logging
from flask import Blueprint, request, jsonify, current_app
from app import db
from app.models.user import User # Import absoluto
from app.models.shift import Shift # Import absoluto
from app.models.location import GPSTrackPoint # Import absoluto
from app.schemas.shift_schema import shift_schema, shifts_schema # Import absoluto
from app.schemas.location_schema import gps_track_point_schema # Import absoluto
# Importar la función para calcular estadísticas (si se usa aquí, aunque suele ser del dashboard)
# from app.utils.stats_calculator import calculate_shift_stats
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime
from marshmallow import ValidationError

# Logger para este módulo
logger = logging.getLogger(__name__)

shifts_bp = Blueprint('shifts', __name__)

@shifts_bp.route('/start', methods=['POST'])
@jwt_required()
def start_shift():
    """Inicia una nueva jornada para el agente actual."""
    current_user_folio = get_jwt_identity() # Obtiene FOLIO (string)
    logger.debug(f"Intento de inicio de turno para folio: {current_user_folio}")

    # Buscar usuario por folio
    user = User.query.filter_by(folio=current_user_folio).first()
    if not user:
        logger.error(f"Token válido pero usuario con folio '{current_user_folio}' no encontrado en BD.")
        return jsonify({"msg": "Usuario del token no encontrado"}), 404
    user_id = user.id # Obtener el ID real

    # Verificar si ya hay una jornada activa
    active_shift = Shift.query.filter_by(user_id=user_id, end_time=None).first()
    if active_shift:
        logger.warning(f"Oficial {user.folio} intentó iniciar turno, pero ya tenía uno activo (ID: {active_shift.id})")
        return jsonify({"msg": "Ya existe una jornada activa", "shift": shift_schema.dump(active_shift)}), 409

    data = request.get_json()
    if not data or not data.get('mobility_type'):
         logger.warning(f"Petición start_shift incompleta para {user.folio}.")
         return jsonify({"msg": "Falta tipo de movilidad (mobility_type)"}), 400

    # Crear nuevo turno
    new_shift = Shift(
        user_id=user_id,
        mobility_type=data['mobility_type'],
        estimated_duration=data.get('estimated_duration'),
        unit_id=data.get('unit_id')
    )
    db.session.add(new_shift)
    try:
        db.session.commit()
        logger.info(f"Oficial {user.folio} inició jornada (ID: {new_shift.id}), movilidad: {new_shift.mobility_type}")
        return jsonify({"msg": "Jornada iniciada", "shift": shift_schema.dump(new_shift)}), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error DB al iniciar jornada para oficial {user.folio}: {e}", exc_info=True)
        return jsonify({"msg": "Error interno al iniciar jornada", "error": str(e)}), 500


@shifts_bp.route('/end', methods=['POST'])
@jwt_required()
def end_shift():
    """Finaliza la jornada activa del agente actual."""
    current_user_folio = get_jwt_identity() # Obtiene FOLIO
    logger.debug(f"Intento de fin de turno para folio: {current_user_folio}")

    user = User.query.filter_by(folio=current_user_folio).first()
    if not user:
        logger.error(f"Token válido pero usuario con folio '{current_user_folio}' no encontrado en BD.")
        return jsonify({"msg": "Usuario del token no encontrado"}), 404
    user_id = user.id

    active_shift = Shift.query.filter_by(user_id=user_id, end_time=None).first()
    if not active_shift:
        logger.warning(f"Oficial {user.folio} intentó finalizar turno, pero no tenía uno activo.")
        return jsonify({"msg": "No hay jornada activa para finalizar"}), 404

    active_shift.end_time = datetime.utcnow()
    shift_id_to_process = active_shift.id

    try:
        db.session.add(active_shift)
        db.session.commit()
        logger.info(f"Oficial {user.folio} finalizó jornada (ID: {shift_id_to_process})")

        # Nota: El cálculo de stats se movió principalmente al dashboard,
        # pero si se necesita aquí, asegurar que `calculate_shift_stats` esté importado y funcione.
        # logger.info(f"Calculando estadísticas para turno {shift_id_to_process}...")
        # stats = calculate_shift_stats(shift_id_to_process)
        # ... (lógica para guardar stats si se calcula aquí) ...

        finalized_shift = db.session.get(Shift, shift_id_to_process) # Recargar para datos frescos
        return jsonify({"msg": "Jornada finalizada", "shift": shift_schema.dump(finalized_shift)}), 200

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error al finalizar jornada para oficial {user.folio} (Shift ID: {shift_id_to_process}): {e}", exc_info=True)
        return jsonify({"msg": "Error interno al finalizar jornada", "error": str(e)}), 500


@shifts_bp.route('/track', methods=['POST'])
@jwt_required()
def track_location():
    """Registra un punto de localización GPS para la jornada activa."""
    current_user_folio = get_jwt_identity() # Obtiene FOLIO

    user = User.query.filter_by(folio=current_user_folio).first()
    if not user:
        # No loguear error necesariamente, podría ser un token viejo
        return jsonify({"msg": "Usuario del token no encontrado"}), 404
    user_id = user.id

    active_shift = Shift.query.filter_by(user_id=user_id, end_time=None).first()
    if not active_shift:
        # logger.warning(f"Oficial {user.folio} envió punto GPS sin turno activo.")
        return jsonify({"msg": "No hay jornada activa para registrar ubicación"}), 400

    data = request.get_json()
    try:
        if not data or data.get('latitude') is None or data.get('longitude') is None:
             raise ValidationError("Faltan coordenadas (latitude, longitude)")
        latitude = float(data['latitude'])
        longitude = float(data['longitude'])
        accuracy = data.get('accuracy')
        accuracy = float(accuracy) if accuracy is not None else None

    except (ValidationError, ValueError, TypeError) as e:
        logger.warning(f"Datos GPS inválidos recibidos de {user.folio}: {data} - Error: {e}")
        return jsonify({"msg": "Datos de ubicación inválidos", "errors": str(e)}), 400

    new_point = GPSTrackPoint(
        shift_id=active_shift.id,
        latitude=latitude,
        longitude=longitude,
        accuracy=accuracy
    )
    db.session.add(new_point)
    try:
        db.session.commit()
        return '', 201 # Created
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error al registrar punto GPS para oficial {user.folio} (Shift ID: {active_shift.id}): {e}", exc_info=True)
        return jsonify({"msg": "Error interno al registrar ubicación", "error": str(e)}), 500

@shifts_bp.route('/current', methods=['GET'])
@jwt_required()
def get_current_shift():
    """Obtiene la jornada activa actual del agente."""
    current_user_folio = get_jwt_identity() # Obtiene FOLIO

    user = User.query.filter_by(folio=current_user_folio).first()
    if not user: return jsonify({"msg": "Usuario del token no encontrado"}), 404
    user_id = user.id

    active_shift = Shift.query.filter_by(user_id=user_id, end_time=None).first()
    if not active_shift: return jsonify({"msg": "No hay jornada activa"}), 404
    return jsonify(shift_schema.dump(active_shift)), 200

# La ruta /<shift_id>/stats la maneja el dashboard.py

# NUEVA RUTA PARA RESTAURAR TURNO ACTIVO TRAS REFRESCO
@shifts_bp.route('/active', methods=['GET'])
@jwt_required()
def get_active_shift():
    """Devuelve la jornada activa (si existe) del agente actual para restauración automática."""
    current_user_folio = get_jwt_identity()
    user = User.query.filter_by(folio=current_user_folio).first()
    if not user:
        return jsonify({'shift': None, 'msg': 'Usuario no encontrado'}), 404
    user_id = user.id
    active_shift = Shift.query.filter_by(user_id=user_id, end_time=None).order_by(Shift.start_time.desc()).first()
    if not active_shift:
        return jsonify({'shift': None, 'msg': 'No hay jornada activa'}), 200
    return jsonify({'shift': shift_schema.dump(active_shift)}), 200


@shifts_bp.route('/track_simple', methods=['POST'])
@jwt_required()
def track_location_simple():
    """Registra un punto de localización GPS sin requerir turno activo (para app móvil)."""
    current_user_folio = get_jwt_identity() # Obtiene FOLIO del JWT token

    user = User.query.filter_by(folio=current_user_folio).first()
    if not user:
        return jsonify({"msg": "Usuario del token no encontrado"}), 404

    data = request.get_json()
    try:
        if not data or data.get('latitude') is None or data.get('longitude') is None:
             raise ValidationError("Faltan coordenadas (latitude, longitude)")
        latitude = float(data['latitude'])
        longitude = float(data['longitude'])
        accuracy = data.get('accuracy')
        accuracy = float(accuracy) if accuracy is not None else None

    except (ValidationError, ValueError, TypeError) as e:
        logger.warning(f"Datos GPS inválidos recibidos de {user.folio}: {data} - Error: {e}")
        return jsonify({"msg": "Datos de ubicación inválidos", "errors": str(e)}), 400

    # Crear un turno temporal si no existe uno activo
    active_shift = Shift.query.filter_by(user_id=user.id, end_time=None).first()

    if not active_shift:
        # Crear un turno temporal para la app móvil
        active_shift = Shift(
            user_id=user.id,
            start_time=datetime.utcnow(),
            mobility_type='mobile_app'  # Tipo especial para app móvil
        )
        db.session.add(active_shift)
        db.session.flush()  # Para obtener el ID sin hacer commit completo
        logger.info(f"Turno temporal creado para usuario {user.folio} (ID: {active_shift.id})")

    new_point = GPSTrackPoint(
        shift_id=active_shift.id,
        latitude=latitude,
        longitude=longitude,
        accuracy=accuracy
    )

    try:
        db.session.add(new_point)
        db.session.commit()
        logger.info(f"Punto GPS (app móvil) registrado para usuario {user.folio}")
        return jsonify({"status": "success", "msg": "Ubicación registrada exitosamente", "point_id": new_point.id}), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error guardando punto GPS para {user.folio}: {e}", exc_info=True)
        return jsonify({"msg": "Error interno al guardar ubicación"}), 500


@shifts_bp.route('/track_anonymous', methods=['POST'])
def track_location_anonymous():
    """Registra un punto de localización GPS usando folio/password en lugar de JWT (para app móvil)."""
    data = request.get_json()

    # Validar que vengan las credenciales
    if not data or not data.get('folio') or not data.get('password'):
        return jsonify({"msg": "Faltan credenciales (folio y password requeridos)"}), 400

    # Autenticar usuario
    user = User.query.filter_by(folio=data['folio']).first()
    if not user or not user.check_password(data['password']) or not user.is_active:
        logger.warning(f"Intento de acceso no autorizado desde app móvil: folio={data.get('folio')}")
        return jsonify({"msg": "Credenciales inválidas"}), 401

    # Validar coordenadas GPS
    try:
        if data.get('latitude') is None or data.get('longitude') is None:
             raise ValidationError("Faltan coordenadas (latitude, longitude)")
        latitude = float(data['latitude'])
        longitude = float(data['longitude'])
        accuracy = data.get('accuracy')
        accuracy = float(accuracy) if accuracy is not None else None

    except (ValidationError, ValueError, TypeError) as e:
        logger.warning(f"Datos GPS inválidos recibidos de {user.folio}: {data} - Error: {e}")
        return jsonify({"msg": "Datos de ubicación inválidos", "errors": str(e)}), 400

    # Crear un turno temporal si no existe uno activo
    active_shift = Shift.query.filter_by(user_id=user.id, end_time=None).first()

    if not active_shift:
        # Crear un turno temporal para la app móvil
        active_shift = Shift(
            user_id=user.id,
            start_time=datetime.utcnow(),
            mobility_type='mobile_app_anonymous'  # Tipo especial para app móvil sin JWT
        )
        db.session.add(active_shift)
        db.session.flush()  # Para obtener el ID sin hacer commit completo
        logger.info(f"Turno temporal (anónimo) creado para usuario {user.folio} (ID: {active_shift.id})")

    new_point = GPSTrackPoint(
        shift_id=active_shift.id,
        latitude=latitude,
        longitude=longitude,
        accuracy=accuracy
    )

    try:
        db.session.add(new_point)
        db.session.commit()
        logger.info(f"Punto GPS (app móvil anónimo) registrado para usuario {user.folio}")
        return jsonify({"status": "success", "msg": "Ubicación registrada exitosamente", "point_id": new_point.id}), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error guardando punto GPS para {user.folio}: {e}", exc_info=True)
        return jsonify({"msg": "Error interno al guardar ubicación"}), 500