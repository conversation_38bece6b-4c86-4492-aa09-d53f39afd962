# app/routes/simulation.py

import requests
import logging
import os
from flask import (
    Blueprint, render_template, request, jsonify, current_app, flash, redirect, url_for
)
from app.models.user import User # Import absoluto
from app import db

sim_logger = logging.getLogger('simulation')
sim_logger.setLevel(logging.DEBUG) # Poner en DEBUG para ver más info

simulation_bp = Blueprint(
    'simulation',
    __name__,
    template_folder='../templates/simulation',
    url_prefix='/_simulate' # Mantener el prefijo para separarlo
)

# --- Helper para URL base (sin cambios) ---
def get_api_base_url():
    scheme = request.scheme # Usar el esquema de la petición actual
    host = request.host.split(':')[0] # Usar el host de la petición
    if host == 'localhost': host = '127.0.0.1' # Asegurar IP para llamadas locales
    port = os.environ.get('FLASK_RUN_PORT', current_app.config.get('PORT', 5005))
    base_url = f"{scheme}://{host}:{port}"
    sim_logger.debug(f"Generated API base URL: {base_url}/api")
    return f"{base_url}/api"

# --- Ruta Principal ---
@simulation_bp.route('/')
def simulation_page():
    """Muestra la página principal de simulación tipo agente."""
    sim_logger.info("Accediendo a la página de simulación...")
    agents = []
    api_base = ""
    try:
        # Obtener agentes activos para el selector
        with current_app.app_context(): # Necesario para query fuera de una request
             agents = User.query.filter_by(role='agente', is_active=True).order_by(User.folio).all()
             api_base = get_api_base_url()
             sim_logger.debug(f"Encontrados {len(agents)} agentes activos.")
    except Exception as e:
        sim_logger.error(f"Error al obtener datos para simulador: {e}", exc_info=True)
        flash('Error crítico al cargar datos del simulador.', 'danger')

    # Renderizar la vista del agente
    return render_template(
        'agent_view.html',
        agents=agents,
        # Ya no pasamos tokens ni turnos activos, el JS los manejará
        API_BASE_URL_FOR_JS=api_base
    )

# --- Rutas de Acción (OBSOLETAS) ---
# Ya no necesitamos las rutas /login-agent, /start-shift, /send-gps, /end-shift aquí
# porque el nuevo agent_simulator.js llama directamente a las APIs reales.
# Puedes comentar o eliminar estas rutas de simulation.py para limpiar.

# @simulation_bp.route('/login-agent', methods=['POST'])
# def login_agent(): ... (Eliminar o comentar)

# @simulation_bp.route('/start-shift', methods=['POST'])
# def start_agent_shift(): ... (Eliminar o comentar)

# @simulation_bp.route('/send-gps', methods=['POST'])
# def send_agent_gps(): ... (Eliminar o comentar)

# @simulation_bp.route('/end-shift', methods=['POST'])
# def end_agent_shift(): ... (Eliminar o comentar)