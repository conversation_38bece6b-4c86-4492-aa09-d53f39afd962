# app/schemas/identification_schema.py

from .. import ma
from ..models.identification import Identified<PERSON>erson, IdentifiedVehicle, IdentifiedLicense # Importa ambos modelos
# Podrías importar UserSchema y ShiftSchema si quisieras anidarlos
# from app.schemas.user_schema import UserSchema
# from app.schemas.shift_schema import ShiftSchema
from ..schemas.user_schema import UserSchema  # Añadir esta importación arriba


# --- Schema para Personas Identificadas ---
class IdentifiedPersonSchema(ma.SQLAlchemyAutoSchema):
    """
    Schema para serializar/deserializar objetos IdentifiedPerson.
    """
    # officer = ma.Nested(UserSchema(only=("id", "folio"))) # Ejemplo de anidación
    # shift = ma.Nested(ShiftSchema(only=("id", "start_time"))) # Ejemplo de anidación
    officer = ma.Nested(UserSchema(only=("folio",)))  # ✅ Mostrar solo el legajo

    class Meta:
        model = IdentifiedPerson
        load_instance = True
        include_fk = True # Incluir shift_id, officer_id

        # Campos generados o controlados por el sistema/BD
        dump_only = ("id", "timestamp")

        # Campo principal para recibir los datos del DNI escaneado.
        # Lo hacemos load_only porque la lógica del backend lo parseará
        # y llenará los otros campos (last_name, first_names, etc.).
        # Esos otros campos sí queremos que se envíen (dump).
        load_only = ("dni_raw_string",)

        # No hay campos obvios para exclude aquí

# Instancias para Personas
identified_person_schema = IdentifiedPersonSchema()
identified_persons_schema = IdentifiedPersonSchema(many=True)


# --- Schema para Vehículos Identificados ---
class IdentifiedVehicleSchema(ma.SQLAlchemyAutoSchema):
    """
    Schema para serializar/deserializar objetos IdentifiedVehicle.
    """
    # officer = ma.Nested(UserSchema(only=("id", "folio"))) # Ejemplo de anidación
    # shift = ma.Nested(ShiftSchema(only=("id", "start_time"))) # Ejemplo de anidación
    officer = ma.Nested(UserSchema(only=("folio",)))

    class Meta:
        model = IdentifiedVehicle
        load_instance = True
        include_fk = True # Incluir shift_id, officer_id

        # Campos generados o controlados por el sistema/BD
        dump_only = ("id", "timestamp")

        # No hay campos obvios para load_only o exclude aquí

# Instancias para Vehículos
identified_vehicle_schema = IdentifiedVehicleSchema()
identified_vehicles_schema = IdentifiedVehicleSchema(many=True)

# --- Schema para Licencias Identificadas ---
class IdentifiedLicenseSchema(ma.SQLAlchemyAutoSchema):
    officer = ma.Nested(UserSchema(only=("folio",)))

    class Meta:
        model = IdentifiedLicense
        load_instance = True
        include_fk = True
        dump_only  = ("id", "timestamp")
        load_only  = ("raw_data",)

# Instancias para Licencias
identified_license_schema  = IdentifiedLicenseSchema()
identified_licenses_schema = IdentifiedLicenseSchema(many=True)