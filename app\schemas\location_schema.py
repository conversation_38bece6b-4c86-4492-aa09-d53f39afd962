# app/schemas/location_schema.py

from .. import ma
from ..models.location import GPSTrackPoint # Importa el modelo SQLAlchemy

class GPSTrackPointSchema(ma.SQLAlchemyAutoSchema):
    """
    Schema para serializar/deserializar objetos GPSTrackPoint.
    """
    class Meta:
        model = GPSTrackPoint
        load_instance = True
        include_fk = True # Incluir shift_id

        # ID y timestamp son generados por el sistema/BD
        dump_only = ("id", "timestamp")

        # No hay campos obvios para load_only o exclude aquí

# Instancias para uso en las rutas
gps_track_point_schema = GPSTrackPointSchema()
gps_track_points_schema = GPSTrackPointSchema(many=True)