# app/schemas/novelty_schema.py

from app import ma # Import absoluto
from app.models.novelty import Novelty, NoveltyReadStatus # Import absoluto
from app.schemas.user_schema import UserSchema # Import absoluto

class NoveltySchema(ma.SQLAlchemyAutoSchema):
    # --- CORRECCIÓN AQUÍ ---
    # El campo en el JSON se llamará 'author'.
    # Marshmallow buscará el atributo 'author_user' en el objeto Novelty
    # para obtener los datos y usará UserSchema para serializarlos.
    author = ma.Nested(UserSchema(only=("id", "folio", "name")), attribute="author_user")
    # ---------------------

    class Meta:
        model = Novelty
        load_instance = True
        include_fk = True # Incluir created_by_user_id
        dump_only = ("id", "created_at", "author") # Hacer dump_only el objeto anidado también

novelty_schema = NoveltySchema()
novelties_schema = NoveltySchema(many=True)

class NoveltyReadStatusSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = NoveltyReadStatus
        load_instance = True
        include_fk = True
        dump_only = ("id", "read_at")

novelty_read_status_schema = NoveltyReadStatusSchema()
novelties_read_status_schema = NoveltyReadStatusSchema(many=True)