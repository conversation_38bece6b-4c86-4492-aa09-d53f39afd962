# app/schemas/shift_schema.py

from .. import ma # Importa la instancia Marshmallow global
from ..models.shift import Shift # Importa el modelo SQLAlchemy correspondiente
# Importar otros schemas si necesitas anidar información relacionada
# from app.schemas.user_schema import UserSchema
# from app.schemas.location_schema import GPSTrackPointSchema
from ..schemas.user_schema import UserSchema  # <-- Asegurate de importar esto arriba

class ShiftSchema(ma.SQLAlchemyAutoSchema):
    """
    Schema para serializar/deserializar objetos Shift.
    SQLAlchemyAutoSchema genera campos automáticamente a partir del modelo Shift.
    """
    officer = ma.Nested(UserSchema(only=("id", "folio", "name")))

    # --- Opcional: Anidar información del oficial ---
    # Si quieres incluir algunos datos del oficial (User) al serializar un Shift:
    # Descomenta la siguiente línea e importa UserSchema arriba.
    # officer = ma.Nested(UserSchema(only=("id", "folio", "name"))) # Muestra solo estos campos del User

    # --- Opcional: Anidar los puntos GPS o identificaciones ---
    # ¡CUIDADO! Esto puede generar respuestas muy grandes si hay muchos puntos/identificaciones.
    # Generalmente es mejor tener endpoints separados para obtener estos detalles.
    # gps_track_points = ma.Nested(GPSTrackPointSchema(many=True, only=("latitude", "longitude", "timestamp")))

    class Meta:
        # Vincula este schema con el modelo SQLAlchemy Shift
        model = Shift
        # ¡Importante! Permite crear/actualizar instancias del modelo Shift
        # a partir de los datos recibidos (deserialización).
        load_instance = True
        # Incluye las claves foráneas (ej: user_id) en la salida JSON. Útil a veces.
        include_fk = True

        # --- Control de Campos ---
        # Campos que SÓLO se enviarán al frontend (dump), no se esperan al recibir datos (load).
        # Típicamente IDs generados por la BD y timestamps automáticos.
        dump_only = ("id", "created_at", "start_time", "end_time")

        # Campos que SÓLO se aceptarán al recibir datos (load), no se envían al frontend (dump).
        # load_only = ("campo_secreto_al_crear",) # No aplica mucho aquí

        # Campos del modelo que NUNCA deben aparecer en la API (ni load ni dump).
        # exclude = ("algun_campo_interno",) # No aplica mucho aquí

# --- Instancias del Schema ---
# Se usan estas instancias en las rutas para serializar/deserializar.
shift_schema = ShiftSchema() # Para un solo objeto Shift
shifts_schema = ShiftSchema(many=True) # Para una lista de objetos Shift