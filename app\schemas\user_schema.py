# app/schemas/user_schema.py

from .. import ma # Importa la instancia Marshmallow desde app/__init__.py
from ..models.user import User

class UserSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = User
        load_instance = True  # Permite cargar datos en objetos User existentes
        # Excluir campos sensibles o no necesarios en la API
        exclude = ("password_hash",)
        # Campos que solo deben usarse al cargar datos (ej: al crear/actualizar usuario)
        load_only = ('password',) # Campo virtual para recibir la contraseña
        # Campos que solo deben mostrarse (ej: generados por la BD)
        dump_only = ("id", "created_at")

    legajo = ma.Str()  # ✅ Asegurarse de incluirlo manualmente

# Puedes crear instancias para un solo objeto o una lista
user_schema = UserSchema()
users_schema = UserSchema(many=True)

# Schema simplificado para login o identidad JWT
class UserJWTSchema(ma.Schema):
     id = ma.Int(dump_only=True)
     folio = ma.Str(required=True)
     role = ma.Str(dump_only=True)

user_jwt_schema = UserJWTSchema()