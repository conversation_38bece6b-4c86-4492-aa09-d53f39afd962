# app/services/dni_parser.py

from collections import namedtuple
import logging # Para registrar errores

# Define la estructura esperada de los datos del DNI
# AJUSTA los nombres y el orden según el formato REAL y VALIDADO del escaneo
DniData = namedtuple('DniData', [
    'id_tramite',
    'apellido',
    'nombres',
    'genero',
    'dni',
    'ejemplar',
    'fecha_nacimiento',
    'fecha_emision',
    'cuil_o_id' # El último campo
    # Añade más campos si el string tiene más partes separadas por @
])

# Configura un logger básico
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def parse_dni_string(dni_string: str) -> DniData | None:
    """
    Parsea el string del DNI con formato específico separado por '@'.
    Ejemplo esperado: 00655127463@GONZALEZ@JOAQUIN ABEL@M@28063127@C@26/03/1980@06/04/2021@203

    Args:
        dni_string: El string crudo obtenido del escaneo del DNI.

    Returns:
        Un objeto DniData con los campos parseados si el formato es correcto,
        o None si el formato es inválido o hay un error.
    """
    if not isinstance(dni_string, str) or not dni_string:
        logger.warning("Intento de parsear DNI con entrada inválida o vacía.")
        return None

    parts = dni_string.strip().split('@')
    expected_parts = len(DniData._fields) # Número de campos definidos en DniData

    # Validar el número de partes obtenidas
    # Ser un poco flexible si hay partes vacías al final producto de un '@' extra
    actual_parts = len(parts)
    if actual_parts < expected_parts:
        logger.error(f"Error parseando DNI: Se esperaban {expected_parts} partes, se obtuvieron {actual_parts}. String: '{dni_string}'")
        return None
    elif actual_parts > expected_parts:
         logger.warning(f"Parseando DNI: Se obtuvieron {actual_parts} partes (más de las {expected_parts} esperadas). Se usarán las primeras {expected_parts}. String: '{dni_string}'")
         # Truncar las partes extra si es necesario, o manejarlas de otra forma.
         # parts = parts[:expected_parts] # Descomentar si se quieren ignorar partes extra

    try:
        # Crear la instancia de DniData usando las partes obtenidas
        # Asegúrate de que el orden en DniData coincida con el orden en el string!
        parsed_data = DniData(*parts[:expected_parts])
        logger.info(f"DNI parseado exitosamente para DNI#: {parsed_data.dni}")
        return parsed_data
    except Exception as e:
        # Captura cualquier error inesperado durante la creación del namedtuple
        logger.exception(f"Excepción inesperada al crear DniData. String: '{dni_string}'. Error: {e}")
        return None

# Ejemplo de uso (puedes quitarlo del archivo final)
# if __name__ == '__main__':
#     test_string = "00655127463@GONZALEZ@JOAQUIN ABEL@M@28063127@C@26/03/1980@06/04/2021@203"
#     data = parse_dni_string(test_string)
#     if data:
#         print("Parseo Exitoso:")
#         print(f"  ID Trámite: {data.id_tramite}")
#         print(f"  Apellido: {data.apellido}")
#         print(f"  Nombres: {data.nombres}")
#         print(f"  DNI: {data.dni}")
#         print(f"  Nacimiento: {data.fecha_nacimiento}")
#     else:
#         print("Fallo el parseo.")

#     test_string_malo = "123@apellido"
#     data_malo = parse_dni_string(test_string_malo)
#     if not data_malo:
#          print("Fallo el parseo del string malo (esperado).")

#     test_string_extra = "00655127463@GONZALEZ@JOAQUIN ABEL@M@28063127@C@26/03/1980@06/04/2021@203@EXTRA@"
#     data_extra = parse_dni_string(test_string_extra)
#     if data_extra:
#         print("Parseo exitoso con datos extra (se ignoraron los extras si se truncó)")
#         print(data_extra)