# app/services/driving_license_parser.py

import logging
import re
from collections import namedtuple

LicenseData = namedtuple('LicenseData', [
    'license_number', 'last_name', 'first_names',
    'dob', 'expiration_date', 'categories'
])

logger = logging.getLogger(__name__)

def parse_driving_license(raw: str) -> LicenseData | None:
    """
    Parsea el bloque de texto PDF417 (Mercosur/AAMVA-like).
    Extrae los campos DCM (license number), DCS (apellido), DAC (nombre),
    DBB (DOB formato YYYYMMDD), DBA (vencimiento YYYYMMDD), DCA (categorías).
    """
    try:
        # Normalizamos saltos de línea
        lines = raw.splitlines()
        data = {}
        for line in lines:
            if line.startswith('DCM'):  data['license_number']  = line[3:].strip()
            if line.startswith('DCS'):  data['last_name']       = line[3:].strip().title()
            if line.startswith('DAC'):  data['first_names']     = line[3:].strip().title()
            if line.startswith('DBB'):  # DOB
                d = line[3:11]
                data['dob'] = f"{d[0:4]}-{d[4:6]}-{d[6:8]}"
            if line.startswith('DBA'):  # Expiration
                e = line[3:11]
                data['expiration_date'] = f"{e[0:4]}-{e[4:6]}-{e[6:8]}"
            if line.startswith('DCA'):  data['categories']     = line[3:].strip()
        # Verificamos campos mínimos
        if all(field in data for field in ['license_number','last_name','first_names']):
            return LicenseData(**data)
        logger.error("Faltan campos obligatorios en licencia: %r", data)
    except Exception as e:
        logger.exception("Error parseando licencia: %s", e)
    return None
