# app/sockets.py
import logging
from flask import request, current_app
from flask_socketio import emit, join_room, leave_room, disconnect
from flask_jwt_extended import decode_token, exceptions as jwt_exceptions
from . import socketio
from app.models.user import User

# <<< Asegúrate que el logger esté configurado para DEBUG si FLASK_ENV es development >>>
# Puedes ajustar esto en __init__.py o aquí
logger = logging.getLogger(__name__) # Usar el logger de Flask-SocketIO o el de Flask
# Example: if current_app.debug: logger.setLevel(logging.DEBUG)

connected_users = {}

@socketio.on('connect', namespace='/live')
def handle_connect(auth):
    sid = request.sid
    logger.info(f"🟡 WS connect attempt SID={sid} on /live")
    try:
        # 1) Validar que venga un dict con 'token'
        if not auth or not isinstance(auth, dict) or 'token' not in auth:
            logger.warning(f"No auth.token para SID={sid}: {auth!r}")
            emit('auth_error', {'msg': 'Token faltante'}, room=sid, namespace='/live')
            return disconnect(sid=sid, namespace='/live')

        token = auth['token']

        # 2) Decodificar y verificar JWT
        decoded = decode_token(token)
        identity_key = current_app.config.get('JWT_IDENTITY_CLAIM', 'sub')
        folio = decoded.get(identity_key)
        if not folio:
            raise RuntimeError("Claim de identidad faltante en token.")

        # 3) Buscar y validar usuario en base de datos
        user = User.query.filter_by(folio=folio).first()
        if not user or not user.is_active:
            raise RuntimeError("Usuario inválido o inactivo.")

        # 4) Manejar duplicados: desconectar sesión anterior si existe
        if folio in connected_users:
            old_sid = connected_users[folio]
            logger.warning(f"User {folio} already connected (SID {old_sid}). Forcing disconnect.")
            emit('force_disconnect', {'reason': 'Nueva sesión iniciada'}, room=old_sid, namespace='/live')
            disconnect(sid=old_sid, namespace='/live')

        connected_users[folio] = sid
        logger.info(f"🔗 User {folio} connected/updated with SID {sid}. Connected users: {list(connected_users.keys())}")

        # 5) Unir salas
        join_room(folio)
        if user.role in ('supervisor', 'comando', 'admin'):
            join_room('supervisors')
            logger.info(f"User {folio} (role={user.role}) joined 'supervisors' room.")

        # 6) Notificar autenticación exitosa
        emit('auth_success', {'folio': folio, 'role': user.role}, room=sid, namespace='/live')
        logger.info(f"✅ WS Auth Success for {folio}. Emitted auth_success to SID {sid}.")

    except Exception as e:
        error_message = str(e)
        logger.warning(f"🔴 WS connect failed SID={sid}: {error_message}", exc_info=True)
        emit('auth_error', {'msg': error_message}, room=sid, namespace='/live')
        disconnect(sid=sid, namespace='/live')
        logger.info(f"🔌 Forcibly disconnected SID {sid} after auth failure.")


# --- DESCONEXIÓN ---
# Sin cambios necesarios en handle_disconnect, handle_start_identification, handle_update_identification
# ... (resto de handle_disconnect) ...
@socketio.on('disconnect', namespace='/live')
def handle_disconnect():
    sid = request.sid
    folio = next((f for f, s in connected_users.items() if s == sid), None)
    if folio and folio in connected_users:
        del connected_users[folio]
        logger.info(f"🔌 WS disconnected: {folio} (SID={sid}). Remaining users: {list(connected_users.keys())}")
    else:
        logger.info(f"🔌 WS disconnected SID={sid} (Folio not found or already removed)")

# --- EVENTO: Agente INICIA identificación ---
# Sin cambios
@socketio.on('start_identification', namespace='/live')
def handle_start_identification(data):
    sid = request.sid
    folio = next((f for f, s in connected_users.items() if s == sid), None)
    if not folio:
        logger.warning(f"WS 'start_identification' from unknown SID: {sid}. Data: {data}")
        return
    id_type = data.get('type')
    lat = data.get('latitude')
    lon = data.get('longitude')
    if not id_type:
        logger.warning(f"AGENT {folio}: Missing 'type' in 'start_identification'. Data: {data}")
        return
    logger.info(f"AGENT {folio} (SID:{sid}) STARTED identification ({id_type}) at [{lat}, {lon}]. Relaying to supervisors...")
    payload_to_supervisors = {'folio': folio, 'type': id_type}
    emit('agent_identification_started', payload_to_supervisors, namespace='/live', room='supervisors')
    logger.info(f"SERVER emitted 'agent_identification_started' to 'supervisors'. Payload: {payload_to_supervisors}")

# --- EVENTO: Agente ACTUALIZA/COMPLETA identificación ---
# Sin cambios
@socketio.on('update_identification', namespace='/live')
def handle_update_identification(data):
    sid = request.sid
    folio = next((f for f, s in connected_users.items() if s == sid), None)
    if not folio:
        logger.warning(f"WS 'update_identification' from unknown SID: {sid}. Data: {data}")
        return
    id_type = data.get('type')
    id_data = data.get('identification_data')
    lat = data.get('latitude')
    lon = data.get('longitude')
    if not id_type or id_data is None:
        logger.warning(f"AGENT {folio}: Missing 'type' or 'identification_data' in 'update_identification'. Data: {data}")
        return
    logger.info(f"AGENT {folio} (SID:{sid}) UPDATED identification ({id_type}) at [{lat}, {lon}]. Data: {id_data}. Relaying to supervisors...")
    payload_to_supervisors = {
        'folio': folio,
        'type': id_type,
        'identification_data': id_data
    }
    emit('agent_identification_updated', payload_to_supervisors, namespace='/live', room='supervisors')
    logger.info(f"SERVER emitted 'agent_identification_updated' to 'supervisors'. Payload: {payload_to_supervisors}")