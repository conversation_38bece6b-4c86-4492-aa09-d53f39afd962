package com.example.opera

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.os.PowerManager
import androidx.core.app.NotificationCompat
import kotlinx.coroutines.*

/**
 * Servicio en primer plano para mantener la aplicación activa en segundo plano
 */
class BackgroundService : Service() {

    private val NOTIFICATION_ID = 1001
    private val CHANNEL_ID = "background_service_channel"
    private var wakeLock: PowerManager.WakeLock? = null
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    override fun onCreate() {
        super.onCreate()

        // Mover operaciones pesadas a corrutinas
        serviceScope.launch {
            // Crear canal de notificación en hilo secundario
            createNotificationChannel()

            // Adquirir WakeLock para mantener la aplicación activa
            withContext(Dispatchers.Main) {
                val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
                wakeLock = powerManager.newWakeLock(
                    PowerManager.PARTIAL_WAKE_LOCK,
                    "Opera::BackgroundService"
                )
                wakeLock?.acquire(10*60*1000L /*10 minutes*/)
            }
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // Iniciar en primer plano inmediatamente con notificación básica
        serviceScope.launch {
            try {
                val notification = createNotification()
                withContext(Dispatchers.Main) {
                    startForeground(NOTIFICATION_ID, notification)
                }
                android.util.Log.d("BackgroundService", "Servicio iniciado en primer plano correctamente")
            } catch (e: Exception) {
                android.util.Log.e("BackgroundService", "Error al iniciar servicio en primer plano: ${e.message}")
                withContext(Dispatchers.Main) {
                    stopSelf()
                }
            }
        }

        // Si el servicio se mata, reiniciarlo
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        super.onDestroy()
        // Cancelar corrutinas
        serviceScope.cancel()
        // Liberar WakeLock
        wakeLock?.release()
        android.util.Log.d("BackgroundService", "Servicio destruido y recursos liberados")
    }

    /**
     * Crea el canal de notificación para Android 8.0+
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Servicio en segundo plano"
            val descriptionText = "Mantiene la aplicación activa en segundo plano"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * Crea la notificación para el servicio en primer plano
     */
    private fun createNotification(): Notification {
        // Intent para abrir la app al tocar la notificación
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Opera activa")
            .setContentText("La aplicación está funcionando en segundo plano")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true) // No se puede deslizar para cerrar
            .build()
    }
}
