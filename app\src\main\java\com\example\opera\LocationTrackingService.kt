package com.example.opera

import android.Manifest
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Build
import android.os.IBinder
import android.os.Looper
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import kotlinx.coroutines.*
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import java.text.SimpleDateFormat
import java.util.*
import kotlin.coroutines.CoroutineContext

/**
 * Servicio de seguimiento de ubicación GPS que funciona en segundo plano
 * Captura ubicaciones GPS y las envía a un servidor Flask
 */
class LocationTrackingService : Service(), CoroutineScope {

    // Job para manejar las corrutinas del servicio
    private val job = SupervisorJob()
    override val coroutineContext: CoroutineContext
        get() = Dispatchers.IO + job

    // Componentes de ubicación
    private lateinit var locationManager: LocationManager
    private var locationListener: LocationListener? = null
    private var isTracking = false

    // Base de datos local
    private lateinit var locationDatabaseHelper: LocationDatabaseHelper

    // Variables para filtrado de ubicaciones
    private var lastValidLocation: Location? = null
    private var lastValidTimestamp: Long = 0

    // Estadísticas de filtrado
    private var totalLocationsReceived = 0
    private var locationsRejectedByAccuracy = 0
    private var locationsRejectedByTime = 0
    private var locationsRejectedBySpeed = 0
    private var locationsRejectedByProvider = 0
    private var locationsAccepted = 0

    companion object {
        private const val TAG = "LocationTrackingService"
        private const val NOTIFICATION_ID = 1
        private const val CHANNEL_ID = "location_tracking_channel"

        // Configuración del servidor Flask
        private const val SERVER_URL = "https://patagoniaservers.com.ar:5005/api/shifts/track_anonymous"
        private const val CONNECTION_TIMEOUT = 15000        // 15 segundos
        private const val READ_TIMEOUT = 20000               // 20 segundos

        // Constantes para SharedPreferences
        private const val PREFS_NAME = "opera_prefs"
        private const val KEY_ACCESS_TOKEN = "access_token"
        private const val KEY_USER_FOLIO = "user_folio"
        private const val KEY_USER_PASSWORD = "user_password"

        // Configuración de ubicación mejorada
        private const val MIN_TIME_BETWEEN_UPDATES = 10000L  // 10 segundos (menos agresivo)
        private const val MIN_DISTANCE_CHANGE = 5.0f         // 5 metros (menos agresivo)

        // Filtros de calidad GPS
        private const val MAX_ACCURACY_METERS = 50.0f        // Máxima precisión aceptable: 50 metros
        private const val MAX_SPEED_KMH = 120.0              // Velocidad máxima aceptable: 120 km/h
        private const val MIN_TIME_BETWEEN_POINTS = 8000L    // Mínimo 8 segundos entre puntos válidos

        // Configuración de envío por lotes
        private const val BATCH_SEND_INTERVAL = 30000L      // 30 segundos
        private const val MAX_RETRY_ATTEMPTS = 4            // Máximo 4 intentos
        private const val MAX_POINTS_PER_BATCH = 50         // Máximo 50 puntos por lote

        // Configuración de notificaciones
        private const val NOTIFICATION_CHECK_INTERVAL = 60000L  // 60 segundos
        private const val MESSAGES_URL = "https://patagoniaservers.com.ar:5005/api/messages/notification-summary"

        // Configuración de estadísticas
        private const val STATS_LOG_INTERVAL = 300000L         // 5 minutos
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Servicio de seguimiento de ubicación creado")

        // Inicializar base de datos
        locationDatabaseHelper = LocationDatabaseHelper(this)

        // Inicializar LocationManager
        locationManager = getSystemService(Context.LOCATION_SERVICE) as LocationManager

        // Crear canal de notificación
        createNotificationChannel()

        // Iniciar envío por lotes en segundo plano
        startBatchSending()

        // Iniciar verificación de notificaciones en segundo plano
        startNotificationChecking()

        // Iniciar logging de estadísticas en segundo plano
        startStatsLogging()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "Comando de inicio recibido")

        // Crear y mostrar notificación persistente
        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)

        // Iniciar seguimiento de ubicación
        startLocationTracking()

        // Retornar START_STICKY para que el servicio se reinicie si es terminado
        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "Servicio de seguimiento de ubicación destruido")

        // Detener seguimiento
        stopLocationTracking()

        // Cancelar todas las corrutinas
        job.cancel()

        // Cerrar base de datos
        locationDatabaseHelper.close()
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null // Este es un servicio no vinculado
    }

    /**
     * Crea el canal de notificación para Android 8.0+
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Seguimiento de Ubicación",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Canal para el seguimiento de ubicación GPS"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * Crea la notificación persistente
     */
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Opera - Seguimiento Activo")
            .setContentText("Registrando ubicación GPS...")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }

    /**
     * Inicia el seguimiento de ubicación GPS
     */
    private fun startLocationTracking() {
        if (isTracking) {
            Log.d(TAG, "El seguimiento ya está activo")
            return
        }

        // Verificar permisos
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
            ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            Log.e(TAG, "Permisos de ubicación no concedidos")
            return
        }

        // Crear listener de ubicación
        locationListener = object : LocationListener {
            override fun onLocationChanged(location: Location) {
                val provider = location.provider ?: "unknown"
                val accuracy = if (location.hasAccuracy()) location.accuracy else null

                Log.d(TAG, "📍 Nueva ubicación recibida:")
                Log.d(TAG, "   Proveedor: $provider")
                Log.d(TAG, "   Coordenadas: ${location.latitude}, ${location.longitude}")
                Log.d(TAG, "   Precisión: ${accuracy}m")
                Log.d(TAG, "   Timestamp: ${location.time}")

                handleNewLocation(location)
            }

            override fun onProviderEnabled(provider: String) {
                Log.d(TAG, "✅ Proveedor habilitado: $provider")
                updateLocationAvailability()
            }

            override fun onProviderDisabled(provider: String) {
                Log.d(TAG, "❌ Proveedor deshabilitado: $provider")
                updateLocationAvailability()
            }
        }

        try {
            // Priorizar GPS como fuente principal
            if (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
                locationManager.requestLocationUpdates(
                    LocationManager.GPS_PROVIDER,
                    MIN_TIME_BETWEEN_UPDATES,
                    MIN_DISTANCE_CHANGE,
                    locationListener!!,
                    Looper.getMainLooper()
                )
                Log.d(TAG, "🛰️ GPS habilitado como fuente principal")
            }

            // Network como respaldo con configuración menos agresiva
            if (locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
                locationManager.requestLocationUpdates(
                    LocationManager.NETWORK_PROVIDER,
                    MIN_TIME_BETWEEN_UPDATES * 2, // Doble de tiempo para Network
                    MIN_DISTANCE_CHANGE * 2,      // Doble de distancia para Network
                    locationListener!!,
                    Looper.getMainLooper()
                )
                Log.d(TAG, "📶 Network habilitado como respaldo")
            }

            isTracking = true
            Log.d(TAG, "✅ Seguimiento de ubicación iniciado")

            // Verificar disponibilidad inicial
            updateLocationAvailability()

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error iniciando seguimiento de ubicación", e)
        }
    }

    /**
     * Detiene el seguimiento de ubicación GPS
     */
    private fun stopLocationTracking() {
        if (!isTracking) {
            return
        }

        locationListener?.let {
            locationManager.removeUpdates(it)
        }
        locationListener = null
        isTracking = false
        Log.d(TAG, "Seguimiento de ubicación detenido")
    }

    /**
     * Maneja una nueva ubicación recibida aplicando filtros de calidad
     */
    private fun handleNewLocation(location: Location) {
        launch {
            try {
                // Aplicar filtros de calidad
                if (!isLocationValid(location)) {
                    return@launch
                }

                // Guardar en base de datos local
                val id = locationDatabaseHelper.saveLocationPoint(
                    latitude = location.latitude,
                    longitude = location.longitude,
                    accuracy = if (location.hasAccuracy()) location.accuracy else null,
                    timestamp = location.time
                )

                Log.d(TAG, "✅ Ubicación válida guardada con ID: $id")
                Log.d(TAG, "   Coordenadas: ${location.latitude}, ${location.longitude}")
                Log.d(TAG, "   Precisión: ${if (location.hasAccuracy()) "${location.accuracy}m" else "N/A"}")

                // Actualizar última ubicación válida
                lastValidLocation = location
                lastValidTimestamp = System.currentTimeMillis()

                // Enviar ubicación al WebView si la MainActivity está activa
                sendLocationToWebView(location.latitude, location.longitude, location.accuracy)

            } catch (e: Exception) {
                Log.e(TAG, "❌ Error guardando ubicación localmente", e)
            }
        }
    }

    /**
     * Valida si una ubicación cumple con los criterios de calidad
     */
    private fun isLocationValid(location: Location): Boolean {
        totalLocationsReceived++
        val currentTime = System.currentTimeMillis()
        val provider = location.provider ?: "unknown"

        // 1. Filtro de precisión (accuracy)
        if (location.hasAccuracy() && location.accuracy > MAX_ACCURACY_METERS) {
            locationsRejectedByAccuracy++
            Log.w(TAG, "🚫 Ubicación rechazada - Precisión muy baja: ${location.accuracy}m (máx: ${MAX_ACCURACY_METERS}m)")
            return false
        }

        // 2. Filtro de tiempo mínimo entre puntos
        if (currentTime - lastValidTimestamp < MIN_TIME_BETWEEN_POINTS) {
            locationsRejectedByTime++
            Log.w(TAG, "🚫 Ubicación rechazada - Muy frecuente: ${currentTime - lastValidTimestamp}ms (mín: ${MIN_TIME_BETWEEN_POINTS}ms)")
            return false
        }

        // 3. Filtro de velocidad máxima (solo si tenemos ubicación anterior)
        lastValidLocation?.let { lastLoc ->
            val distance = calculateDistance(
                lastLoc.latitude, lastLoc.longitude,
                location.latitude, location.longitude
            )
            val timeDiffSeconds = (currentTime - lastValidTimestamp) / 1000.0
            val speedKmh = (distance / timeDiffSeconds) * 3.6 // m/s a km/h

            if (speedKmh > MAX_SPEED_KMH) {
                locationsRejectedBySpeed++
                Log.w(TAG, "🚫 Ubicación rechazada - Velocidad imposible: ${String.format("%.1f", speedKmh)} km/h (máx: ${MAX_SPEED_KMH} km/h)")
                Log.w(TAG, "   Distancia: ${String.format("%.1f", distance)}m en ${String.format("%.1f", timeDiffSeconds)}s")
                return false
            }
        }

        // 4. Priorizar GPS sobre Network si ambos están disponibles
        if (provider == LocationManager.NETWORK_PROVIDER && lastValidLocation != null) {
            val timeSinceLastGps = currentTime - lastValidTimestamp
            if (timeSinceLastGps < MIN_TIME_BETWEEN_UPDATES * 2) {
                locationsRejectedByProvider++
                Log.w(TAG, "🚫 Ubicación Network rechazada - GPS reciente disponible")
                return false
            }
        }

        // 5. Validar coordenadas básicas
        if (location.latitude == 0.0 && location.longitude == 0.0) {
            locationsRejectedByAccuracy++ // Contar como error de precisión
            Log.w(TAG, "🚫 Ubicación rechazada - Coordenadas inválidas (0,0)")
            return false
        }

        locationsAccepted++
        Log.d(TAG, "✅ Ubicación válida - Proveedor: $provider, Precisión: ${if (location.hasAccuracy()) "${location.accuracy}m" else "N/A"}")
        return true
    }

    /**
     * Calcula la distancia entre dos puntos GPS en metros
     */
    private fun calculateDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Double {
        val earthRadius = 6371000.0 // Radio de la Tierra en metros
        val dLat = Math.toRadians(lat2 - lat1)
        val dLon = Math.toRadians(lon2 - lon1)
        val a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                Math.sin(dLon / 2) * Math.sin(dLon / 2)
        val c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
        return earthRadius * c
    }

    /**
     * Envía la ubicación al WebView a través de la interfaz estática
     */
    private fun sendLocationToWebView(latitude: Double, longitude: Double, accuracy: Float?) {
        try {
            MainActivity.updateLocationFromService(latitude, longitude, accuracy)
            Log.d(TAG, "📍 Ubicación enviada al WebView: $latitude, $longitude")
        } catch (e: Exception) {
            Log.e(TAG, "Error enviando ubicación al WebView", e)
        }
    }

    /**
     * Actualiza el estado de disponibilidad de ubicación
     */
    private fun updateLocationAvailability() {
        val isGpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
        val isNetworkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
        val isLocationAvailable = isGpsEnabled || isNetworkEnabled

        Log.d(TAG, "Disponibilidad de ubicación: $isLocationAvailable")

        if (!isLocationAvailable && isTracking) {
            // Si no hay ubicación disponible, reiniciar el seguimiento después de un tiempo
            launch {
                delay(5000) // Esperar 5 segundos
                if (!locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) &&
                    !locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
                    Log.w(TAG, "🔄 GPS sigue no disponible, reiniciando seguimiento...")
                    restartLocationTracking()
                }
            }
        }
    }

    /**
     * Reinicia el seguimiento de ubicación
     */
    private suspend fun restartLocationTracking() {
        Log.d(TAG, "🔄 Reiniciando seguimiento de ubicación...")
        stopLocationTracking()
        delay(2000) // Esperar 2 segundos antes de reiniciar
        startLocationTracking()
    }

    /**
     * Inicia el envío por lotes de ubicaciones
     */
    private fun startBatchSending() {
        launch {
            while (isActive) {
                try {
                    sendPendingLocations()
                } catch (e: Exception) {
                    Log.e(TAG, "Error en envío por lotes", e)
                }
                delay(BATCH_SEND_INTERVAL)
            }
        }
    }

    /**
     * Envía todas las ubicaciones pendientes al servidor en lotes ordenados
     */
    private suspend fun sendPendingLocations() {
        val allPendingLocations = locationDatabaseHelper.getUnsentLocationPoints()
        if (allPendingLocations.isEmpty()) {
            Log.d(TAG, "No hay ubicaciones pendientes para enviar")
            return
        }

        Log.d(TAG, "📦 Total de ubicaciones pendientes: ${allPendingLocations.size}")

        // Procesar en lotes para no sobrecargar el servidor
        val batches = allPendingLocations.chunked(MAX_POINTS_PER_BATCH)
        Log.d(TAG, "📦 Procesando en ${batches.size} lote(s) de máximo $MAX_POINTS_PER_BATCH puntos")

        var totalSent = 0
        var totalFailed = 0

        for ((batchIndex, batch) in batches.withIndex()) {
            Log.d(TAG, "📤 Procesando lote ${batchIndex + 1}/${batches.size} (${batch.size} puntos)")

            for (locationData in batch) {
                val id = locationData["id"] as? Long
                val timestamp = locationData["timestamp"] as? Long
                val timestampStr = if (timestamp != null) formatTimestamp(timestamp) else "N/A"

                Log.d(TAG, "📍 Enviando punto ID=$id, Coords=${locationData["latitude"]}, ${locationData["longitude"]}, Timestamp=$timestampStr")

                val success = tryServerConnection(locationData, SERVER_URL, "servidor principal")

                if (success) {
                    if (id != null) {
                        locationDatabaseHelper.markLocationAsSent(id)
                        totalSent++
                        Log.d(TAG, "✅ Punto $id enviado exitosamente")
                    }
                } else {
                    totalFailed++
                    Log.w(TAG, "❌ Error enviando punto $id")

                    // Si falla un punto, esperar un poco antes del siguiente
                    delay(2000)
                }
            }

            // Pausa entre lotes para no saturar el servidor
            if (batchIndex < batches.size - 1) {
                Log.d(TAG, "⏸️ Pausa entre lotes...")
                delay(5000)
            }
        }

        Log.i(TAG, "📊 Resumen de envío: $totalSent enviados, $totalFailed fallidos de ${allPendingLocations.size} total")

        // Limpiar puntos antiguos enviados
        locationDatabaseHelper.cleanOldSentPoints()
    }

    /**
     * Intenta conectar a un servidor específico
     */
    private suspend fun tryServerConnection(locationData: Map<String, Any?>, serverUrl: String, serverName: String): Boolean {
        var connection: HttpURLConnection? = null
        try {
            // Crear la URL
            val url = URL(serverUrl)
            connection = url.openConnection() as HttpURLConnection

            // Obtener credenciales de usuario
            val userCredentials = getUserCredentials()
            if (userCredentials.first.isNullOrEmpty() || userCredentials.second.isNullOrEmpty()) {
                Log.e(TAG, "No hay credenciales de usuario disponibles")
                return false
            }

            // Configurar la conexión
            connection.apply {
                requestMethod = "POST"
                setRequestProperty("Content-Type", "application/json")
                setRequestProperty("Accept", "application/json")
                connectTimeout = CONNECTION_TIMEOUT
                readTimeout = READ_TIMEOUT
                doOutput = true
                doInput = true
            }

            // Crear el JSON con los datos de ubicación y credenciales (formato esperado por el servidor)
            val jsonData = JSONObject().apply {
                put("folio", userCredentials.first)
                put("password", userCredentials.second)
                put("latitude", locationData["latitude"])
                put("longitude", locationData["longitude"])
                put("accuracy", locationData["accuracy"] ?: JSONObject.NULL)
                put("timestamp", locationData["timestamp"] ?: System.currentTimeMillis())
            }

            Log.d(TAG, "🚀 Iniciando envío de ubicación: ${locationData["latitude"]}, ${locationData["longitude"]}")
            Log.d(TAG, "📤 Enviando a $serverName - JSON: $jsonData")

            // Enviar datos
            val outputStream = OutputStreamWriter(connection.outputStream)
            outputStream.write(jsonData.toString())
            outputStream.flush()
            outputStream.close()

            // Leer respuesta
            val responseCode = connection.responseCode
            Log.d(TAG, "📡 $serverName - Código de respuesta: $responseCode")

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Leer respuesta exitosa
                val inputStream = BufferedReader(InputStreamReader(connection.inputStream))
                val response = inputStream.readText()
                inputStream.close()

                Log.d(TAG, "✅ Ubicación enviada exitosamente a $serverName: ID=${locationData["id"]}")
                Log.d(TAG, "📥 Respuesta del $serverName: $response")
                return true
            } else {
                // Leer respuesta de error
                val errorStream = connection.errorStream
                val errorResponse = if (errorStream != null) {
                    BufferedReader(InputStreamReader(errorStream)).readText()
                } else {
                    "Sin detalles del error"
                }
                Log.e(TAG, "❌ Error del $serverName (código $responseCode): $errorResponse")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Excepción enviando a $serverName", e)
            return false
        } finally {
            connection?.disconnect()
        }
    }

    /**
     * Obtiene el token JWT guardado en SharedPreferences
     */
    private fun getAccessToken(): String? {
        val sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return sharedPreferences.getString(KEY_ACCESS_TOKEN, null)
    }

    /**
     * Obtiene las credenciales de usuario guardadas en SharedPreferences
     * @return Pair<folio, password>
     */
    private fun getUserCredentials(): Pair<String?, String?> {
        val sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val folio = sharedPreferences.getString(KEY_USER_FOLIO, null)
        val password = sharedPreferences.getString(KEY_USER_PASSWORD, null)
        return Pair(folio, password)
    }

    /**
     * Formatea un timestamp a string legible
     */
    private fun formatTimestamp(timestamp: Long): String {
        val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
        sdf.timeZone = TimeZone.getTimeZone("UTC")
        return sdf.format(Date(timestamp))
    }

    /**
     * Inicia la verificación periódica de notificaciones
     */
    private fun startNotificationChecking() {
        launch {
            while (isActive) {
                try {
                    checkForNotifications()
                } catch (e: Exception) {
                    Log.e(TAG, "Error en verificación de notificaciones", e)
                }
                delay(NOTIFICATION_CHECK_INTERVAL)
            }
        }
    }

    /**
     * Verifica si hay notificaciones pendientes en el servidor
     */
    private suspend fun checkForNotifications() {
        val userCredentials = getUserCredentials()
        if (userCredentials.first.isNullOrEmpty() || userCredentials.second.isNullOrEmpty()) {
            Log.d(TAG, "No hay credenciales para verificar notificaciones")
            return
        }

        var connection: HttpURLConnection? = null
        try {
            val url = URL(MESSAGES_URL)
            connection = url.openConnection() as HttpURLConnection

            // Configurar la conexión
            connection.apply {
                requestMethod = "GET"
                setRequestProperty("Content-Type", "application/json")
                setRequestProperty("Accept", "application/json")
                connectTimeout = CONNECTION_TIMEOUT
                readTimeout = READ_TIMEOUT
                doInput = true
            }

            // Crear token JWT temporal para la request
            val tempToken = createTempAuthToken(userCredentials.first!!, userCredentials.second!!)
            if (tempToken != null) {
                connection.setRequestProperty("Authorization", "Bearer $tempToken")
            } else {
                Log.e(TAG, "No se pudo crear token temporal para notificaciones")
                return
            }

            val responseCode = connection.responseCode
            Log.d(TAG, "🔔 Verificación de notificaciones - Código: $responseCode")

            if (responseCode == HttpURLConnection.HTTP_OK) {
                val inputStream = BufferedReader(InputStreamReader(connection.inputStream))
                val response = inputStream.readText()
                inputStream.close()

                Log.d(TAG, "📥 Respuesta de notificaciones: $response")

                // Parsear respuesta JSON
                val jsonResponse = JSONObject(response)
                val hasNotifications = jsonResponse.optBoolean("has_notifications", false)
                val totalNotifications = jsonResponse.optInt("total_notifications", 0)
                val messagesCount = jsonResponse.optInt("unread_messages_count", 0)
                val noveltiesCount = jsonResponse.optInt("new_novelties_count", 0)

                if (hasNotifications && totalNotifications > 0) {
                    Log.i(TAG, "🔔 Notificaciones encontradas: $totalNotifications ($messagesCount mensajes, $noveltiesCount novedades)")
                    showNotification(messagesCount, noveltiesCount)
                } else {
                    Log.d(TAG, "✅ No hay notificaciones pendientes")
                }
            } else {
                Log.w(TAG, "❌ Error verificando notificaciones: código $responseCode")
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Excepción verificando notificaciones", e)
        } finally {
            connection?.disconnect()
        }
    }

    /**
     * Crea un token JWT temporal usando las credenciales del usuario
     */
    private suspend fun createTempAuthToken(folio: String, password: String): String? {
        var connection: HttpURLConnection? = null
        try {
            val url = URL("https://patagoniaservers.com.ar:5005/api/auth/login")
            connection = url.openConnection() as HttpURLConnection

            connection.apply {
                requestMethod = "POST"
                setRequestProperty("Content-Type", "application/json")
                setRequestProperty("Accept", "application/json")
                connectTimeout = CONNECTION_TIMEOUT
                readTimeout = READ_TIMEOUT
                doOutput = true
                doInput = true
            }

            // Crear JSON de login
            val loginData = JSONObject().apply {
                put("folio", folio)
                put("password", password)
            }

            // Enviar datos
            val outputStream = OutputStreamWriter(connection.outputStream)
            outputStream.write(loginData.toString())
            outputStream.flush()
            outputStream.close()

            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK) {
                val inputStream = BufferedReader(InputStreamReader(connection.inputStream))
                val response = inputStream.readText()
                inputStream.close()

                val jsonResponse = JSONObject(response)
                val token = jsonResponse.optString("access_token")

                if (token.isNotEmpty()) {
                    Log.d(TAG, "🔑 Token temporal creado exitosamente")
                    return token
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error creando token temporal", e)
        } finally {
            connection?.disconnect()
        }

        return null
    }

    /**
     * Muestra una notificación local con el resumen de mensajes y novedades
     */
    private fun showNotification(messagesCount: Int, noveltiesCount: Int) {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // Crear canal de notificación para mensajes (diferente al del servicio)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                "messages_channel",
                "Mensajes y Novedades",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notificaciones de mensajes del operador y novedades"
                enableVibration(true)
                enableLights(true)
            }
            notificationManager.createNotificationChannel(channel)
        }

        // Crear intent para abrir la app
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Crear texto de la notificación
        val title = when {
            messagesCount > 0 && noveltiesCount > 0 -> "Mensajes y Novedades"
            messagesCount > 0 -> "Nuevos Mensajes"
            noveltiesCount > 0 -> "Nuevas Novedades"
            else -> "Notificaciones"
        }

        val text = buildString {
            if (messagesCount > 0) {
                append("$messagesCount mensaje${if (messagesCount > 1) "s" else ""}")
            }
            if (messagesCount > 0 && noveltiesCount > 0) {
                append(" y ")
            }
            if (noveltiesCount > 0) {
                append("$noveltiesCount novedad${if (noveltiesCount > 1) "es" else ""}")
            }
            append(" pendiente${if (messagesCount + noveltiesCount > 1) "s" else ""}")
        }

        // Crear y mostrar notificación
        val notification = NotificationCompat.Builder(this, "messages_channel")
            .setContentTitle(title)
            .setContentText(text)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .setCategory(NotificationCompat.CATEGORY_MESSAGE)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .build()

        notificationManager.notify(2, notification) // ID diferente al servicio (que usa 1)

        Log.i(TAG, "🔔 Notificación mostrada: $title - $text")
    }

    /**
     * Inicia el logging periódico de estadísticas de filtrado
     */
    private fun startStatsLogging() {
        launch {
            while (isActive) {
                try {
                    logFilteringStats()
                } catch (e: Exception) {
                    Log.e(TAG, "Error en logging de estadísticas", e)
                }
                delay(STATS_LOG_INTERVAL)
            }
        }
    }

    /**
     * Registra las estadísticas de filtrado GPS en los logs
     */
    private fun logFilteringStats() {
        if (totalLocationsReceived == 0) {
            Log.i(TAG, "📊 Estadísticas GPS: Sin datos aún")
            return
        }

        val acceptanceRate = (locationsAccepted.toDouble() / totalLocationsReceived * 100)
        val pendingCount = locationDatabaseHelper.getUnsentCount()

        Log.i(TAG, "📊 ===== ESTADÍSTICAS GPS (últimos ${STATS_LOG_INTERVAL/60000} min) =====")
        Log.i(TAG, "📊 Total recibidas: $totalLocationsReceived")
        Log.i(TAG, "📊 Aceptadas: $locationsAccepted (${String.format("%.1f", acceptanceRate)}%)")
        Log.i(TAG, "📊 Rechazadas por precisión: $locationsRejectedByAccuracy")
        Log.i(TAG, "📊 Rechazadas por tiempo: $locationsRejectedByTime")
        Log.i(TAG, "📊 Rechazadas por velocidad: $locationsRejectedBySpeed")
        Log.i(TAG, "📊 Rechazadas por proveedor: $locationsRejectedByProvider")
        Log.i(TAG, "📊 Pendientes de envío: $pendingCount")
        Log.i(TAG, "📊 ================================================")

        // Resetear estadísticas para el próximo período
        totalLocationsReceived = 0
        locationsAccepted = 0
        locationsRejectedByAccuracy = 0
        locationsRejectedByTime = 0
        locationsRejectedBySpeed = 0
        locationsRejectedByProvider = 0
    }
}
