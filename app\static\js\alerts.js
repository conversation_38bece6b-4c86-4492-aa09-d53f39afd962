// static/js/alerts.js

/**
 * Muestra un alerta Bootstrap y la cierra automáticamente.
 * @param {'alert-danger'|'alert-success'} type 
 * @param {string} message 
 * @param {number} duration tiempo en ms para auto-cerrar (por defecto 5s)
 */
function showAlert(type, message, duration = 5000) {
    const placeholder = document.getElementById('agent-alert-placeholder');
    if (!placeholder) return;
    placeholder.innerHTML = `
      <div class="alert ${type} alert-dismissible fade show" role="alert">
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Cerrar"></button>
      </div>`;
    setTimeout(() => {
      const el = placeholder.querySelector('.alert');
      if (el) bootstrap.Alert.getOrCreateInstance(el).close();
    }, duration);
  }
  
  /**
   * Busca en novelties si existe un texto (DNI o patente) y dispara el alerta.
   * @param {string} text 
   * @param {'DNI'|'patente'} label 
   */
  function checkNoveltiesAndAlert(text, label) {
    if (!text) return;
    axios.get(`${document.body.dataset.apiBaseUrl || '/api'}/novelties/all?per_page=100`)
      .then(resp => {
        const novs = resp.data.novelties || resp.data || [];
        const found = novs.some(n => n.content.includes(text));
        if (found) {
          showAlert('alert-danger', `¡Alerta! ${label} "${text}" figura en novedades.`);
        } else {
          showAlert('alert-success', `${label} "${text}" sin novedades.`);
        }
      })
      .catch(err => console.error('No se pudieron cargar novedades para alerta:', err));
  }
  
  // ------------------------
  // Interceptor global de Axios
  // ------------------------
  axios.interceptors.response.use(response => {
    const url = response.config.url || '';
    // después de identificar persona
    if (url.match(/\/identifications\/person$/)) {
      const dni = response.data.dni_number || response.data.dni_raw_string;
      checkNoveltiesAndAlert(dni, 'DNI');
    }
    // después de identificar vehículo
    if (url.match(/\/identifications\/vehicle$/)) {
      const plate = response.data.plate;
      checkNoveltiesAndAlert(plate, 'Patente');
    }
    return response;
  }, error => Promise.reject(error));
  