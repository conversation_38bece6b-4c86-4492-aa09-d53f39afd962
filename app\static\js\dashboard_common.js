// app/static/js/dashboard_common.js

if (!window.__dashboardCommonLoaded__) {
    window.__dashboardCommonLoaded__ = true;

    window.allowedRoles = ['supervisor', 'comando'];

    const loginUrl = document.body.dataset.loginUrl || '/dashboard/login';
    const dashboardUrl = document.body.dataset.dashboardUrl || '/dashboard/';

    function checkAuthAndRole() {
        const token = localStorage.getItem('accessToken');
        const userRole = localStorage.getItem('userRole');

        if (!token || !userRole || !window.allowedRoles.includes(userRole)) {
            console.warn("Auth Check Failed. Redirecting to login.", {tokenExists: !!token, role: userRole});
            clearAuthData();
            if (window.location.pathname !== loginUrl) {
                window.location.href = loginUrl;
            }
            return false;
        }

        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        axios.interceptors.response.use(
            response => response,
            error => {
                if (error.response && [401, 403, 422].includes(error.response.status)) {
                    console.warn(`Axios interceptor caught ${error.response.status}. Forzando cierre de sesión.`);
                    logout();
                }
                return Promise.reject(error);
            }
        );

        return true;
    }

    function clearAuthData() {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('userRole');
        localStorage.removeItem('userFolio');
    }

    function logout() {
        console.log("Logging out...");
        clearAuthData();
        window.location.href = loginUrl;
    }

    function getAuthToken() {
        return localStorage.getItem('accessToken');
    }

    function getUserRole() {
        return localStorage.getItem('userRole');
    }

    function getUserFolio() {
        return localStorage.getItem('userFolio');
    }

    function formatDateTime(isoString) {
        if (!isoString) return 'N/A';
        if (typeof dayjs === 'undefined' || !dayjs.Ls || !dayjs.Ls['es'] || !dayjs.extend) {
            console.warn("Day.js no cargado correctamente. Usando fallback.");
            try {
                return new Date(isoString).toLocaleString('es-AR', { dateStyle: 'short', timeStyle: 'short' });
            } catch (e) {
                return 'Fecha inválida';
            }
        }
        dayjs.extend(window.dayjs_plugin_localizedFormat);
        return dayjs(isoString).locale('es').format('DD/MM/YYYY HH:mm');
    }

    function updateNavbarUser(defaultText = 'Usuario') {
        const folioSpan = document.getElementById('navbar-user-folio');
        const userFolio = getUserFolio();
        if (folioSpan) {
            folioSpan.textContent = userFolio || defaultText;
        }
    }

    // Exponer funciones a nivel global si se usan desde otros scripts
    window.checkAuthAndRole = checkAuthAndRole;
    window.logout = logout;
    window.updateNavbarUser = updateNavbarUser;
    window.formatDateTime = formatDateTime;
}
