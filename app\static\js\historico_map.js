// static/js/historico_map.js

let histMap;
let agentPolylines = {};  // folio → L.Polyline
let agentMarkers   = [];  // Array de L<PERSON>Marker

function initHistoricalMap() {
  if (!checkAuthAndRole()) return;
  console.log("[HIST_INIT] Inicializando Mapa Histórico");

  // 1) Crear mapa
  histMap = L.map('map').setView([-40.8119, -62.9966], 13);
  <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors'
  }).addTo(histMap);

  // 2) Referencias UI
  const btnLoad   = document.getElementById('btn-load-agents');
  const btnShow   = document.getElementById('btn-show-map');
  const chkAll    = document.getElementById('check-all-agents');
  const container = document.getElementById('agents-container');

  // “Seleccionar todos” / “Seleccionar ninguno”
  chkAll.addEventListener('change', () => {
    container.querySelectorAll('input[type=checkbox]')
      .forEach(ch => ch.checked = chkAll.checked);
  });

  // Cargar lista de agentes para el periodo
  btnLoad.addEventListener('click', () => {
    const from = document.getElementById('date-from').value;
    const to   = document.getElementById('date-to').value;
    console.log(`[HIST] Cargando agentes entre ${from} → ${to}`);
    axios.get('/api/dashboard/historical-agents', {
      params: { date_from: from, date_to: to }
    })
    .then(resp => renderAgentsCheckboxes(resp.data))
    .catch(err => {
      console.error('[HIST] Error cargando agentes:', err.response || err);
      alert('No se pudieron cargar los agentes históricos.');
    });
  });

  // “Ver en Mapa” dibuja recorridos e identificaciones
  btnShow.addEventListener('click', () => {
    const dateFrom    = document.getElementById('date-from').value;
    const dateTo      = document.getElementById('date-to').value;
    const type        = document.getElementById('type-filter').value;
    const agentFolios = Array.from(
      container.querySelectorAll('input[type=checkbox]:checked')
    ).map(ch => ch.value);

    console.log(`[HIST] Dibujando para agentes [${agentFolios.join(',')}]`);
    drawHistoricMap({ dateFrom, dateTo, agentFolios, type });
  });
}

function renderAgentsCheckboxes(agents) {
  const c = document.getElementById('agents-container');
  c.innerHTML = '';
  agents.forEach(a => {
    const div = document.createElement('div');
    div.className = 'form-check form-check-inline';
    div.innerHTML = `
      <input class="form-check-input"
             type="checkbox"
             id="agent-${a.folio}"
             value="${a.folio}"
             checked>
      <label class="form-check-label" for="agent-${a.folio}">
        ${a.folio} (${a.name || '—'})
      </label>
    `;
    c.appendChild(div);
  });
  // Actualizar estado de “Seleccionar todos”
  const all = c.querySelectorAll('input[type=checkbox]');
  const sel = c.querySelectorAll('input[type=checkbox]:checked');
  document.getElementById('check-all-agents').checked = (all.length === sel.length);
}

async function drawHistoricMap({ dateFrom, dateTo, agentFolios, type }) {
  // 1) Limpiar capas anteriores
  Object.values(agentPolylines).forEach(poly => histMap.removeLayer(poly));
  agentPolylines = {};
  agentMarkers.forEach(mk => histMap.removeLayer(mk));
  agentMarkers = [];

  // 2) Traer y dibujar tracks filtrados
  try {
    const { data: tracks } = await axios.get('/api/dashboard/historical-tracks', {
      params: {
        date_from:    dateFrom,
        date_to:      dateTo,
        agent_folios: agentFolios.join(',')
      }
    });
    console.log(`[HIST] ${tracks.length} tracks recibidos`);

    // Por si el backend devolviera más de lo pedido, volvemos a filtrar
    const filtered = tracks.filter(t => agentFolios.includes(t.folio));

    filtered.forEach(agent => {
      if (!agent.track || agent.track.length < 2) return;
      const latlngs = agent.track.map(p => [p.latitude, p.longitude]);
      const poly = L.polyline(latlngs, { weight: 4, opacity: 0.6 })
                     .addTo(histMap);
      agentPolylines[agent.folio] = poly;
    });
  } catch (e) {
    console.error('[HIST] Error cargando tracks:', e.response || e);
    alert('No se pudieron cargar los recorridos.');
  }

// 3) Traer y dibujar identificaciones filtradas
try {
    const { data: ids } = await axios.get('/api/dashboard/historical-identifications', {
      params: {
        date_from:    dateFrom,
        date_to:      dateTo,
        agent_folios: agentFolios.join(','),
        type
      }
    });
    console.log(`[HIST] ${ids.length} identificaciones recibidas`);
    ids.forEach(item => {
      // Usamos el mismo icono 3.png para todos
      const histIcon = L.icon({
        iconUrl:    '/static/img/3.png',
        iconSize:   [30, 30],    // ancho, alto en px
        iconAnchor: [15, 30],    // punto que "toca" la coordenada (mitad inferior)
        popupAnchor:[0, -30]     // desplaza el popup 30px hacia arriba
      });
  
      const mk = L.marker([item.latitude, item.longitude], { icon: histIcon })
        .addTo(histMap)
        .bindPopup(`
          <b>${ item.type === 'person' ? 'Persona' : 'Vehículo' }</b><br>
          Oficial: ${ item.folio }<br>
          Fecha: ${ new Date(item.timestamp).toLocaleString() }
        `);
  
      agentMarkers.push(mk);
    });
  } catch (e) {
    console.error('[HIST] Error cargando identificaciones:', e.response || e);
    alert('No se pudieron cargar las identificaciones.');
  }

  // 4) Ajustar vista al conjunto de capas
  const layers = [
    ...Object.values(agentPolylines),
    ...agentMarkers
  ];
  if (layers.length) {
    const fg = L.featureGroup(layers);
    histMap.fitBounds(fg.getBounds(), { padding: [20, 20] });
  }
}

// Arrancar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', initHistoricalMap);
