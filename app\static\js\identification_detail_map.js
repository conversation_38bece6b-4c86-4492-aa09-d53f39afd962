// static/js/identification_detail_map.js

document.addEventListener('DOMContentLoaded', () => {
    if (!currentIdentification.lat || !currentIdentification.lon) return;

    const map = L.map('map').setView([currentIdentification.lat, currentIdentification.lon], 16);

    <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);

    // Íconos personalizados
    const iconActual = L.icon({
        iconUrl: '/static/img/11.png',
        iconSize: [32, 32],
        iconAnchor: [16, 32]
    });

    const iconAnterior = L.icon({
        iconUrl: '/static/img/3.png',
        iconSize: [24, 24],
        iconAnchor: [12, 24]
    });

    // Marcador actual
    L.marker([currentIdentification.lat, currentIdentification.lon], {
        icon: iconActual
    }).addTo(map).bindPopup(`
        <b>ID Actual: ${currentIdentification.id}</b><br>
        Oficial: ${currentIdentification.officer_folio}<br>
        ${new Date(currentIdentification.timestamp).toLocaleString('es-AR')}
    `);

    // Marcadores anteriores
    currentIdentification.others.forEach(item => {
        const detailsUrl = item.is_person
            ? `/dashboard/identifications/person/${item.id}`
            : `/dashboard/identifications/vehicle/${item.id}`;

        const popupContent = `
            <b><a href="${detailsUrl}" target="_blank">ID ${item.id}</a></b><br>
            Oficial: ${item.officer_folio}<br>
            ${new Date(item.timestamp).toLocaleString('es-AR')}
        `;
        L.marker([item.lat, item.lon], {
            icon: iconAnterior
        }).addTo(map).bindPopup(popupContent);
    });
});