// static/js/identifications.js

let currentPageIdent = 1;
const identPerPage = 15;
let identTableBody = null;
let identPaginationControls = null;
let identFilterForm = null;
let shiftIdFromURL = null;

// Verifica autenticación y configura Axios
function checkAuthAndRole() {
    const token = localStorage.getItem('accessToken');
    const role = localStorage.getItem('userRole');

    if (!token) {
        console.warn("No autenticado. Redirigiendo a login...");
        // window.location.href = '/login';
        return false;
    }

    // Configura token en Axios
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    return true;
}

// Actualiza la navbar con el folio y nombre del usuario
function updateNavbarUser() {
    const userFolio = localStorage.getItem('userFolio');
    const userName = localStorage.getItem('userName');
    const element = document.getElementById('navbar-user-info');
    if (element && userFolio) {
        element.textContent = `${userFolio} - ${userName || ''}`;
    }
}

// Formatea una fecha ISO a DD/MM/YYYY HH:mm:ss
function formatDateTime(isoString) {
    if (!isoString) return '-';
    try {
        if (typeof dayjs === 'function') {
            return dayjs(isoString).format('DD/MM/YYYY HH:mm:ss');
        } else {
            const date = new Date(isoString);
            return date.toLocaleString('es-AR');
        }
    } catch (e) {
        console.error("Error formateando fecha:", isoString, e);
        return isoString;
    }
}

// Inicializa la tabla de identificaciones
function initIdentificationsTable() {
    if (!checkAuthAndRole()) return;

    identTableBody = document.getElementById('identifications-table-body');
    identPaginationControls = document.getElementById('pagination-controls');
    identFilterForm = document.getElementById('filter-form');

    const urlParams = new URLSearchParams(window.location.search);
    shiftIdFromURL = urlParams.get('shift_id');

    if (!identTableBody || !identPaginationControls || !identFilterForm) {
        console.error("Faltan elementos esenciales del DOM en la página de identificaciones.");
        return;
    }

    updateNavbarUser();

    identFilterForm.addEventListener('submit', (event) => {
        event.preventDefault();
        fetchIdentifications(1);
    });

    fetchIdentifications(1);
}

// Renderiza la tabla con identificaciones
function renderIdentifications(identifications) {
    if (!identTableBody) return;
    identTableBody.innerHTML = '';

    if (!identifications || identifications.length === 0) {
        identTableBody.innerHTML = '<tr><td colspan="8" class="text-center">No se encontraron identificaciones con los filtros aplicados.</td></tr>';
        return;
    }

    identifications.forEach(item => {
        const isPerson = 'last_name' in item || 'dni_number' in item;
        const type = isPerson ? 'Persona' : 'Vehículo';
        const typeBadge = isPerson ? 'bg-primary' : 'bg-info';
        const mainId = isPerson ? (item.dni_number || 'S/DNI') : (item.plate || 'S/PAT');
        const secondaryId = isPerson
            ? `${item.last_name || ''}, ${item.first_names || ''}`.replace(/^, |, $/g, '').trim()
            : `${item.brand || ''} ${item.model || ''}`.trim();
        const officerInfo = item.officer?.folio || 'N/A';
        const locationIcon = (item.location_lat != null && item.location_lon != null)
            ? `<a href="https://maps.google.com/?q=${item.location_lat},${item.location_lon}" target="_blank" title="Ver ubicación en Google Maps">
                   <i class="bi bi-geo-alt-fill text-success"></i>
               </a>`
            : '<span class="text-muted">-</span>';

        const detailsUrl = isPerson
            ? `/dashboard/identifications/person/${item.id}`
            : `/dashboard/identifications/vehicle/${item.id}`;

        const row = `
            <tr>
                <td>${item.id}</td>
                <td><span class="badge ${typeBadge}">${type}</span></td>
                <td>${formatDateTime(item.timestamp)}</td>
                <td>${officerInfo}</td>
                <td>${mainId}</td>
                <td>${secondaryId || '-'}</td>
                <td><a href="${detailsUrl}" class="btn btn-sm btn-outline-primary">Ver</a></td>
                <td class="text-center">${locationIcon}</td>
                <td>${item.shift_id || 'N/A'}</td>
            </tr>
        `;
        identTableBody.innerHTML += row;
    });
}

// Renderiza la paginación
function renderIdentPagination(totalItems, totalPages) {
    if (!identPaginationControls) return;
    identPaginationControls.innerHTML = '';
    if (!totalPages || totalPages <= 1) return;

    const createPageItem = (page, label, isDisabled = false, isActive = false) => {
        const liClass = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
        const onclick = (!isDisabled && !isActive) ? `window.fetchIdentifications(${page})` : 'event.preventDefault();';
        return `<li class="${liClass}"><a class="page-link" href="#" onclick="${onclick}">${label}</a></li>`;
    };

    identPaginationControls.innerHTML += createPageItem(currentPageIdent - 1, '«', currentPageIdent === 1);

    const maxPagesToShow = 5;
    let startPage = Math.max(1, currentPageIdent - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);
    if (endPage - startPage + 1 < maxPagesToShow) {
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    if (startPage > 1) {
        identPaginationControls.innerHTML += createPageItem(1, 1, false, currentPageIdent === 1);
        if (startPage > 2) {
            identPaginationControls.innerHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        identPaginationControls.innerHTML += createPageItem(i, i, false, i === currentPageIdent);
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            identPaginationControls.innerHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        identPaginationControls.innerHTML += createPageItem(totalPages, totalPages, false, totalPages === currentPageIdent);
    }

    identPaginationControls.innerHTML += createPageItem(currentPageIdent + 1, '»', currentPageIdent === totalPages);
}

// Función global para obtener identificaciones
window.fetchIdentifications = function(page = 1) {
    currentPageIdent = page;
    if (!identTableBody || !identFilterForm) {
        console.warn("fetchIdentifications llamado pero faltan elementos del DOM.");
        return;
    }

    identTableBody.innerHTML = '<tr><td colspan="8" class="text-center">Cargando identificaciones...</td></tr>';

    const params = new URLSearchParams({
        page: currentPageIdent,
        per_page: identPerPage
    });

    // Capturamos los filtros del DOM
    const filterType          = document.getElementById('filter-type');
    const filterOfficer       = document.getElementById('filter-officer');
    const filterDocOrPlate    = document.getElementById('filter-doc-or-plate');
    const filterNameOrBrand   = document.getElementById('filter-name-or-brand');
    const filterDateFrom      = document.getElementById('filter-date-from');
    const filterDateTo        = document.getElementById('filter-date-to');

    if (filterType && filterType.value)               params.append('type',           filterType.value);
    if (filterOfficer && filterOfficer.value)         params.append('officer_folio',  filterOfficer.value);
    if (filterDocOrPlate && filterDocOrPlate.value)   params.append('doc_or_plate',   filterDocOrPlate.value);
    if (filterNameOrBrand && filterNameOrBrand.value) params.append('name_or_brand',  filterNameOrBrand.value);
    if (filterDateFrom && filterDateFrom.value)       params.append('date_from',      filterDateFrom.value);
    if (filterDateTo   && filterDateTo.value)         params.append('date_to',        filterDateTo.value);
    if (shiftIdFromURL)                               params.append('shift_id',       shiftIdFromURL);

    axios.get(`/api/dashboard/identifications-list?${params.toString()}`)
        .then(response => {
            renderIdentifications(response.data.identifications || []);
            renderIdentPagination(response.data.total, response.data.pages);
        })
        .catch(error => {
            console.error("Error fetching identifications:", error.response || error);
            let errorMsg = "Error al cargar identificaciones.";
            if (error.response) {
                if (error.response.status === 401) {
                    errorMsg = "No autorizado. Por favor, inicie sesión de nuevo.";
                } else if (error.response.data && error.response.data.msg) {
                    errorMsg = error.response.data.msg;
                } else {
                    errorMsg = `Error ${error.response.status}: ${error.response.statusText}`;
                }
            } else {
                errorMsg = "No se pudo conectar con el servidor.";
            }
            identTableBody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">${errorMsg}</td></tr>`;
        });
}

// Función para resetear filtros
window.resetFilters = function() {
    if (identFilterForm) identFilterForm.reset();
    const docOrPlateInput  = document.getElementById('filter-doc-or-plate');
    const nameOrBrandInput = document.getElementById('filter-name-or-brand');
    if (docOrPlateInput)  docOrPlateInput.value = '';
    if (nameOrBrandInput) nameOrBrandInput.value = '';
    fetchIdentifications(1);
}
