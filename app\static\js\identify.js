// static/js/identify.js
// Funciones para MANEJAR VISUALMENTE el estado de identificación en un marcador Leaflet

/**
 * Inicia el estado visual de "identificación en curso".
 * - Cambia el icono a 11.png.
 * - Muestra popup "en curso".
 * - Guarda el icono original en el marcador.
 * - Marca el estado 'isIdentifying' en el marcador.
 * @param {L.<PERSON>} marker - El marcador del agente.
 * @param {string} folio - El folio del agente.
 * @param {string} identificationType - 'person' o 'vehicle'.
 */
window.startIdentificationVisual = function(marker, folio, identificationType = 'person') {
    if (!marker) return;
    console.log(`[IDENTIFY.JS] Starting visual state for ${folio}`);

    // Guardar icono original (si no está ya guardado o identificando)
    if (!marker.isIdentifying && !marker.originalIconUrl) {
        try {
            marker.originalIconUrl = marker.options.icon.options.iconUrl;
        } catch (e) {
            console.warn(`[IDENTIFY.JS] Could not get original icon URL for ${folio}.`);
            marker.originalIconUrl = null; // Marcar para recalcular
        }
    } else if (marker.isIdentifying) {
         console.log(`[IDENTIFY.JS] Marker ${folio} already in identifying state.`);
         // No cambiar icono original si ya estaba identificando
    }

    marker.isIdentifying = true; // Marcar estado

    // Cambiar a ícono de identificación
    marker.setIcon(L.icon({
        iconUrl: '/static/img/11.png',
        iconSize: [32, 32], iconAnchor: [16, 32], popupAnchor: [0, -30]
    }));

    // Mostrar popup "en curso"
    const typeText = identificationType === 'person' ? 'Persona' : 'Vehículo';
    const popupContent = `<b>${folio}</b><br>Identificación ${typeText} en curso...`;
    marker.bindPopup(popupContent).openPopup();
};

/**
 * Actualiza el popup con los datos de la identificación completada.
 * @param {L.Marker} marker - El marcador del agente.
 * @param {string} folio - El folio del agente.
 * @param {object} identificationData - Datos básicos ({dni, last_name, first_names} o {plate, brand, model}).
 * @param {string} identificationType - 'person' o 'vehicle'.
 */
window.updateIdentificationVisual = function(marker, folio, identificationData, identificationType = 'person') {
    if (!marker || !marker.isIdentifying) {
        console.warn(`[IDENTIFY.JS] Cannot update visual for ${folio}: Marker not found or not identifying.`);
        return;
    }
     console.log(`[IDENTIFY.JS] Updating visual state for ${folio} with data:`, identificationData);

    // Construir contenido del popup actualizado
    let details = 'Datos recibidos.';
    if (identificationType === 'person' && identificationData) {
        details = `<b>Persona:</b><br>
                   ${identificationData.last_name || ''}, ${identificationData.first_names || ''}<br>
                   DNI: ${identificationData.dni || 'S/DNI'}`;
    } else if (identificationType === 'vehicle' && identificationData) {
         details = `<b>Vehículo:</b><br>
                   Placa: ${identificationData.plate || 'S/DOM'}<br>
                   ${identificationData.brand || ''} ${identificationData.model || ''}`;
    }
    const popupContent = `<b>${folio}</b><br>${details}`;
    marker.setPopupContent(popupContent);
    if (!marker.isPopupOpen()) marker.openPopup(); // Reabrir si se cerró
};

/**
 * Restaura el estado visual normal del marcador después de la identificación.
 * - Restaura el icono original.
 * - Restaura el popup original.
 * - Limpia el estado 'isIdentifying'.
 * @param {L.Marker} marker - El marcador del agente.
 * @param {object} agentData - Datos del agente (para obtener icono y popup originales).
 */
window.revertIdentificationVisual = function(marker, agentData) {
    if (!marker) return;
    const folio = agentData?.folio || marker.folio || 'Unknown'; // Intenta obtener folio
    console.log(`[IDENTIFY.JS] Reverting visual state for ${folio}.`);

    marker.isIdentifying = false; // Cambiar estado

    if (marker.isPopupOpen()) marker.closePopup();

    // Restaurar icono original
    let originalIconUrl = null;
    if (marker.originalIconUrl) {
        originalIconUrl = marker.originalIconUrl;
        delete marker.originalIconUrl; // Limpiar el guardado
    } else if (agentData) {
        // Si no se guardó, recalcular basado en datos actuales (getAgentIcon DEBE estar definido globalmente o en live_map.js)
        try {
             originalIconUrl = window.getAgentIcon(agentData); // Asume que getAgentIcon está disponible
        } catch(e) {
             console.error("[IDENTIFY.JS] window.getAgentIcon not found!", e);
             originalIconUrl = '/static/img/policie.png'; // Fallback
        }
        console.log(`[IDENTIFY.JS] Original icon recalculated for ${folio}: ${originalIconUrl}`);
    } else {
        originalIconUrl = '/static/img/policie.png'; // Fallback
        console.warn(`[IDENTIFY.JS] No original icon or agent data for ${folio}. Using default.`);
    }
    marker.setIcon(L.icon({
        iconUrl: originalIconUrl,
        iconSize: [32, 32], iconAnchor: [16, 32], popupAnchor: [0, -30]
    }));

    // Restaurar popup normal (basado en los datos más recientes del agente)
    if (agentData) {
        const normalPopupHtml = `
            <b>Oficial:</b> ${agentData.folio} (${agentData.name || 'N/A'})<br>
            <b>Movilidad:</b> ${agentData.mobility || 'N/A'}<br>
            <b>Últ. Act:</b> ${agentData.last_update ? formatDateTime(agentData.last_update) : 'N/A'}<br>
            <b>Precisión:</b> ${agentData.accuracy ? agentData.accuracy.toFixed(1) + 'm' : 'N/A'}
        `;
        marker.bindPopup(normalPopupHtml); // Volver a bindear
    } else {
        marker.unbindPopup(); // Quitar popup si no hay datos
    }
};

// La función original showIdentificationFlow ya no es necesaria para el flujo WS
// window.showIdentificationFlow = function(...) { ... } // COMENTAR O ELIMINAR