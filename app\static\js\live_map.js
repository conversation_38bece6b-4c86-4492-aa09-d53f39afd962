// static/js/live_map.js
// Versión que utiliza identify.js para la lógica visual

let map = null;                // 🌍 Mapa Leaflet global
let agentMarkers = {};         // folio → L.Marker
let agentDataStore = {};       // folio → { last_update, mobility, name, latitude, longitude, accuracy, folio }
let updateIntervalId = null;   // ID del polling REST
let identificationTimers = {}; // folio → timeoutID (para revertir visualización)
let operatorSocket = null;     // WebSocket del operador
let selectedFolios = new Set();  // Set para la selección múltiple


/**
 * Habilita/deshabilita y actualiza el texto del botón batch.
 */
function updateBatchButton() {
  const batchBtn = document.getElementById('batchMsgBtn');
  if (!batchBtn) return;
  batchBtn.disabled = selectedFolios.size === 0;
  batchBtn.textContent = selectedFolios.size
    ? `Mensaje a ${selectedFolios.size}`
    : `Mensaje a seleccionados`;
}

/**
 * Obtiene la URL del icono del agente según su estado y movilidad.
 * Necesita estar disponible globalmente si identify.js la llama.
 * @param {object | string} agentOrFolio - El objeto agente o su folio.
 * @returns {string} URL del icono.
 */
window.getAgentIcon = function(agentOrFolio) { // Hacerla global
    let agentFolio = typeof agentOrFolio === 'string' ? agentOrFolio : agentOrFolio?.folio;
    let agentData = typeof agentOrFolio === 'string' ? agentDataStore[agentOrFolio] : agentOrFolio;
    const marker = agentMarkers[agentFolio];

    // Prioridad 1: Si el marcador indica que está identificando visualmente
    if (marker && marker.isIdentifying) {
        return '/static/img/11.png'; // Icono de identificación en curso
    }

    // Prioridad 2: Si no hay datos o está inactivo
    if (!agentData) {
        // console.warn(`[getAgentIcon] No data found for ${agentFolio}, returning default.`);
        return '/static/img/police_stop.png'; // Default si no hay datos
    }
    const inactive = agentData.last_update
      ? ((Date.now() - new Date(agentData.last_update)) / 60000) > 5 // 5 minutos inactivo
      : true;
    if (inactive) {
        return '/static/img/police_stop.png'; // Icono inactivo
    }

    // Prioridad 3: Icono según movilidad
    switch (agentData.mobility) {
      case 'moto':      return '/static/img/moto.png';
      case 'movil':     return '/static/img/movil.png';
      case 'Bicicleta': return '/static/img/bici.png'; // Asegúrate que exista bici.png
      case 'Peatonal':
      default:          return '/static/img/policie.png'; // Peatonal o default
    }
}

/**
 * Actualiza los marcadores en el mapa basándose en la lista de agentes.
 * Preserva el estado visual 'isIdentifying' si un agente está en medio de una ID.
 * @param {Array<object>} agents - Lista de objetos de agente desde la API.
 */
function updateAgentMarkers(agents) {
  const incomingFolios = agents.map(a => a.folio);
  const existingFolios = Object.keys(agentMarkers);

  // 1. Eliminar marcadores obsoletos
  existingFolios.forEach(folio => {
    if (!incomingFolios.includes(folio)) {
      console.log(`[MAP] Removing marker for disconnected/inactive agent: ${folio}`);
      if (agentMarkers[folio]) map.removeLayer(agentMarkers[folio]);
      delete agentMarkers[folio];
      delete agentDataStore[folio];
      if (identificationTimers[folio]) {
        clearTimeout(identificationTimers[folio]);
        delete identificationTimers[folio];
      }
    }
  });

  // 2. Actualizar o crear marcadores
  agents.forEach(agent => {
    if (agent.latitude == null || agent.longitude == null) return;

    agentDataStore[agent.folio] = agent;
    const pos = [agent.latitude, agent.longitude];
    const marker = agentMarkers[agent.folio];

    const normalIconUrl = window.getAgentIcon(agent);
    const normalIcon = L.icon({
      iconUrl: normalIconUrl,
      iconSize: [32, 32], iconAnchor: [16, 32], popupAnchor: [0, -30]
    });
    const normalPopupHtml = `
      <b>Oficial:</b> ${agent.folio} (${agent.name || 'N/A'})<br>
      <b>Movilidad:</b> ${agent.mobility || 'N/A'}<br>
      <b>Últ. Act:</b> ${agent.last_update ? formatDateTime(agent.last_update) : 'N/A'}<br>
      <b>Precisión:</b> ${agent.accuracy ? agent.accuracy.toFixed(1) + 'm' : 'N/A'}<br>
      <button class="btn btn-sm btn-outline-primary send-message-btn mt-1" data-folio="${agent.folio}">
        <i class="bi bi-chat-dots"></i> Enviar mensaje
      </button>
    `;

    if (marker) { // Marcador existente
      marker.setLatLng(pos);
      if (!marker.isIdentifying) {
        const currentIconUrl = marker.options.icon?.options?.iconUrl;
        if (currentIconUrl !== normalIconUrl) marker.setIcon(normalIcon);
        marker.setPopupContent(normalPopupHtml);
      }
    } else { // Nuevo marcador
      console.log(`[MAP] Creating new marker for agent: ${agent.folio}`);
      // Creamos un nuevo marker
      const newMarker = L.marker(pos, { icon: normalIcon })
        .addTo(map)
        .bindPopup(normalPopupHtml);

      // ── NUEVO: Ctrl+click para seleccionar/deseleccionar este agente ──
      newMarker.on('click', e => {
        if (!e.originalEvent.ctrlKey) return;
        const f = agent.folio;
        if (selectedFolios.has(f)) {
          selectedFolios.delete(f);
          newMarker.setOpacity(1);
        } else {
          selectedFolios.add(f);
          newMarker.setOpacity(0.6);
        }
       // Llama a la función global
        updateBatchButton();
        e.originalEvent.preventDefault();
      });

      agentMarkers[agent.folio] = newMarker;
    }
  });
}

// ─── NUEVO BLOQUE PARA DIBUJAR RUTAS ──────────────────────────────────────────

// Guarda las polilíneas actuales en el mapa
let agentPolylines = {};

/**
 * Dibuja (o actualiza) en el mapa las rutas de los últimos 30 min para cada agente.
 * @param {Array} tracks Lista desde /api/dashboard/active-tracks
 */
function updateAgentTracks(tracks) {
  // Elimina las polilíneas anteriores
  Object.values(agentPolylines).forEach(poly => map.removeLayer(poly));
  agentPolylines = {};

  tracks.forEach(agent => {
    if (!agent.track || agent.track.length < 2) return;

    // Construye array de [lat, lon]
    const latlngs = agent.track.map(p => [p.latitude, p.longitude]);

    // Dibuja la polilínea (default azul)
    const line = L.polyline(latlngs, {
      weight: 4,
      opacity: 0.6
    }).addTo(map);

    agentPolylines[agent.folio] = line;
  });
}

/**
 * Llama a la API para obtener los tracks y los dibuja.
 */
function fetchLiveTracks() {
  axios.get('/api/dashboard/active-tracks')
    .then(resp => updateAgentTracks(resp.data || []))
    .catch(err => console.error('[POLL] Error fetching tracks:', err));
}

// ─── FIN NUEVO BLOQUE ────────────────────────────────────────────────────────

/**
 * Conecta el WebSocket del operador al servidor.
 */
function connectOperatorWebSocket() {
    const token = localStorage.getItem('accessToken'); // Asume que el token del operador está aquí
    if (!token) {
        console.error("[WS_OPERATOR] No token found for operator.");
        // Manejar error: mostrar mensaje, redirigir a login, etc.
        alert("Error de autenticación. Por favor, inicie sesión.");
        logout(); // Asume que logout() existe en dashboard_common.js
        return;
    }

    if (operatorSocket && operatorSocket.connected) {
        console.log("[WS_OPERATOR] WebSocket already connected.");
        return;
    }

    console.log("[WS_OPERATOR] Attempting to connect WebSocket...");
    // Asegúrate que la URL y el namespace sean correctos
    const wsUrl = 'wss://patagoniaservers.com.ar:5005';
    operatorSocket = io(wsUrl + '/live', { // Incluye el namespace
        secure: true,
        transports: ['websocket'],
        auth: { token: token } // Método preferido para pasar el token
    });

    // --- Manejadores de eventos WebSocket ---
    operatorSocket.on('connect', () => {
        console.log('[WS_OPERATOR] Successfully connected to WebSocket /live!');
    });

    operatorSocket.on('connect_error', (err) => {
        console.error('[WS_OPERATOR] WebSocket Connection Error:', err);
        // Podrías intentar reconectar o mostrar un estado de error en la UI
    });

    operatorSocket.on('auth_error', (err) => {
        console.error('[WS_OPERATOR] WebSocket Authentication Error:', err);
        alert(`Error de autenticación en tiempo real: ${err.msg || 'Error desconocido'}. La sesión se cerrará.`);
        logout(); // Forzar logout si falla la autenticación WS
    });

    operatorSocket.on('disconnect', (reason) => {
         console.warn(`[WS_OPERATOR] Disconnected from WebSocket: ${reason}`);
         operatorSocket = null; // Limpiar la instancia
         // Podrías implementar lógica de reconexión aquí si es deseado
         // setTimeout(connectOperatorWebSocket, 5000); // Ejemplo: intentar reconectar cada 5s
    });

    // ─── Listener para INICIO de identificación ─────────────────────────────────
operatorSocket.on('agent_identification_started', (data) => {
  console.log("[WS_OPERATOR] Event 'agent_identification_started' received:", data);
  const folio = data.folio;
  const marker = agentMarkers[folio];
  if (!marker) return console.warn(`Marker for ${folio} not found.`);

  // Limpio cualquier timer pendiente
  if (identificationTimers[folio]) {
    clearTimeout(identificationTimers[folio]);
    delete identificationTimers[folio];
  }

  // Marco el estado y delego a identify.js
  marker.isIdentifying = true;
  window.startIdentificationVisual(marker, folio, data.type);
});


// ─── Listener para ACTUALIZACIÓN/FIN de identificación ──────────────────────
operatorSocket.on('agent_identification_updated', (data) => {
  console.log("[WS_OPERATOR] Event 'agent_identification_updated' received:", data);
  const folio = data.folio;
  const marker = agentMarkers[folio];
  const agentData = agentDataStore[folio];
  if (!marker) return console.warn(`Marker for ${folio} not found.`);

  // Marcar como “identifying” y limpiar timer previo
  marker.isIdentifying = true;
  if (identificationTimers[folio]) clearTimeout(identificationTimers[folio]);

  // 1) Poner el icono “11.png” y abrir popup con los datos
  marker.setIcon(L.icon({
    iconUrl: '/static/img/11.png', iconSize:[32,32], iconAnchor:[16,32], popupAnchor:[0,-30]
  }));
  const info = data.identification_data;
  const html = data.type === 'person'
    ? `<b>${info.last_name}, ${info.first_names}</b><br>DNI: ${info.dni}`
    : `<b>Patente:</b> ${info.plate}<br>${info.brand} ${info.model}`;
  marker.setPopupContent(html);
  marker.openPopup();

  // 2) Comprobar si el DNI/patente figura en novedades y, de ser así, mostrar el popup grande
  const idText = data.type === 'person' ? info.dni : info.plate;
  axios.get(`${document.body.dataset.apiBaseUrl||'/api'}/novelties/all?per_page=100`)
    .then(resp => {
      const novs = resp.data.novelties || resp.data || [];
      if (novs.some(n => n.content.includes(idText))) {
        const { lat, lng } = marker.getLatLng();
        window.showOperatorNoveltyPopup({
          folio,
          lat,
          lng,
          timestamp: new Date()
        });
      }
    })
    .catch(err => console.error('Error comprobando novedades:', err));

  // 3) Tras 5s revertir al estado normal
  identificationTimers[folio] = setTimeout(() => {
    console.log(`[WS_OPERATOR] Reverting visual state for ${folio}`);

    marker.isIdentifying = false;

    // Restaurar icono según movilidad / último estado
    const normalIconUrl = window.getAgentIcon(agentData);
    marker.setIcon(L.icon({
      iconUrl: normalIconUrl, iconSize:[32,32], iconAnchor:[16,32], popupAnchor:[0,-30]
    }));

    // Restaurar popup “normal”
    const normalPopup = `
      <b>Oficial:</b> ${agentData.folio} (${agentData.name||'N/A'})<br>
      <b>Movilidad:</b> ${agentData.mobility||'N/A'}<br>
      <b>Últ. Act:</b> ${agentData.last_update ? formatDateTime(agentData.last_update) : 'N/A'}<br>
      <b>Precisión:</b> ${agentData.accuracy ? agentData.accuracy.toFixed(1)+'m' : 'N/A'}
    `;
    marker.setPopupContent(normalPopup);
    marker.closePopup();

    delete identificationTimers[folio];
  }, 5000);
});

} // Fin de connectOperatorWebSocket

/**
 * Función principal de inicialización del mapa en vivo.
 */
function initLiveMap() {
  // 1. Verificar autenticación y rol
  if (!checkAuthAndRole()) {
    console.error("[INIT] Authentication check failed. Aborting map initialization.");
    return;
  }
  console.log("[INIT] Authentication OK.");

  // 2. Inicializar Mapa Leaflet
  const mapEl = document.getElementById('map');
  if (!mapEl) {
    console.error("[INIT] Map HTML element '#map' not found!");
    return;
  }
  mapEl.innerHTML = '';
  map = L.map('map').setView([-40.8119, -62.9966], 14);
  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    maxZoom: 19,
    attribution: '© OpenStreetMap contributors'
  }).addTo(map);
  console.log("[INIT] Leaflet map initialized.");
  
  // ≫ Batch Message Button
  // const batchBtn = document.getElementById('batchMsgBtn');
  //function updateBatchButton() {
    const batchBtn = document.getElementById('batchMsgBtn');
  //  if (!batchBtn) return;
  //  batchBtn.disabled = selectedFolios.size === 0;
  //  batchBtn.textContent = selectedFolios.size
  //    ? `Mensaje a ${selectedFolios.size}`
  //    : `Mensaje a seleccionados`;
  //}
  updateBatchButton();

   //  3 Hook para “Enviar Mensaje” 
  // Inicializamos el Modal de Bootstrap
  const operatorMessageModalEl = document.getElementById('operatorMessageModal');
  const operatorMessageModal   = new bootstrap.Modal(operatorMessageModalEl);
  const msgFolioInput          = document.getElementById('operator-message-folio');
  const msgTextInput           = document.getElementById('operator-message-text');
  const msgFeedback            = document.getElementById('operator-message-feedback');
  const msgSendBtn             = document.getElementById('operator-message-send-btn');

  // Cada vez que abrimos un popup de agente, enganchamos el botón
  map.on('popupopen', e => {
    const btn = e.popup._contentNode.querySelector('.send-message-btn');
    if (!btn) return;
    btn.addEventListener('click', () => {
      // Preparamos y abrimos el modal
      msgFolioInput.value = btn.dataset.folio;
      msgTextInput.value  = '';
      msgFeedback.classList.add('d-none');
      operatorMessageModal.show();
    });
  });

  // Al hacer click en “Enviar” dentro del modal
  msgSendBtn.addEventListener('click', () => {
    const folio = msgFolioInput.value;
    const text  = msgTextInput.value.trim();
    if (!text) {
      msgFeedback.textContent = 'El mensaje no puede estar vacío.';
      msgFeedback.className   = 'alert alert-danger';
      msgFeedback.classList.remove('d-none');
      return;
    }
    axios.post('/api/dashboard/operator-message', { folio, message: text })
      .then(() => {
        msgFeedback.textContent = 'Mensaje enviado correctamente.';
        msgFeedback.className   = 'alert alert-success';
        msgFeedback.classList.remove('d-none');
        setTimeout(() => operatorMessageModal.hide(), 1500);
      })
      .catch(err => {
        console.error('Error enviando mensaje al agente:', err);
        msgFeedback.textContent = 'Error al enviar el mensaje.';
        msgFeedback.className   = 'alert alert-danger';
        msgFeedback.classList.remove('d-none');
      });
  });

  // Botón para enviar mensaje a varios agentes seleccionados
  batchBtn.addEventListener('click', () => {
    const fols = Array.from(selectedFolios);
    if (!fols.length) return;

    // Preparamos el modal para batch (sin folio individual)
    msgFolioInput.value = '';
    msgFolioInput.placeholder = fols.join(', ');
    msgTextInput.value = '';
    msgFeedback.classList.add('d-none');
    operatorMessageModal.show();

    // Reemplazamos la acción de envío para este batch
    msgSendBtn.onclick = () => {
      const text = msgTextInput.value.trim();
      if (!text) {
        msgFeedback.textContent = 'El mensaje no puede estar vacío.';
        msgFeedback.className = 'alert alert-danger';
        msgFeedback.classList.remove('d-none');
        return;
      }
      axios.post('/api/dashboard/operator-message', {
        folios: fols,
        message: text
      })
      .then(() => {
        operatorMessageModal.hide();
        // Restaurar opacidad y limpiar selección
        fols.forEach(f => agentMarkers[f].setOpacity(1));
        selectedFolios.clear();
        updateBatchButton();
        alert(`Mensaje enviado a ${fols.length} agente(s).`);
      })
      .catch(err => {
        console.error('Error enviando mensaje batch:', err);
        msgFeedback.textContent = 'Error al enviar el mensaje.';
        msgFeedback.className = 'alert alert-danger';
        msgFeedback.classList.remove('d-none');
      });
    };
  });
  
  // 4. Añadir Controles Adicionales (Botón Ubicación, Radio Búsqueda)
  const locateBtn = document.createElement('button');
  locateBtn.innerHTML = '<i class="bi bi-crosshair2"></i>';
  locateBtn.title = "Centrar en tu ubicación";
  locateBtn.className = 'btn btn-primary position-fixed rounded-circle shadow';
  Object.assign(locateBtn.style, { bottom:'90px', right:'30px', width:'50px', height:'50px', zIndex:1000 });
  locateBtn.onclick = centerMapOnUserLocation;
  document.body.appendChild(locateBtn);

  const radiusDiv = document.createElement('div');
  radiusDiv.innerHTML = `
    <label for="searchRadius" class="form-label mb-1">Radio (m):</label>
    <input type="number" id="searchRadius" class="form-control form-control-sm"
           value="500" min="50" max="2000" step="50">
  `;
  radiusDiv.className = 'position-fixed bg-white p-2 border shadow rounded';
  Object.assign(radiusDiv.style, { bottom:'160px', right:'30px', width:'120px', zIndex:1000 });
  document.body.appendChild(radiusDiv);
  console.log("[INIT] UI Controls added (Locate Button, Radius Input).");

  // 5. Conectar WebSocket para identificaciones en tiempo real
  connectOperatorWebSocket();

  // 6. Funciones de polling REST
  function fetchLiveAgents() {
    console.log("[POLL] Fetching live agents via REST...");
    axios.get('/api/dashboard/live-agents?limit=200')
      .then(resp => {
        console.log(`[POLL] Received ${resp.data?.length || 0} agents.`);
        updateAgentMarkers(resp.data || []);
      })
      .catch(err => console.error('[POLL] Error fetching live agents:', err.response || err));
  }

  function fetchLiveTracks() {
    console.log("[POLL] Fetching live tracks via REST...");
    axios.get('/api/dashboard/active-tracks')
      .then(resp => {
        updateAgentTracks(resp.data || []);
      })
      .catch(err => console.error('[POLL] Error fetching tracks:', err.response || err));
  }

  // 7. Llamadas iniciales
  fetchLiveAgents();
  fetchLiveTracks();

  // 8. Intervalo cada 15 s para refrescar agentes y tracks
  if (updateIntervalId) clearInterval(updateIntervalId);
  updateIntervalId = setInterval(() => {
    fetchLiveAgents();
    fetchLiveTracks();
  }, 15000);
  console.log(`[INIT] Polling started (Interval ID: ${updateIntervalId}).`);

  // 9. Listener de Clic para buscar agentes cercanos
  let tempPointMarker = null;
    // Ctrl+click para marcar/desmarcar agentes
    map.on('click', e => {
      if (!e.originalEvent.ctrlKey) return;
      for (const folio in agentMarkers) {
        const m = agentMarkers[folio];
        if (m.getLatLng().equals(e.latlng)) {
          if (selectedFolios.has(folio)) {
            selectedFolios.delete(folio);
            m.setOpacity(1);
          } else {
            selectedFolios.add(folio);
            m.setOpacity(0.6);
          }
          updateBatchButton();
          break;
        }
      }
    });
  
    // Click normal para buscar agentes cercanos (sin Ctrl)
    map.on('click', e => {
      if (e.originalEvent.ctrlKey) return;
      const { lat, lng } = e.latlng;
    const radius = parseInt(document.getElementById('searchRadius').value, 10) || 500;

    if (tempPointMarker) map.removeLayer(tempPointMarker);
    tempPointMarker = L.marker([lat, lng], {
      icon: L.icon({
        iconUrl: '/static/img/point.png',
        iconSize: [25,25], iconAnchor:[12,25], popupAnchor:[0,-20]
      })
    })
    .addTo(map)
    .bindPopup(`Buscando agentes a ${radius}m...`)
    .openPopup();

    axios.post('/api/dashboard/nearest-agents', { latitude: lat, longitude: lng, max_distance: radius })
      .then(res => {
        const nearby = res.data.agents || [];
        let html = nearby.length
          ? `<h6>Agentes Cercanos (${radius}m):</h6><ul class="list-unstyled small">` +
            nearby.map(a => `<li><b>${a.folio}</b> - ${a.distance.toFixed(0)} m</li>`).join('') +
            `</ul>`
          : 'No se encontraron agentes cercanos.';
        tempPointMarker.setPopupContent(html).openPopup();
      })
      .catch(err => {
        console.error("[MAP CLICK] Error fetching nearest agents:", err.response || err);
        tempPointMarker.setPopupContent('Error al buscar agentes cercanos.').openPopup();
      });
  });

  // 10. Limpieza al salir de la página
  window.addEventListener('beforeunload', () => {
    if (updateIntervalId) clearInterval(updateIntervalId);
    if (operatorSocket && operatorSocket.connected) operatorSocket.disconnect();
    Object.values(agentPolylines).forEach(poly => map.removeLayer(poly));
    Object.keys(identificationTimers).forEach(f => clearTimeout(identificationTimers[f]));
    if (locateBtn.parentNode) locateBtn.parentNode.removeChild(locateBtn);
    if (radiusDiv.parentNode) radiusDiv.parentNode.removeChild(radiusDiv);
    console.log("[CLEANUP] Resources cleared.");
  });

  console.log("[INIT] Live Map initialization complete.");
}

 // Fin de initLiveMap

/**
 * Centra el mapa en la ubicación actual del usuario (navegador).
 */
function centerMapOnUserLocation() {
    if (!navigator.geolocation) {
      alert("Geolocalización no soportada por este navegador.");
      return;
    }
    console.log("[GEOLOC] Requesting user location...");
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const userLatLng = [position.coords.latitude, position.coords.longitude];
        console.log("[GEOLOC] Location found:", userLatLng);
        map.setView(userLatLng, 16); // Centrar y hacer zoom
        // Añadir marcador temporal para la ubicación del usuario
        L.marker(userLatLng, {
          title: "Tu ubicación",
          icon: L.icon({
            iconUrl: '/static/img/user_location.png', // Asegúrate que exista user_location.png
            iconSize:[25, 25], iconAnchor:[12, 25], popupAnchor:[0, -20]
          })
        }).addTo(map).bindPopup("Tu ubicación aproximada").openPopup();
      },
      (error) => {
        console.error("[GEOLOC] Error getting location:", error);
        let errorMsg = "No se pudo obtener tu ubicación: ";
        switch(error.code) {
            case error.PERMISSION_DENIED: errorMsg += "Permiso denegado."; break;
            case error.POSITION_UNAVAILABLE: errorMsg += "Información no disponible."; break;
            case error.TIMEOUT: errorMsg += "Timeout."; break;
            default: errorMsg += "Error desconocido.";
        }
        alert(errorMsg);
      },
      { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 } // Opciones para obtener ubicación precisa
    );
}

// NOTA: La llamada a initLiveMap() debe estar en el HTML (<script> al final del body)
// document.addEventListener('DOMContentLoaded', initLiveMap);