// app/static/js/login.js

function initLoginPage() {
    const loginForm = document.getElementById('login-form');
    const errorDiv = document.getElementById('login-error');
    // Obtener URL de destino desde un atributo data en el body o usar fallback
    const dashboardUrl = document.body.dataset.dashboardUrl || '/dashboard/';

    if (!loginForm) {
        console.error("Login form not found!");
        return;
    }

    // Verificar si ya hay un token al cargar la página de login
    // Si es válido, redirigir directamente al dashboard
    const existingToken = localStorage.getItem('accessToken');
    const existingRole = localStorage.getItem('userRole');
    const allowedRoles = ['supervisor', 'comando']; // Roles que pueden usar el dashboard
    if (existingToken && existingRole && allowedRoles.includes(existingRole)) {
        console.log("Usuario ya logueado, redirigiendo al dashboard...");
        // Opcional: Podrías hacer una llamada a /api/auth/me para validar el token antes de redirigir
        window.location.href = dashboardUrl;
        return; // Evitar añadir el listener del formulario si ya está logueado
    } else {
        // Limpiar datos si no son válidos o no existen
        localStorage.removeItem('accessToken');
        localStorage.removeItem('userRole');
        localStorage.removeItem('userFolio');
    }


    loginForm.addEventListener('submit', function(event) {
        event.preventDefault();
        errorDiv.classList.add('d-none');

        const folio = document.getElementById('folio').value;
        const password = document.getElementById('password').value;

        // Deshabilitar botón mientras se procesa (opcional)
        const submitButton = loginForm.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.textContent;
        submitButton.disabled = true;
        submitButton.textContent = 'Ingresando...';


        axios.post('/api/auth/login', { folio: folio, password: password })
            .then(function (response) {
                console.log('Login successful:', response.data);
                if (response.data.access_token && response.data.user) {
                    // Verificar si el rol del usuario logueado le permite acceder al dashboard
                    if (allowedRoles.includes(response.data.user.role)) {
                        localStorage.setItem('accessToken', response.data.access_token);
                        localStorage.setItem('userRole', response.data.user.role);
                        localStorage.setItem('userFolio', response.data.user.folio || '');
                        window.location.href = dashboardUrl; // Redirigir al dashboard
                    } else {
                        // Rol no autorizado para este dashboard
                        showLoginError(`Acceso denegado: Rol '${response.data.user.role}' no autorizado para este panel.`);
                        // No limpiamos el token aquí necesariamente, pero no redirigimos
                        submitButton.disabled = false; // Reactivar botón
                        submitButton.textContent = originalButtonText;
                    }
                } else {
                    showLoginError('Respuesta inesperada del servidor.');
                     submitButton.disabled = false; // Reactivar botón
                     submitButton.textContent = originalButtonText;
                }
            })
            .catch(function (error) {
                console.error('Login error:', error.response || error);
                let errorMsg = 'Error de conexión o del servidor.';
                if (error.response && error.response.data && error.response.data.msg) {
                    errorMsg = error.response.data.msg;
                } else if (error.response && error.response.status === 401) {
                    errorMsg = 'Folio o contraseña incorrectos.';
                }
                showLoginError(errorMsg);
                 submitButton.disabled = false; // Reactivar botón
                 submitButton.textContent = originalButtonText;
            });
    });

    function showLoginError(message) {
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.classList.remove('d-none');
        } else {
            alert(message); // Fallback
        }
    }
}