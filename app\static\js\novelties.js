// app/static/js/novelties.js

let currentSearchTerm = "";
let noveltyModalInstance = null;
let noveltyTableBody = null;
let noveltyForm = null;
let noveltyFormError = null;
let noveltyModalLabel = null;
let noveltyIdInput, noveltyContentInput, noveltyImageInput;
let currentPageNovelties = 1;
const noveltiesPerPage = 15;

/** Inicializa la vista de novedades */
function initNoveltiesPage() {
    if (!checkAuthAndRole()) return;

    noveltyTableBody = document.getElementById('novelties-table-body');
    noveltyForm = document.getElementById('novelty-form');
    noveltyFormError = document.getElementById('novelty-form-error');
    noveltyModalLabel = document.getElementById('noveltyModalLabel');
    noveltyIdInput = document.getElementById('novelty-id');
    noveltyContentInput = document.getElementById('novelty-content');
    noveltyImageInput = document.getElementById('novelty-image-url');

    const modalElement = document.getElementById('noveltyModal');
    if (modalElement && !noveltyModalInstance) {
        noveltyModalInstance = new bootstrap.Modal(modalElement);
    }
    const searchForm = document.getElementById('novelty-search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const searchInput = document.getElementById('novelty-search-input');
            currentSearchTerm = searchInput?.value?.trim() || "";
            fetchNovelties(1);
        });
    }
    fetchNovelties();
}

/** Carga todas las novedades */
function fetchNovelties(page = 1) {
    currentPageNovelties = page;
    noveltyTableBody.innerHTML = '<tr><td colspan="6" class="text-center">Cargando novedades...</td></tr>';
    const params = new URLSearchParams({
        page,
        per_page: noveltiesPerPage
    });
    if (currentSearchTerm) params.append('search', currentSearchTerm);

    axios.get(`/api/novelties/all?${params.toString()}`)
    .then(response => {
        const data = response.data;
        if (!data || !Array.isArray(data.novelties)) {
            throw new Error("Respuesta inesperada del servidor: faltan novedades");
        }

        const novelties = data.novelties;
        const total = data.total || 0;
        const pages = data.pages || 1;

        renderNovelties(novelties);
        renderNoveltiesPagination(total, pages);
    })
    .catch(error => {
        console.error('Error al cargar novedades:', error);
        noveltyTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error al cargar novedades.</td></tr>';
    });
}

function renderNovelties(novelties) {
    noveltyTableBody.innerHTML = '';
    if (!novelties.length) {
        noveltyTableBody.innerHTML = '<tr><td colspan="6" class="text-center">No hay novedades para mostrar.</td></tr>';
        return;
    }

    novelties.forEach(n => {
        const row = `
        <tr>
            <td>${n.id}</td>
            <td>${n.content}</td>
            <td>${n.image_url ? `<img src="${n.image_url}" width="100">` : '-'}</td>
            <td>${n.author ? `${n.author.name} (${n.author.folio})` : '-'}</td>
            <td>${formatDateTime(n.created_at)}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick='openEditNovelty(${JSON.stringify(n).replace(/'/g, "\'")})'>Editar</button>
                <button class="btn btn-sm btn-outline-danger" onclick='deleteNovelty(${n.id})'>Eliminar</button>
            </td>
        </tr>`;
        noveltyTableBody.innerHTML += row;
    });
}

/** Abre el modal para crear una novedad */
function openCreateModal() {
    if (!noveltyForm || !noveltyModalInstance) return;
    noveltyForm.reset();
    noveltyIdInput.value = '';
    noveltyModalLabel.textContent = 'Crear Novedad';
    noveltyFormError.classList.add('d-none');
    noveltyModalInstance.show();
}

/** Abre el modal para editar una novedad */
function openEditNovelty(novelty) {
    noveltyFormError.classList.add('d-none');
    noveltyIdInput.value = novelty.id;
    noveltyContentInput.value = novelty.content;
    noveltyImageInput.value = novelty.image_url || '';
    noveltyModalLabel.textContent = 'Editar Novedad';
    noveltyModalInstance.show();
}

/** Envía el formulario de creación o edición */
function handleNoveltySubmit() {
    const content = noveltyContentInput.value.trim();
    const image_url = noveltyImageInput.value.trim();
    const noveltyId = noveltyIdInput.value;
    const isEditing = !!noveltyId;

    if (!content) {
        showFormError('El contenido es obligatorio.');
        return;
    }

    const payload = { content, image_url };
    const url = isEditing ? `/api/novelties/${noveltyId}` : '/api/novelties';
    const method = isEditing ? 'put' : 'post';

    axios({ method, url, data: payload })
        .then(() => {
            noveltyModalInstance.hide();
            fetchNovelties();
            alert(`Novedad ${isEditing ? 'actualizada' : 'creada'} exitosamente.`);
        })
        .catch(err => {
            console.error('Error al guardar novedad:', err);
            showFormError('Error al guardar la novedad.');
        });
}

function showFormError(msg) {
    noveltyFormError.textContent = msg;
    noveltyFormError.classList.remove('d-none');
}

/** Elimina una novedad */
function deleteNovelty(id) {
    if (!confirm('¿Estás seguro que deseas eliminar esta novedad?')) return;
    axios.delete(`/api/novelties/${id}`)
        .then(() => {
            fetchNovelties();
            alert('Novedad eliminada correctamente.');
        })
        .catch(err => {
            console.error('Error al eliminar novedad:', err);
            alert('No se pudo eliminar la novedad.');
        });
}

// Mostrar novedades no leídas al iniciar sesión
function checkUnreadNoveltiesOnLogin() {
    axios.get('/api/novelties/unread')
        .then(response => {
            const unread = response.data;
            if (unread.length) showUnreadPopup(unread);
        })
        .catch(error => console.warn('No se pudieron obtener las novedades no leídas:', error));
}

function showUnreadPopup(novelties) {
    const list = novelties.map(n => `<li><strong>${formatDateTime(n.created_at)}</strong>: ${n.content}</li>`).join('');
    const modal = document.createElement('div');
    modal.classList.add('modal', 'fade');
    modal.innerHTML = `
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Novedades recientes</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <ul>${list}</ul>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" id="markAsReadBtn">Marcar como leídas</button>
        </div>
      </div>
    </div>`;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    document.getElementById('markAsReadBtn').onclick = () => {
        Promise.all(novelties.map(n => axios.post(`/api/novelties/${n.id}/read`)))
            .then(() => bsModal.hide())
            .catch(err => alert('Error al marcar como leídas'));
    };
}

function renderNoveltiesPagination(totalItems, totalPages) {
    const paginationContainer = document.getElementById('pagination-controls-novelties');
    if (!paginationContainer) return;

    paginationContainer.innerHTML = '';
    if (totalPages <= 1) return;

    const createPageItem = (page, label, isActive = false) => {
        return `
            <li class="page-item ${isActive ? 'active' : ''}">
                <a class="page-link" href="#" onclick="fetchNovelties(${page}); return false;">${label}</a>
            </li>`;
    };

    paginationContainer.innerHTML += createPageItem(currentPageNovelties - 1, '«');
    for (let i = 1; i <= totalPages; i++) {
        paginationContainer.innerHTML += createPageItem(i, i, i === currentPageNovelties);
    }
    paginationContainer.innerHTML += createPageItem(currentPageNovelties + 1, '»');
}

function resetNoveltySearch() {
    document.getElementById('novelty-search-input').value = '';
    currentSearchTerm = "";
    fetchNovelties(1);
}