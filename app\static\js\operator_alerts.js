// static/js/operator_alerts.js
;(function(){
  /**
   * Muestra en pantalla un panel fijo con info de novedad
   * @param {{ folio: string, lat: number, lng: number, timestamp: Date }} opts
   */
  function showOperatorNoveltyPopup({ folio, lat, lng, timestamp }) {
    // Si ya existe, lo removemos
    const existing = document.getElementById('operator-novelty-popup');
    if (existing) existing.remove();

    // Contenedor principal
    const container = document.createElement('div');
    container.id = 'operator-novelty-popup';
    Object.assign(container.style, {
      position: 'fixed',
      top: '20px',
      left: '50%',
      transform: 'translateX(-50%)',
      background: 'rgba(231,76,60,0.95)',
      color: '#fff',
      padding: '1rem 1.5rem',
      'border-radius': '8px',
      'box-shadow': '0 4px 12px rgba(0,0,0,0.4)',
      'z-index': 10000,
      'min-width': '280px',
      'max-width': '90%',
      'font-family': 'Arial, sans-serif',
    });

    container.innerHTML = `
      <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:0.5rem;">
        <strong style="font-size:1.1em;">¡Novedad en identificación!</strong>
        <button id="operator-novelty-close" style="
          background:transparent;border:none;color:#fff;font-size:1.2em;
          cursor:pointer;line-height:1;">
          &times;
        </button>
      </div>
      <div style="font-size:0.95em;line-height:1.3;">
        <p><strong>Agente:</strong> ${folio}</p>
        <p><strong>Fecha/Hora:</strong> ${timestamp.toLocaleString()}</p>
        <p>
          <strong>Ubicación:</strong>
          <a href="#" id="operator-novelty-link" style="color:#f1c40f;
             text-decoration:underline;cursor:pointer;">
            [ver en mapa]
          </a>
        </p>
      </div>
    `;

    document.body.appendChild(container);

    // Cerrar al hacer click en la X
    container
      .querySelector('#operator-novelty-close')
      .addEventListener('click', () => container.remove());

    // Al hacer click en “ver en mapa” centramos y abrimos popup de Leaflet
    container
      .querySelector('#operator-novelty-link')
      .addEventListener('click', e => {
        e.preventDefault();
        if (window.map && window.agentMarkers && window.agentMarkers[folio]) {
          const m = window.agentMarkers[folio];
          window.map.setView(m.getLatLng(), 16);
          m.openPopup();
        }
        container.remove();
      });
  }

  // Hacemos la función accesible desde cualquier script
  window.showOperatorNoveltyPopup = showOperatorNoveltyPopup;
})();
