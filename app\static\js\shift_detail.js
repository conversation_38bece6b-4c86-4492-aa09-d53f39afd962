// app/static/js/shift_detail.js

let detailMap = null; // Instancia del mapa de detalle
let polylineLayer = null; // Capa para la polilínea del recorrido
let startMarkerLayer = null; // Marcador de inicio
let endMarkerLayer = null; // Marcador de fin

/**
 * Inicializa la página de detalle de turno.
 * @param {number} shiftId - El ID del turno a mostrar.
 */
function initShiftDetail(shiftId) {
    if (!checkAuthAndRole()) return;

    const distanceSpan = document.getElementById('stat-distance');
    const personIdsSpan = document.getElementById('stat-person-ids');
    const vehicleIdsSpan = document.getElementById('stat-vehicle-ids');
    const mapElement = document.getElementById('detail-map');

    if (!mapElement) {
        console.error("Elemento del mapa de detalle no encontrado.");
        return;
    }
    mapElement.innerHTML = '';

    detailMap = L.map(mapElement).setView([-34.60, -58.38], 13);
    <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        attribution: '© OpenStreetMap'
    }).addTo(detailMap);

    updateNavbarUser();

    fetchShiftStats(shiftId, distanceSpan, personIdsSpan, vehicleIdsSpan);
    fetchShiftTrack(shiftId);
}

/** Obtiene y muestra las estadísticas del turno */
function fetchShiftStats(shiftId, distElem, personElem, vehicleElem) {
    if (!distElem || !personElem || !vehicleElem) return;

    axios.get(`/api/dashboard/shifts/${shiftId}/stats`)
        .then(response => {
            const stats = response.data;
            distElem.textContent = `${stats.total_distance_km.toFixed(2)} km (${stats.calculated_on === 'request_time' ? 'calc. ahora' : 'guardado'})`;
            personElem.textContent = stats.person_identifications;
            vehicleElem.textContent = stats.vehicle_identifications;
        })
        .catch(error => {
            console.error("Error fetching shift stats:", error.response || error);
            distElem.textContent = 'Error';
            personElem.textContent = 'Error';
            vehicleElem.textContent = 'Error';
        });
}

/** Obtiene y dibuja el recorrido del turno en el mapa */
function fetchShiftTrack(shiftId) {
    const mapElement = document.getElementById('detail-map');
    if (!detailMap || !mapElement) return;

    axios.get(`/api/dashboard/shifts/${shiftId}/trackpoints`)
        .then(response => {
            const trackPoints = response.data;
            console.log(`Track points recibidos para shift ${shiftId}:`, trackPoints.length);

            if (polylineLayer) detailMap.removeLayer(polylineLayer);
            if (startMarkerLayer) detailMap.removeLayer(startMarkerLayer);
            if (endMarkerLayer) detailMap.removeLayer(endMarkerLayer);

            if (trackPoints && trackPoints.length > 0) {
                const latLngs = trackPoints.map(p => [p.latitude, p.longitude]);

                polylineLayer = L.polyline(latLngs, { color: 'blue', weight: 3, opacity: 0.7 }).addTo(detailMap);

                startMarkerLayer = L.marker(latLngs[0], {
                    title: 'Inicio Turno',
                    icon: createColoredMarker('green')
                }).addTo(detailMap)
                  .bindPopup(`<b>Inicio:</b><br>${formatDateTime(trackPoints[0].timestamp)}`);

                if (latLngs.length > 1) {
                    const lastPointIndex = latLngs.length - 1;
                    endMarkerLayer = L.marker(latLngs[lastPointIndex], {
                        title: 'Fin Turno / Últ. Posición',
                        icon: createColoredMarker('red')
                    }).addTo(detailMap)
                      .bindPopup(`<b>Fin/Últ. Pos:</b><br>${formatDateTime(trackPoints[lastPointIndex].timestamp)}`);
                }

                detailMap.fitBounds(polylineLayer.getBounds().pad(0.1));

                // Cálculo de distancia si se requiere
                const distanceElem = document.getElementById('stat-distance');
                if (distanceElem && distanceElem.textContent.includes('0.00')) {
                    const totalDistance = calculateDistanceFromPoints(trackPoints);
                    distanceElem.textContent = `${totalDistance.toFixed(2)} km (calculado)`;
                }
            } else {
                mapElement.innerHTML = '<p class="text-center text-muted p-5">No hay datos de recorrido GPS para este turno.</p>';
            }
        })
        .catch(error => {
            console.error('Error fetching shift track:', error.response || error);
            mapElement.innerHTML = '<p class="text-center text-danger p-5">Error al cargar el recorrido.</p>';
        });
}

/** Calcula la distancia total del recorrido GPS usando Haversine */
function calculateDistanceFromPoints(points) {
    function haversine(lat1, lon1, lat2, lon2) {
        const R = 6371; // km
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLon = (lon2 - lon1) * Math.PI / 180;
        const a = Math.sin(dLat / 2) ** 2 +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLon / 2) ** 2;
        return 2 * R * Math.asin(Math.sqrt(a));
    }

    let total = 0;
    for (let i = 1; i < points.length; i++) {
        const prev = points[i - 1];
        const curr = points[i];
        total += haversine(prev.latitude, prev.longitude, curr.latitude, curr.longitude);
    }
    return total;
}

/** Helper para crear marcadores de colores */
function createColoredMarker(color = 'blue') {
    let iconUrl = 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png';
    if (color === 'green') iconUrl = 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png';
    if (color === 'red') iconUrl = 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png';
    const shadowUrl = 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png';

    return L.icon({
        iconUrl: iconUrl,
        shadowUrl: shadowUrl,
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
        shadowSize: [41, 41]
    });
}
