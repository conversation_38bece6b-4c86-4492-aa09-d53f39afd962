// static/js/shifts.js

let currentPageShifts = 1;
const shiftsPerPage = 15;

function initShiftsTable() {
    if (!checkAuthAndRole()) return;

    // --- Obtener elementos del DOM ---
    const shiftsTableBody     = document.getElementById('shifts-table-body');
    const paginationControls  = document.getElementById('pagination-controls');
    const filterForm          = document.getElementById('filter-form');
    const filterFolio         = document.getElementById('filter-folio');
    const filterDateFrom      = document.getElementById('filter-date-from');
    const filterDateTo        = document.getElementById('filter-date-to');
    const filterStatus        = document.getElementById('filter-status');

    if (!shiftsTableBody || !paginationControls || !filterForm) {
        console.error("Faltan elementos DOM para tabla de turnos o filtros");
        return;
    }

    updateNavbarUser();

    // Al enviar el formulario de filtros, arrancamos en la página 1
    filterForm.addEventListener('submit', e => {
        e.preventDefault();
        fetchShifts(1);
    });

    // Función que carga y dibuja los turnos
    window.fetchShifts = function(page = 1) {
        currentPageShifts = page;
        shiftsTableBody.innerHTML = '<tr><td colspan="10" class="text-center">Cargando turnos...</td></tr>';

        // --- Construir parámetros ---
        const params = new URLSearchParams({
            page: page,
            per_page: shiftsPerPage
        });
        if (filterFolio.value)    params.append('folio', filterFolio.value);
        if (filterDateFrom.value) params.append('date_from', filterDateFrom.value);
        if (filterDateTo.value)   params.append('date_to', filterDateTo.value);
        if (filterStatus.value)   params.append('status', filterStatus.value);

        axios.get(`/api/dashboard/shifts-history?${params}`)
            .then(resp => {
                const { shifts, total, pages } = resp.data;
                renderShifts(shifts);
                renderPagination(total, pages);
            })
            .catch(err => {
                console.error('Error fetching shifts:', err.response || err);
                shiftsTableBody.innerHTML = '<tr><td colspan="10" class="text-center text-danger">Error al cargar turnos.</td></tr>';
            });
    };

    // Dibuja las filas de la tabla
    function renderShifts(shifts) {
        shiftsTableBody.innerHTML = '';
        if (!shifts.length) {
            shiftsTableBody.innerHTML = '<tr><td colspan="10" class="text-center">No se encontraron turnos.</td></tr>';
            return;
        }
        shifts.forEach(shift => {
            const officerInfo = shift.officer
                ? `${shift.officer.folio}${shift.officer.name ? ` (${shift.officer.name})` : ''}`
                : 'N/A';
            const started  = formatDateTime(shift.start_time);
            const ended    = shift.end_time ? formatDateTime(shift.end_time) : '-';
            const mobility = shift.mobility_type || 'N/A';
            const distance = shift.total_distance_km != null
                ? `${shift.total_distance_km.toFixed(2)} km`
                : '-';
            const persCnt = shift.person_id_count  != null ? shift.person_id_count  : '-';
            const vehCnt  = shift.vehicle_id_count != null ? shift.vehicle_id_count : '-';
            const isActive = !shift.end_time;
            const statusBadge = isActive
                ? '<span class="badge bg-success">Activo</span>'
                : '<span class="badge bg-secondary">Finalizado</span>';

            const actions = `
                <a href="/dashboard/turnos/${shift.id}"
                   class="btn btn-sm btn-outline-primary me-1"
                   title="Ver Detalles">
                    <i class="bi bi-eye"></i>
                </a>
                ${isActive ? `
                <button class="btn btn-sm btn-outline-danger"
                        title="Forzar Finalización"
                        onclick="handleForceEndShift(${shift.id})">
                    <i class="bi bi-stop-circle-fill"></i>
                </button>` : ''}
            `;

            shiftsTableBody.innerHTML += `
                <tr>
                    <td>${shift.id}</td>
                    <td>${officerInfo}</td>
                    <td>${started}</td>
                    <td>${ended}</td>
                    <td>${mobility}</td>
                    <td>${distance}</td>
                    <td>${persCnt}</td>
                    <td>${vehCnt}</td>
                    <td>${statusBadge}</td>
                    <td>${actions}</td>
                </tr>
            `;
        });
    }

    // Dibuja la paginación
    function renderPagination(total, totalPages) {
        paginationControls.innerHTML = '';
        if (totalPages <= 1) return;

        const makeItem = (p, label, disabled=false, active=false) => {
            const cls = `page-item${disabled ? ' disabled' : ''}${active ? ' active' : ''}`;
            const fn  = (!disabled && !active) ? `fetchShifts(${p})` : 'event.preventDefault();';
            return `<li class="${cls}"><a class="page-link" href="#" onclick="${fn}">${label}</a></li>`;
        };

        paginationControls.innerHTML += makeItem(currentPageShifts-1, '«', currentPageShifts===1);

        // Mostrar un bloque de 5 páginas
        let start = Math.max(1, currentPageShifts - 2),
            end   = Math.min(totalPages, start + 4);
        if (end - start < 4) start = Math.max(1, end - 4);

        if (start > 1) {
            paginationControls.innerHTML += makeItem(1, 1);
            if (start > 2) paginationControls.innerHTML += '<li class="page-item disabled"><span class="page-link">…</span></li>';
        }
        for (let p = start; p <= end; p++) {
            paginationControls.innerHTML += makeItem(p, p, false, p===currentPageShifts);
        }
        if (end < totalPages) {
            if (end < totalPages - 1) paginationControls.innerHTML += '<li class="page-item disabled"><span class="page-link">…</span></li>';
            paginationControls.innerHTML += makeItem(totalPages, totalPages);
        }

        paginationControls.innerHTML += makeItem(currentPageShifts+1, '»', currentPageShifts===totalPages);
    }

    // Fuerza el fin de un turno
    window.handleForceEndShift = function(shiftId) {
        if (!confirm(`¿Forzar fin del turno ID ${shiftId}?`)) return;
        axios.put(`/api/dashboard/shifts/${shiftId}/force-end`)
            .then(() => {
                alert(`Turno ${shiftId} finalizado.`);
                fetchShifts(currentPageShifts);
            })
            .catch(err => {
                console.error(`Error forzando fin turno ${shiftId}:`, err.response||err);
                const msg = err.response?.data?.msg || 'Error al forzar finalización.';
                alert(msg);
            });
    };

    // Resetea filtros y recarga
    window.resetFilters = function() {
        filterForm.reset();
        fetchShifts(1);
    };

    // Carga inicial
    fetchShifts(1);
}
