// static/js/unread_novelties.js

document.addEventListener('DOMContentLoaded', () => {
    const token = getAuthToken();
    if (!token) return;

    axios.get('/api/novelties/unread', {
        headers: {
            Authorization: `Bearer ${token}`
        }
    })
    .then(response => {
        const novedades = response.data;
        if (!novedades || novedades.length === 0) return;

        const listContainer = document.getElementById('unread-novelties-list');
        listContainer.innerHTML = '';

        novedades.forEach(n => {
            const card = document.createElement('div');
            card.className = 'card mb-3 shadow-sm';

            const autor = n.author ? `${n.author.name || 'Sin nombre'} (${n.author.folio})` : 'Autor desconocido';

            card.innerHTML = `
                <div class="card-body">
                    <p class="card-text">${n.content}</p>
                    ${n.image_url ? `<img src="${n.image_url}" class="img-fluid rounded mt-2" alt="Imagen de novedad">` : ''}
                    <small class="text-muted d-block">Creado: ${formatDateTime(n.created_at)}</small>
                    <small class="text-muted"><i class="bi bi-person-circle me-1"></i>Autor: ${autor}</small>
                </div>
            `;
            listContainer.appendChild(card);
        });

        const modal = new bootstrap.Modal(document.getElementById('unreadNoveltiesModal'));
        modal.show();
    })
    .catch(error => {
        console.error("Error al cargar novedades no leídas:", error);
    });
});

window.markAllNoveltiesAsRead = async function () {
    const token = getAuthToken();
    if (!token) return;

    try {
        const res = await axios.get('/api/novelties/unread', {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });

        const novedades = res.data;
        await Promise.all(
            novedades.map(nov => axios.post(`/api/novelties/${nov.id}/read`, {}, {
                headers: { Authorization: `Bearer ${token}` }
            }))
        );

        const modalElement = document.getElementById('unreadNoveltiesModal');
        const modalInstance = bootstrap.Modal.getInstance(modalElement);
        if (modalInstance) modalInstance.hide();
    } catch (err) {
        console.error("Error al marcar novedades como leídas:", err);
    }
};
