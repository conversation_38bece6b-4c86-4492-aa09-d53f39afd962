// app/static/js/users.js

let currentPageUsers = 1;
const usersPerPage = 15;
let userModalInstance = null;
let userTableBody = null;
let paginationControls = null;
let userForm = null;
let userFormError = null;
let userModalLabel = null;
let userIdInput, userFolioInput, userNameInput, userPasswordInput, userPasswordConfirmInput, userRoleSelect, userUnitInput;

function initUsersPage() {
    if (!checkAuthAndRole()) return;

    userTableBody = document.getElementById('users-table-body');
    paginationControls = document.getElementById('pagination-controls-users');
    const userModalElement = document.getElementById('userModal');
    userForm = document.getElementById('user-form');
    userFormError = document.getElementById('user-form-error');
    userModalLabel = document.getElementById('userModalLabel');
    userIdInput = document.getElementById('user-id');
    userFolioInput = document.getElementById('user-folio');
    userNameInput = document.getElementById('user-name');
    userPasswordInput = document.getElementById('user-password');
    userPasswordConfirmInput = document.getElementById('user-password-confirm');
    userRoleSelect = document.getElementById('user-role');
    userUnitInput = document.getElementById('user-unit');

    if (!userTableBody || !paginationControls || !userModalElement || !userForm) {
        console.error("Faltan elementos esenciales del DOM en la página de usuarios.");
        return;
    }

    if (!userModalInstance) {
        userModalInstance = new bootstrap.Modal(userModalElement);
    }

    updateNavbarUser();
    fetchUsers(1);
}

function renderUsers(users) {
    if (!userTableBody) return;
    userTableBody.innerHTML = '';
    if (!users || users.length === 0) {
        userTableBody.innerHTML = '<tr><td colspan="8" class="text-center">No hay usuarios para mostrar.</td></tr>';
        return;
    }
    users.forEach(user => {
        const roleBadgeClass = getRoleBadgeClass(user.role);
        const activeBadgeClass = user.is_active ? 'success' : 'secondary';
        const row = `
            <tr>
                <td>${user.id}</td>
                <td>${user.folio}</td>
                <td>${user.name || 'N/A'}</td>
                <td><span class="badge bg-${roleBadgeClass}">${user.role}</span></td>
                <td>${user.unit_id || '-'}</td>
                <td><span class="badge bg-${activeBadgeClass}">${user.is_active ? 'Sí' : 'No'}</span></td>
                <td>${formatDateTime(user.created_at)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-secondary me-1" title="Editar"
                        onclick='openEditUserModal(${JSON.stringify(user).replace(/'/g, "\\'")})'>
                        <i class="bi bi-pencil-square"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-${user.is_active ? 'danger' : 'success'} me-1" title="Activar/Desactivar"
                        onclick="handleToggleUserStatus(${user.id})">
                        <i class="bi ${user.is_active ? 'bi-x-circle' : 'bi-check-circle'}"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" title="Eliminar"
                        onclick="handleDeleteUser(${user.id})">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        userTableBody.innerHTML += row;
    });
}

function getRoleBadgeClass(role) {
    switch (role) {
        case 'supervisor': return 'warning text-dark';
        case 'comando': return 'danger';
        case 'agente':
        default: return 'primary';
    }
}

function renderUserPagination(totalItems, totalPages) {
    if (!paginationControls) return;
    paginationControls.innerHTML = '';
    if (!totalPages || totalPages <= 1) return;

    const createPageItem = (page, label, isDisabled = false, isActive = false) => {
        const liClass = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
        const onclick = (!isDisabled && !isActive) ? `fetchUsers(${page})` : 'event.preventDefault();';
        return `<li class="${liClass}"><a class="page-link" href="#" onclick="${onclick}">${label}</a></li>`;
    };

    paginationControls.innerHTML += createPageItem(currentPageUsers - 1, '«', currentPageUsers === 1);
    for (let i = 1; i <= totalPages; i++) {
        paginationControls.innerHTML += createPageItem(i, i, false, i === currentPageUsers);
    }
    paginationControls.innerHTML += createPageItem(currentPageUsers + 1, '»', currentPageUsers === totalPages);
}

window.fetchUsers = function(page = 1) {
    currentPageUsers = page;
    if (!userTableBody) return;
    userTableBody.innerHTML = '<tr><td colspan="8" class="text-center">Cargando usuarios...</td></tr>';
    const params = new URLSearchParams({ page: currentPageUsers, per_page: usersPerPage });

    axios.get(`/api/dashboard/users?${params.toString()}`)
        .then(response => {
            renderUsers(response.data.users || []);
            renderUserPagination(response.data.total, response.data.pages);
        })
        .catch(error => {
            console.error("Error fetching users:", error.response || error);
            let errorMsg = "Error al cargar usuarios.";
            if (error.response && error.response.data && error.response.data.msg) {
                errorMsg = error.response.data.msg;
                if (error.response.data.msg === "Subject must be a string") {
                    errorMsg += " (Error interno de autenticación)";
                }
            } else if (error.response && error.response.status) {
                errorMsg += ` (Código: ${error.response.status})`;
            }
            userTableBody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">${errorMsg}</td></tr>`;
        });
}

window.openCreateUserModal = function() {
    if (!userForm || !userIdInput || !userModalLabel || !userModalInstance) return;
    userForm.reset();
    userIdInput.value = '';
    userModalLabel.textContent = 'Crear Nuevo Usuario';
    userPasswordInput.disabled = false;
    userPasswordConfirmInput.disabled = false;
    userPasswordInput.required = true;
    userPasswordConfirmInput.required = true;
    userFormError.classList.add('d-none');
    userModalInstance.show();
}

window.openEditUserModal = function(user) {
    if (!userForm || !userIdInput || !userModalLabel || !userModalInstance) return;

    userForm.reset();
    userFormError.classList.add('d-none');

    userIdInput.value = user.id;
    userFolioInput.value = user.folio;
    userNameInput.value = user.name || '';
    userRoleSelect.value = user.role;
    userUnitInput.value = user.unit_id || '';

    userPasswordInput.value = '';
    userPasswordConfirmInput.value = '';
    userPasswordInput.disabled = true;
    userPasswordConfirmInput.disabled = true;
    userPasswordInput.required = false;
    userPasswordConfirmInput.required = false;

    userModalLabel.textContent = 'Editar Usuario';
    userModalInstance.show();
}

window.handleUserFormSubmit = function() {
    if (!userForm || !userFormError) return;
    userFormError.classList.add('d-none');

    const folio = userFolioInput.value.trim();
    const name = userNameInput.value.trim();
    const password = userPasswordInput.value;
    const passwordConfirm = userPasswordConfirmInput.value;
    const role = userRoleSelect.value;
    const unit = userUnitInput.value.trim();
    const userId = userIdInput.value;
    const isEditing = !!userId;

    if (!folio || !name || !role || (!isEditing && (!password || !passwordConfirm))) {
        showUserFormError("Completa todos los campos obligatorios (*)."); return;
    }
    if (!isEditing && password !== passwordConfirm) {
        showUserFormError("Las contraseñas no coinciden."); return;
    }
    if (!isEditing && password.length < 6) {
        showUserFormError("La contraseña debe tener al menos 6 caracteres."); return;
    }

    const userData = { folio, name, role, unit_id: unit || null };
    if (!isEditing) userData.password = password;

    const url = isEditing ? `/api/dashboard/users/${userId}` : '/api/dashboard/users';
    const method = isEditing ? 'put' : 'post';

    const submitButton = document.querySelector('#userModal .modal-footer button.btn-primary');
    const originalButtonText = submitButton.textContent;
    submitButton.disabled = true;
    submitButton.textContent = 'Guardando...';

    axios({ method, url, data: userData })
        .then(response => {
            userModalInstance.hide();
            fetchUsers(currentPageUsers);
            alert(`Usuario ${isEditing ? 'actualizado' : 'creado'} exitosamente!`);
        })
        .catch(error => {
            console.error('Error saving user:', error.response || error);
            let errorMsg = `Error al ${isEditing ? 'actualizar' : 'crear'} el usuario.`;
            if (error.response && error.response.data && error.response.data.msg) {
                errorMsg = error.response.data.msg;
                if (error.response.data.msg === "Subject must be a string") {
                    errorMsg += " (Error interno de autenticación)";
                }
            } else if (error.response && error.response.status) {
                errorMsg += ` (Código: ${error.response.status})`;
            }
            showUserFormError(errorMsg);
        })
        .finally(() => {
            submitButton.disabled = false;
            submitButton.textContent = originalButtonText;
        });
}

function showUserFormError(message) {
    if (userFormError) {
        userFormError.textContent = message;
        userFormError.classList.remove('d-none');
    } else {
        console.error("User Form Error Div not found! Message:", message);
        alert("Error en el formulario: " + message);
    }
}

window.handleToggleUserStatus = function(userId) {
    if (!confirm("¿Estás seguro de que deseas cambiar el estado de este usuario?")) return;

    axios.put(`/api/dashboard/users/${userId}/toggle-status`)
        .then(response => {
            alert(response.data.msg || "Estado del usuario actualizado.");
            fetchUsers(currentPageUsers);
        })
        .catch(error => {
            console.error('Error al cambiar estado:', error.response || error);
            let errorMsg = "Error al cambiar estado del usuario.";
            if (error.response && error.response.data && error.response.data.msg) {
                errorMsg = error.response.data.msg;
            }
            alert(errorMsg);
        });
}

window.handleDeleteUser = function(userId) {
    if (!confirm("¿Realmente deseas eliminar este usuario de forma permanente?")) return;

    axios.delete(`/api/dashboard/users/${userId}`)
        .then(response => {
            alert(response.data.msg || "Usuario eliminado correctamente.");
            fetchUsers(currentPageUsers);
        })
        .catch(error => {
            console.error('Error al eliminar usuario:', error.response || error);
            let errorMsg = "Error al eliminar usuario.";
            if (error.response && error.response.data && error.response.data.msg) {
                errorMsg = error.response.data.msg;
            }
            alert(errorMsg);
        });
}

