<!-- templates/auth/login.html.html -->

{% extends 'base.html' %}

{% block title %}Iniciar <PERSON><PERSON> - OPERA{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <h2 class="mb-4 text-center">Iniciar Sesión - OPERA</h2>

        {# Mensaje de error (se mostrará con JavaScript) #}
        <div id="login-error" class="alert alert-danger d-none" role="alert"></div>

        {# Formulario HTML - La lógica de envío y manejo de JWT será con JavaScript #}
        <form id="login-form">
            <div class="mb-3">
                <label for="folio" class="form-label">Folio Personal</label>
                <input type="text" class="form-control" id="folio" name="folio" required>
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">Contraseña</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">Ingresar</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
{# Axios ya está en base.html #}
<script src="{{ url_for('static', filename='js/login.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', () => {
        initLoginPage();
    });
</script>
{% endblock %}