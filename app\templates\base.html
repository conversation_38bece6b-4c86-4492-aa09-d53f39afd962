<!-- templates/base.html -->
<!doctype html>
<html lang="es">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}OPERA Seguridad{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- Custom styles -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    {% block styles %}{% endblock %}

    <style>
        body { padding-top: 56px; }
        #map, #detail-map { height: 500px; width: 100%; }
        .leaflet-container { z-index: 0; }
    </style>
</head>
{# Añadir data-attributes para URLs generadas por Flask #}
<body data-login-url="{{ url_for('dashboard.login_view') }}" data-dashboard-url="{{ url_for('dashboard.index') }}">
    {% include 'includes/_navbar.html' %}

    <main class="container mt-4">
        {% include 'includes/_messages.html' %}
        {% block content %}{% endblock %}
    </main>

    <footer class="container mt-5 py-3 text-center text-muted border-top">
        OPERA Seguridad Pública © {{ now().year }}
    </footer>

    <!-- Bootstrap JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- Qr y Barras -->
    <script src="https://unpkg.com/html5-qrcode"></script>
    <!-- Day.js (ANTES de common.js y unread_novelties.js) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dayjs/1.11.10/dayjs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dayjs/1.11.10/locale/es.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dayjs/1.11.10/plugin/localizedFormat.min.js"></script>
    <script>
        // Configurar Day.js globalmente
        if(typeof dayjs !== 'undefined') {
            dayjs.locale('es');
            dayjs.extend(window.dayjs_plugin_localizedFormat);
        } else {
            console.error("Day.js no se cargó correctamente.");
        }
    </script>
    
    <link rel="icon" href="{{ url_for('static', filename='img/favicon.ico') }}" type="image/x-icon">
    <link rel="shortcut icon" href="{{ url_for('static', filename='img/favicon.ico') }}" type="image/x-icon">

    <!-- Scripts Comunes del Dashboard (ANTES de específicos y unread) -->
    <script src="{{ url_for('static', filename='js/dashboard_common.js') }}"></script>

    <!-- Script del pop-up de novedades (DESPUÉS de common.js) -->
    <script src="{{ url_for('static', filename='js/unread_novelties.js') }}"></script>

    <!-- Bloque para scripts ESPECÍFICOS de página (se cargan DESPUÉS de los comunes) -->
    {% block scripts %}{% endblock %}

    <!-- Modal emergente de novedades no leídas (HTML) -->
    <div class="modal fade" id="unreadNoveltiesModal" tabindex="-1" aria-labelledby="unreadNoveltiesLabel" aria-hidden="true">
         <div class="modal-dialog modal-dialog-scrollable modal-lg">
            <div class="modal-content border-primary shadow">
                <div class="modal-header bg-primary text-white"> <h5 class="modal-title" id="unreadNoveltiesLabel"><i class="bi bi-megaphone-fill me-2"></i>Novedades Recientes</h5> <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button> </div>
                <div class="modal-body" id="unread-novelties-list"><p class="text-muted text-center">Cargando novedades...</p></div>
                <div class="modal-footer"> <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cerrar</button> <button type="button" class="btn btn-success" onclick="markAllNoveltiesAsRead()">Marcar como leídas y continuar</button> </div>
            </div>
         </div>
    </div>

</body>
</html>