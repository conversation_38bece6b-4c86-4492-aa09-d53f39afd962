<!-- templates/dashboard/historico_map.html -->

{% extends 'base.html' %}
{% block title %}Mapa Histórico - OPERA{% endblock %}

{% block styles %}
  {{ super() }}
  <style>
    #map { height: 60vh; min-height:500px; }
  </style>
{% endblock %}

{% block content %}
  <h1 class="mb-4">Mapa Histórico</h1>

  <form id="historical-form" class="row g-3 mb-4 align-items-end">
    <div class="col-md-3">
      <label for="date-from" class="form-label">Desde</label>
      <input type="datetime-local" id="date-from" class="form-control" required>
    </div>
    <div class="col-md-3">
      <label for="date-to" class="form-label">Hasta</label>
      <input type="datetime-local" id="date-to" class="form-control" required>
    </div>
    <div class="col-md-3">
      <label for="type-filter" class="form-label">Tipo ID</label>
      <select id="type-filter" class="form-select">
        <option value="all">Todos</option>
        <option value="person">Personas</option>
        <option value="vehicle">Vehículos</option>
      </select>
    </div>
    <div class="col-md-3">
      <label class="form-label">Agentes</label>
      <div class="mb-2">
        <label>
          <input type="checkbox" id="check-all-agents"> Seleccionar todos
        </label>
      </div>
      <div id="agents-container" class="d-flex flex-wrap gap-2"></div>
    </div>

    <div class="col-12 text-end">
      <button type="button" id="btn-load-agents" class="btn btn-secondary">
        Cargar Agentes
      </button>
      <button type="button" id="btn-show-map" class="btn btn-primary">
        Ver en Mapa
      </button>
    </div>
  </form>

  <div id="map">Cargando mapa...</div>
{% endblock %}

{% block scripts %}
  {{ super() }}
  <script src="{{ url_for('static', filename='js/historico_map.js') }}"></script>
{% endblock %}
