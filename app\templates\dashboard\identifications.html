<!-- templates/dashboard/identifications.html -->

{% extends 'base.html' %}

{% block title %}Identificaciones - OPERA{% endblock %}

{% block content %}
    <h1 class="mb-4">Identificaciones Realizadas</h1>

    {# Controles de Filtro (Implementar con JS y parámetros en fetchIdentifications) #}
    <form id="filter-form" class="row g-3 mb-4 align-items-end bg-light p-3 rounded">
        <div class="col-md-3">
          <label for="filter-type" class="form-label">Tipo</label>
          <select id="filter-type" class="form-select">
            <option value="">Todos</option>
            <option value="person">Persona</option>
            <option value="vehicle">Vehículo</option>
          </select>
        </div>
        <div class="col-md-3">
          <label for="filter-officer" class="form-label">Oficial (Folio)</label>
          <input type="text" id="filter-officer" name="officer_folio" class="form-control" placeholder="Folio">
        </div>
        <div class="col-md-3">
          <label for="filter-doc-or-plate" class="form-label">DNI / Patente</label>
          <input type="text" id="filter-doc-or-plate" name="doc_or_plate" class="form-control" placeholder="DNI o Patente">
        </div>
        <div class="col-md-3">
          <label for="filter-name-or-brand" class="form-label">Nombre / Marca</label>
          <input type="text" id="filter-name-or-brand" name="name_or_brand" class="form-control" placeholder="Nombre completo o marca del vehículo">
        </div>
        <div class="col-md-3">
          <label for="filter-date-from" class="form-label">Desde</label>
          <input type="date" id="filter-date-from" name="date_from" class="form-control">
        </div>
        <div class="col-md-3">
          <label for="filter-date-to" class="form-label">Hasta</label>
          <input type="date" id="filter-date-to" name="date_to" class="form-control">
        </div>
        <div class="col-12">
          <button type="submit" class="btn btn-primary">Filtrar</button>
          <button type="reset" class="btn btn-secondary" onclick="resetFilters()">Limpiar</button>
        </div>
      </form>
  
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Tipo</th>
                    <th>Fecha/Hora</th>
                    <th>Oficial (Folio)</th>
                    <th>DNI / Patente</th>
                    <th>Nombre / Marca</th>
                    <th>Ubicación</th> {# Icono/Link para indicar si tiene GPS #}
                    <th>Turno ID</th>
                    {#<th>Acciones</th>#} {# Podrías añadir un botón de detalle #}
                </tr>
            </thead>
            <tbody id="identifications-table-body">
                {# Los datos se cargarán aquí con JavaScript #}
                <tr><td colspan="8" class="text-center">Cargando identificaciones...</td></tr>
            </tbody>
        </table>
    </div>

    {# Paginación (controlada por JS) #}
    <nav aria-label="Page navigation">
      <ul class="pagination justify-content-center" id="pagination-controls">
        </ul>
    </nav>

{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/identifications.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', () => {
        initIdentificationsTable(); // Asume que esta es tu función init
    });
</script>
{% endblock %}