<!-- templates/dashboard/identifications_details.html -->
{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <h3>Detalle de Identificación</h3>
    <hr>
    {% if identification.__class__.__name__ == 'IdentifiedPerson' %}
    <h5>Tipo: Persona</h5>
    <ul class="list-group">
        <li class="list-group-item"><strong>Nombre:</strong> {{ identification.last_name }}, {{ identification.first_names }}</li>
        <li class="list-group-item"><strong>DNI:</strong> {{ identification.dni_number }}</li>
        <li class="list-group-item"><strong>CUIL / ID:</strong> {{ identification.cuil_or_id }}</li>
        <li class="list-group-item"><strong>Fe<PERSON> de Nacimiento:</strong> {{ identification.dob }}</li>
        <li class="list-group-item"><strong>Edad:</strong> {{ age }} años</li>
        <li class="list-group-item"><strong><PERSON><PERSON>:</strong> {{ identification.issue_date }}</li>
        <li class="list-group-item"><strong>Género:</strong> {{ identification.gender }}</li>
        <li class="list-group-item"><strong>Identificado hace menos de 24 hs:</strong> {{ identified_recently }}</li>
        <li class="list-group-item"><strong>Turno:</strong> {{ identification.shift_id }}</li>
        <li class="list-group-item">
            <strong>Oficial que identificó:</strong>
            {% if identification.officer %}
                <a href="{{ url_for('dashboard.users_view') }}?search={{ identification.officer.folio }}">
                    {{ identification.officer.folio }}
                </a>
            {% else %}
                No disponible
            {% endif %}
        </li>        <li class="list-group-item"><strong>Ubicación:</strong>
            {% if identification.location_lat and identification.location_lon %}
                <span class="text-success">Disponible</span>
            {% else %}No disponible{% endif %}
        </li>
    </ul>
    {% elif identification.__class__.__name__ == 'IdentifiedVehicle' %}
    <h5>Tipo: Vehículo</h5>
    <ul class="list-group">
        <li class="list-group-item"><strong>Dominio:</strong> {{ identification.plate }}</li>
        <li class="list-group-item"><strong>Tipo de vehículo:</strong> {{ identification.vehicle_type }}</li>
        <li class="list-group-item"><strong>Marca:</strong> {{ identification.brand }}</li>
        <li class="list-group-item"><strong>Modelo:</strong> {{ identification.model }}</li>
        <li class="list-group-item"><strong>Color:</strong> {{ identification.color }}</li>
        <li class="list-group-item"><strong>Identificado hace menos de 24 hs:</strong> {{ identified_recently }}</li>
        <li class="list-group-item"><strong>Turno:</strong> {{ identification.shift_id }}</li>
        <li class="list-group-item">
            <strong>Oficial que identificó:</strong>
            {% if identification.officer %}
                <a href="{{ url_for('dashboard.users_view') }}?search={{ identification.officer.folio }}">
                    {{ identification.officer.folio }}
                </a>
            {% else %}
                No disponible
            {% endif %}
        </li>
        <li class="list-group-item"><strong>Ubicación:</strong>
            {% if identification.location_lat and identification.location_lon %}
                <span class="text-success">Disponible</span>
            {% else %}No disponible{% endif %}
        </li>
        {% if identification.image_url %}
        <li class="list-group-item">
            <strong>Foto:</strong><br>
            <img src="{{ identification.image_url }}" class="img-fluid rounded mt-2" alt="Foto del vehículo">
        </li>
        {% endif %}
    </ul>
    {% endif %}

    <div id="map" class="mt-4" style="height: 400px;"></div>

    <div class="mt-4">
        <a href="{{ url_for('dashboard.identifications_view') }}" class="btn btn-secondary">Volver</a>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
<script>
  const currentIdentification = {
    id: {{ identification.id }},
    lat: {{ identification.location_lat or 'null' }},
    lon: {{ identification.location_lon or 'null' }},
    officer_id: {{ identification.officer_id or 'null' }},
    officer_folio: "{{ identification.officer.folio if identification.officer else 'N/A' }}",
    timestamp: "{{ identification.timestamp.isoformat() if identification.timestamp else '' }}",
    others: {{ other_locations | tojson }}
  };
</script>
<script src="{{ url_for('static', filename='js/identification_detail_map.js') }}"></script>
{% endblock %}