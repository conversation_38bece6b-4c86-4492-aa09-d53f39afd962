<!-- templates/dashboard/index.html -->

{% extends 'base.html' %} {# <-- SOLO UNA VEZ #}

{% block title %}Dashboard - OPERA{% endblock %}

    {% block content %}
<h1 class="mb-4">Dashboard Supervisor</h1>
<p>Bienvenido al tablero de comando de OPERA Seguridad Pública.</p>

<div class="row">
  <!-- Mapa en Vivo -->
  <div class="col-md-4 mb-3">
    <div class="card text-center h-100">
      <div class="card-body d-flex flex-column">
        <h5 class="card-title">Mapa en Vivo</h5>
        <p class="card-text flex-grow-1">Visualizar ubicación de agentes activos.</p>
        <a href="{{ url_for('dashboard.live_map_view') }}" class="btn btn-primary mt-auto">Ir al Mapa</a>
      </div>
    </div>
  </div>

  <!-- <PERSON><PERSON> Histórico -->
  <div class="col-md-4 mb-3">
    <div class="card text-center h-100">
      <div class="card-body d-flex flex-column">
        <h5 class="card-title">Mapa Histórico</h5>
        <p class="card-text flex-grow-1">Ver recorridos e identificaciones por rango de fechas y filtros.</p>
        <a href="{{ url_for('dashboard.historico_map_view') }}" class="btn btn-dark mt-auto">
          Ir al Mapa Histórico
        </a>
      </div>
    </div>
  </div>

  <!-- Historial de Turnos -->
  <div class="col-md-4 mb-3">
    <div class="card text-center h-100">
      <div class="card-body d-flex flex-column">
        <h5 class="card-title">Historial de Turnos</h5>
        <p class="card-text flex-grow-1">Consultar jornadas finalizadas y activas.</p>
        <a href="{{ url_for('dashboard.shifts_view') }}" class="btn btn-secondary mt-auto">Ver Turnos</a>
      </div>
    </div>
  </div>

  <!-- Identificaciones -->
  <div class="col-md-4 mb-3">
    <div class="card text-center h-100">
      <div class="card-body d-flex flex-column">
        <h5 class="card-title">Identificaciones</h5>
        <p class="card-text flex-grow-1">Revisar identificaciones de personas y vehículos.</p>
        <a href="{{ url_for('dashboard.identifications_view') }}" class="btn btn-info text-white mt-auto">Ver Identificaciones</a>
      </div>
    </div>
  </div>

  <!-- Novedades -->
  <div class="col-md-4 mb-3">
    <div class="card text-center h-100">
      <div class="card-body d-flex flex-column">
        <h5 class="card-title">Novedades</h5>
        <p class="card-text flex-grow-1">Gestionar las novedades operativas.</p>
        <a href="{{ url_for('dashboard.novelties_view') }}" class="btn btn-warning mt-auto">Gestionar Novedades</a>
      </div>
    </div>
  </div>

  <!-- Gestión de Usuarios -->
  <div class="col-md-4 mb-3">
    <div class="card text-center h-100">
      <div class="card-body d-flex flex-column">
        <h5 class="card-title">Gestión de Usuarios</h5>
        <p class="card-text flex-grow-1">Crear, ver y administrar cuentas de usuario.</p>
        <a href="{{ url_for('dashboard.users_view') }}" class="btn btn-success mt-auto">Administrar Usuarios</a>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
    {# Incluimos el common.js para el chequeo de auth y el logout #}
    <script src="{{ url_for('static', filename='js/dashboard_common.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Primero, verificar autenticación y rol
            if (!checkAuthAndRole()) {
                return; // Detener si la verificación falla (ya redirige)
            }

            // Lógica específica para esta página (index), si la hubiera.
            // Por ejemplo, actualizar el nombre de usuario en el navbar

            const folioSpan = document.getElementById('navbar-user-folio');
            const userFolio = localStorage.getItem('userFolio');
             if (folioSpan && userFolio) {
                 folioSpan.textContent = userFolio;
             } else if (folioSpan) {
                 // Si no hay folio en localStorage, mostrar 'Usuario' o dejar el default
                 folioSpan.textContent = 'Usuario';
             }
        });
    </script>
{% endblock %}