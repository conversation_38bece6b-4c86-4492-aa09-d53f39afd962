<!-- templates/dashboard/live_map.html -->

{% extends 'base.html' %}

{% block title %}Mapa en Vivo - OPERA{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
<!-- Tus otros estilos -->
<style>
    #map { height: 60vh; /* Ajusta altura */ min-height: 500px; background-color: #f0f0f0; border: 1px solid #ccc; width: 100%; }
    /* Estilos para botones/controles añadidos por JS si es necesario */
</style>
{% endblock %}

{% block content %}
  <h1 class="mb-4">Mapa de Agentes en Vivo</h1>
  <div id="map">Cargando mapa...</div>

  <!-- Botón para enviar mensaje a varios agentes seleccionados -->
  <button id="batchMsgBtn"
          class="btn btn-sm btn-warning position-fixed"
          style="bottom: 250px; right: 30px; z-index:1000;"
          disabled>
    Mensaje a seleccionados
  </button>

  <!-- Modal para enviar mensaje al agente (único o múltiples) -->
  <div class="modal fade" id="operatorMessageModal" tabindex="-1" aria-labelledby="operatorMessageModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="operatorMessageModalLabel">Enviar mensaje</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Cerrar"></button>
        </div>
        <div class="modal-body">
          <form id="operator-message-form">
            <!-- si es broadcast, dejamos este campo vacío y mostramos placeholder -->
            <input type="hidden" id="operator-message-folio">
            <div class="mb-3">
              <label for="operator-message-text" class="form-label">Mensaje</label>
              <textarea class="form-control" id="operator-message-text" rows="3" required></textarea>
            </div>
          </form>
          <div id="operator-message-feedback" class="alert d-none" role="alert"></div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button type="button" class="btn btn-primary" id="operator-message-send-btn">Enviar</button>
        </div>
      </div>
    </div>
  </div>
{% endblock %}


{% block scripts %}
<!-- Dependencias primero -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script> <!-- Versión actualizada -->
<script src="https://cdn.jsdelivr.net/npm/dayjs@1/dayjs.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/dayjs@1/plugin/localizedFormat.js"></script>
<script src="https://cdn.jsdelivr.net/npm/dayjs@1/locale/es.js"></script>

<!-- Scripts de tu aplicación -->
<script src="{{ url_for('static', filename='js/dashboard_common.js') }}"></script>
<!-- ***** ORDEN CORREGIDO ***** -->
<script src="{{ url_for('static', filename='js/identify.js') }}"></script>
<script src="{{ url_for('static', filename='js/live_map.js') }}"></script>
<script src="{{ url_for('static', filename='js/operator_alerts.js') }}"></script>

<!-- ************************** -->

<script>
    // Configurar Day.js en español
    if (typeof dayjs !== 'undefined' && typeof dayjs_plugin_localizedFormat !== 'undefined' && dayjs.Ls['es']) {
        dayjs.extend(dayjs_plugin_localizedFormat);
        dayjs.locale('es');
        console.log("Day.js configured for Spanish.");
    } else {
        console.warn("Day.js or Spanish locale not fully loaded.");
    }

    // Inicializar el mapa cuando el DOM esté listo
    document.addEventListener('DOMContentLoaded', () => {
        console.log("DOM fully loaded. Calling initLiveMap() from live_map.html");
        // La función initLiveMap está definida en live_map.js
        if (typeof initLiveMap === 'function') {
            initLiveMap();
        } else {
            console.error("initLiveMap function not found!");
        }
    });
</script>
{% endblock %}