<!-- templates/dashboard/novelties.html -->

{% extends 'base.html' %}

{% block title %}Gestión de Novedades - OPERA{% endblock %}

{% block content %}
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Gestión de Novedades</h1>
        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#noveltyModal" onclick="openCreateModal()">
            <i class="bi bi-plus-circle"></i> Crear Novedad {# Requiere Bootstrap Icons #}
        </button>
    </div>

    {# Formulario de Busqueda #}
    <form id="novelty-search-form" class="row g-3 align-items-end mb-3">
      <div class="col-md-6">
          <label for="novelty-search-input" class="form-label">Buscar novedad</label>
          <input type="text" id="novelty-search-input" class="form-control" placeholder="Contenido o palabra clave">
      </div>
      <div class="col-auto">
          <button type="submit" class="btn btn-primary">Buscar</button>
          <button type="button" class="btn btn-secondary" onclick="resetNoveltySearch()">Limpiar</button>
      </div>
  </form>

    {# Tabla de Novedades #}
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Contenido</th>
                    <th>Imagen</th>
                    <th>Autor (Folio)</th>
                    <th>Fecha Creación</th>
                    <th>Acciones</th>
                </tr>
            </thead>
            <tbody id="novelties-table-body">
                {# Las novedades se cargarán aquí con JavaScript #}
                <tr><td colspan="6" class="text-center">Cargando novedades...</td></tr>
            </tbody>
        </table>
    </div>

     {# Paginación (controlada por JS) - Puede ser necesaria si hay muchas novedades #}
    <nav aria-label="Page navigation">
      <ul class="pagination justify-content-center" id="pagination-controls-novelties">
        </ul>
    </nav>


    <!-- Modal para Crear/Editar Novedad -->
    <div class="modal fade" id="noveltyModal" tabindex="-1" aria-labelledby="noveltyModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="noveltyModalLabel">Crear Novedad</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <form id="novelty-form">
              {# Campo oculto para guardar el ID al editar #}
              <input type="hidden" id="novelty-id">
              <div class="mb-3">
                <label for="novelty-content" class="form-label">Contenido <span class="text-danger">*</span></label>
                <textarea class="form-control" id="novelty-content" rows="5" required></textarea>
              </div>
              <div class="mb-3">
                <label for="novelty-image-url" class="form-label">URL de Imagen (Opcional)</label>
                <input type="url" class="form-control" id="novelty-image-url" placeholder="https://ejemplo.com/imagen.jpg">
                 <small class="form-text text-muted">Asegúrate de que la URL sea accesible públicamente.</small>
              </div>
               {# Mensaje de error del formulario #}
               <div id="novelty-form-error" class="alert alert-danger d-none mt-3" role="alert"></div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
            <button type="button" class="btn btn-primary" onclick="handleNoveltySubmit()">Guardar Novedad</button>
          </div>
        </div>
      </div>
    </div>

{% endblock %}

{% block scripts %}
{# Axios y Day.js ya están incluidos (o pon Day.js en base.html) #}
{# Bootstrap JS (para Modal) ya está en base.html #}
<script src="{{ url_for('static', filename='js/dashboard_common.js') }}"></script>
{# ¡Asume que creaste este archivo! #}
<script src="{{ url_for('static', filename='js/novelties.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', () => {
        initNoveltiesPage(); // Asume que esta es tu función init
    });
</script>
{% endblock %}