<!-- templates/dashboard/shift_detail.html -->
{% extends 'base.html' %}
{% block title %}Detalle Turno {{ shift_id }} - OPERA{% endblock %} {# Usar shift_id del contexto #}
{% block content %}
    <h1 class="mb-4">Detalle del Turno #{{ shift_id }}</h1>
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Información General</div>
                <ul class="list-group list-group-flush">
                    {# Acceder a datos del diccionario shift_data (si se pasan desde Python) #}
                    <<li class="list-group-item"><b>Oficial:</b> <span id="detail-officer-folio">{{ shift_data.officer_folio }}</span></li>
                    <li class="list-group-item"><b>Inicio:</b> <span id="detail-start-time">{{ shift_data.start_time_str }}</span></li>
                    <li class="list-group-item"><b>Fin:</b> <span id="detail-end-time">{{ shift_data.end_time_str }}</span></li>
                    <li class="list-group-item"><b>Movilidad:</b> <span id="detail-mobility">{{ shift_data.mobility_type }}</span></li>
                    <li class="list-group-item"><b>Unidad:</b> <span id="detail-unit">{{ shift_data.unit_id }}</span></li>
                    <li class="list-group-item"><b>Estado:</b> <span id="detail-status">{{ 'En curso' if shift_data.is_active else 'Finalizado' }}</span></li>                  
                </ul>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Estadísticas</div>
                <ul class="list-group list-group-flush">
                    <li class="list-group-item">
                        <b>Distancia Recorrida:</b>
                        <span id="stat-distance">Calculando...</span>
                    </li>
                    <li class="list-group-item">
                        <b>Identificaciones (Personas):</b>
                        <span id="stat-person-ids">Calculando...</span>
                        <a href="/dashboard/identificaciones?shift_id={{ shift_id }}" class="ms-2" title="Ver identificaciones de este turno">
                            <i class="bi bi-box-arrow-up-right"></i>
                        </a>
                    </li>
                    <li class="list-group-item">
                        <b>Identificaciones (Vehículos):</b>
                        <span id="stat-vehicle-ids">Calculando...</span>
                        <a href="/dashboard/identificaciones?shift_id={{ shift_id }}" class="ms-2" title="Ver identificaciones de este turno">
                            <i class="bi bi-box-arrow-up-right"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>      
    <h2 class="mb-3">Recorrido Realizado</h2>
    <div id="detail-map">Cargando mapa de recorrido...</div>
{% endblock %}

{% block scripts %}
    {# QUITAR: dashboard_common.js ya está en base.html #}
    {# <script src="{{ url_for('static', filename='js/dashboard_common.js') }}"></script> #}

    {# Script específico para esta página #}
    <script src="{{ url_for('static', filename='js/shift_detail.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Pasar el shift_id (del contexto de Flask) a la función init
            initShiftDetail({{ shift_id }});
        });
    </script>
{% endblock %}