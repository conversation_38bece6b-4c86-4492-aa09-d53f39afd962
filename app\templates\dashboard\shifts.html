{% extends 'base.html' %}

{% block title %}Historial de Turnos - OPERA{% endblock %}

{% block content %}
    <h1 class="mb-4">Historia<PERSON> de Turnos</h1>

    <!-- Filtros -->
    <form id="filter-form" class="row g-3 mb-4 align-items-end bg-light p-3 rounded">
        <div class="col-md-3">
            <label for="filter-folio" class="form-label">Oficial (Folio)</label>
            <input type="text" id="filter-folio" name="folio" class="form-control" placeholder="Folio">
        </div>
        <div class="col-md-3">
            <label for="filter-date-from" class="form-label">Desde</label>
            <input type="date" id="filter-date-from" name="date_from" class="form-control">
        </div>
        <div class="col-md-3">
            <label for="filter-date-to" class="form-label">Hasta</label>
            <input type="date" id="filter-date-to" name="date_to" class="form-control">
        </div>
        <div class="col-md-3">
            <label for="filter-status" class="form-label">Estado</label>
            <select id="filter-status" name="status" class="form-select">
                <option value="">Todos</option>
                <option value="active">Activo</option>
                <option value="finished">Finalizado</option>
            </select>
        </div>
        <div class="col-12">
            <button type="submit" class="btn btn-primary">Filtrar</button>
            <button type="reset" class="btn btn-secondary" onclick="resetFilters()">Limpiar</button>
        </div>
    </form>

    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>ID Turno</th>
                    <th>Oficial (Folio)</th>
                    <th>Inicio</th>
                    <th>Fin</th>
                    <th>Movilidad</th>
                    <th>Distancia</th>
                    <th>ID Pers.</th>
                    <th>ID Veh.</th>
                    <th>Estado</th>
                    <th>Acciones</th>
                </tr>
            </thead>
            <tbody id="shifts-table-body">
                <tr>
                    <td colspan="10" class="text-center">Cargando turnos...</td>
                </tr>
            </tbody>
        </table>
    </div>

    <nav aria-label="Page navigation">
      <ul class="pagination justify-content-center" id="pagination-controls"></ul>
    </nav>
{% endblock %}

{% block scripts %}
    <script src="{{ url_for('static', filename='js/shifts.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            initShiftsTable();
        });
    </script>
{% endblock %}
