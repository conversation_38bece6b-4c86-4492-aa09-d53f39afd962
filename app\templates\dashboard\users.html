<!-- templates/dashboard/users.html -->
{% extends 'base.html' %}

{% block title %}Gestión de Usuarios - OPERA{% endblock %}

{% block content %}
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Gestión de Usuarios</h1>
        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#userModal" onclick="openCreateUserModal()">
            <i class="bi bi-person-plus-fill"></i> Crear Usuario
        </button>
    </div>

    {# Opcional: Añadir filtros para la tabla de usuarios #}

    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Folio</th>
                    <th>Nombre</th>
                    <th>Rol</th>
                    <th>Unidad</th>
                    <th>Activo</th>
                    <th>Fecha Creación</th>
                    <th>Acciones</th> {# Para editar/desactivar en el futuro #}
                </tr>
            </thead>
            <tbody id="users-table-body">
                {# Los usuarios se cargarán aquí con JavaScript #}
                <tr><td colspan="8" class="text-center">Cargando usuarios...</td></tr>
            </tbody>
        </table>
    </div>

    {# Opcional: Añadir paginación si la lista de usuarios es larga #}
    <nav aria-label="Page navigation">
      <ul class="pagination justify-content-center" id="pagination-controls-users">
        </ul>
    </nav>


    <!-- Modal para Crear/Editar Usuario -->
    <div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="userModalLabel">Crear Nuevo Usuario</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <form id="user-form">
              {# Campo oculto para ID (para futura edición) #}
              <input type="hidden" id="user-id">

              <div class="mb-3">
                <label for="user-folio" class="form-label">Folio <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="user-folio" required>
              </div>
              <div class="mb-3">
                <label for="user-name" class="form-label">Nombre Completo <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="user-name" required>
              </div>
              <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="user-password" class="form-label">Contraseña <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="user-password" required>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="user-password-confirm" class="form-label">Confirmar Contraseña <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="user-password-confirm" required>
                  </div>
              </div>
              <div class="mb-3">
                <label for="user-role" class="form-label">Rol <span class="text-danger">*</span></label>
                <select class="form-select" id="user-role" required>
                  <option value="agente" selected>Agente</option>
                  <option value="supervisor">Supervisor</option>
                  <option value="comando">Comando</option>
                </select>
              </div>
              <div class="mb-3">
                <label for="user-unit" class="form-label">Unidad Operativa (Opcional)</label>
                <input type="text" class="form-control" id="user-unit">
              </div>

               {# Mensaje de error del formulario #}
               <div id="user-form-error" class="alert alert-danger d-none mt-3" role="alert"></div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
            <button type="button" class="btn btn-primary" onclick="handleUserFormSubmit()">Guardar Usuario</button>
          </div>
        </div>
      </div>
    </div>

{% endblock %}

{% block scripts %}
    {# Dependencias comunes y formateador de fecha #}
    <script src="{{ url_for('static', filename='js/dashboard_common.js') }}"></script>
    {# Day.js para formatear fechas (si no está ya en base.html) #}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dayjs/1.11.10/dayjs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dayjs/1.11.10/locale/es.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dayjs/1.11.10/plugin/localizedFormat.min.js"></script>
    <script>dayjs.locale('es'); dayjs.extend(window.dayjs_plugin_localizedFormat);</script>

    {# Script específico para esta página #}
    <script src="{{ url_for('static', filename='js/users.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            initUsersPage(); // Llama a la función de inicialización del JS
        });
    </script>
{% endblock %}