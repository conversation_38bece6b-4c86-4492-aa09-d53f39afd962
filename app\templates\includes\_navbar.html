<!-- templates/includes/_navbar.html -->

<nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
    <div class="container">
        <a class="navbar-brand" href="{{ url_for('dashboard.index') }}">
            OPERA Dashboard
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <!-- Enlaces principales del Dashboard -->
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('dashboard.live_map_view') }}">Mapa en Vivo</a>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('dashboard.historico_map_view') }}">Mapa Historico</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('dashboard.shifts_view') }}">Historial Turnos</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('dashboard.identifications_view') }}">Identificaciones</a>
                </li>
                 <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('dashboard.novelties_view') }}">Novedades</a>
                </li>
                 <li class="nav-item"> {# <-- NUEVO ITEM DE MENÚ --> #}
                    <a class="nav-link" href="{{ url_for('dashboard.users_view') }}">Usuarios</a>
                </li>
            </ul>
            <!-- Información del usuario y Logout -->
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                         {# El JS puede actualizar esto si guarda el folio en localStorage #}
                         <span id="navbar-user-folio">{{ user_info.folio if user_info else 'Usuario' }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <button class="dropdown-item" onclick="logout()">Cerrar Sesión</button>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

{# Script básico para logout (borra token de localStorage) #}
{# Es mejor tener este script en un archivo JS común (ej: dashboard_common.js) #}
{# pero lo dejamos aquí por ahora si aún no has hecho la refactorización #}
<script>
    function logout() {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('userRole'); // Limpiar rol también
        localStorage.removeItem('userFolio'); // Limpiar folio si lo guardaste
        // Opcional: llamar a API de logout si usas blocklist
        window.location.href = "{{ url_for('dashboard.login_view') }}"; // Redirigir al login del dashboard
    }

    // Opcional: Actualizar nombre de usuario en navbar si está en localStorage
    document.addEventListener('DOMContentLoaded', (event) => {
        const folioSpan = document.getElementById('navbar-user-folio');
        const userFolio = localStorage.getItem('userFolio'); // Asume que guardaste esto en el login
         if (folioSpan && userFolio) {
             folioSpan.textContent = userFolio;
         } else if (folioSpan) {
             // Si no hay folio en localStorage, mostrar 'Usuario' o dejar el default
              // El default viene del render_template si user_info es None
             // folioSpan.textContent = 'Usuario';
         }
    });
</script>