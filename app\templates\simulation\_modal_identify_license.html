<!-- app/templates/simulation/_modal_identify_license.html -->
<div class="modal fade" id="identifyLicenseModal" tabindex="-1" aria-labelledby="identifyLicenseModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="identifyLicenseModalLabel">Identificar Licencia (Sim)</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Cerrar"></button>
      </div>
      <div class="modal-body">
        <form id="identify-license-form">

          <!-- Botón iniciar -->
          <div class="mb-3 text-center">
            <button type="button" class="btn btn-outline-primary" id="btn-scan-license">
              <i class="bi bi-qr-code-scan me-2"></i>Escanear Licencia
            </button>
          </div>

          <!-- Selector c<PERSON>mara -->
          <select id="camera-selection-license" class="form-select form-select-sm mb-2 d-none"
                  aria-label="Seleccionar cámara Licencia"></select>

          <!-- Contenedor Scanner -->
          <div id="scanner-container-license"
               class="mb-3"
               style="width:100%; aspect-ratio:1/1; max-height:400px; display:none;
                      border:1px solid #ccc; background-color:#f8f9fa;
                      position:relative; overflow:hidden;">
            <video id="reader-license"
                   style="width:100%; height:100%; object-fit:cover;"
                   playsinline></video>

            <!-- recuadro de escaneo -->
            <div id="scan-region-overlay-license"
                 style="position:absolute;
                        top:50%; left:50%;
                        transform:translate(-50%,-50%);
                        width:85%; height:25%;
                        border:3px solid rgba(255,0,0,0.7);
                        box-shadow:0 0 0 9999px rgba(0,0,0,0.5);
                        pointer-events:none;
                        z-index:10;"></div>

            <!-- botón detener -->
            <button type="button"
                    class="btn btn-sm btn-danger position-absolute bottom-0 end-0 m-2"
                    id="btn-stop-scan-license"
                    aria-label="Detener escaneo Licencia"
                    style="z-index:11;">
              <i class="bi bi-stop-circle"></i> Detener
            </button>
          </div>

          <!-- Campo raw -->
          <div class="mb-2">
            <label for="license-raw" class="form-label">String Licencia (Raw)</label>
            <input type="text"
                   class="form-control form-control-sm"
                   id="license-raw"
                   placeholder="Datos crudos del escaneo..."
                   readonly>
            <small class="form-text text-muted">Datos completos del escáner.</small>
          </div>
          <hr>

          <!-- Campos licencia básicos -->
          <div class="row">
            <div class="col-md-6 mb-2">
              <label for="license-number" class="form-label">Nro Licencia <span class="text-danger">*</span></label>
              <input type="text" class="form-control form-control-sm" id="license-number" required>
            </div>
            <div class="col-md-6 mb-2">
              <label for="license-categories" class="form-label">Categorías</label>
              <input type="text" class="form-control form-control-sm" id="license-categories">
            </div>
          </div>
          <div class="row">
            <div class="col-md-6 mb-2">
              <label for="license-lastname" class="form-label">Apellido</label>
              <input type="text" class="form-control form-control-sm" id="license-lastname">
            </div>
            <div class="col-md-6 mb-2">
              <label for="license-firstnames" class="form-label">Nombres</label>
              <input type="text" class="form-control form-control-sm" id="license-firstnames">
            </div>
          </div>
          <div class="row">
            <div class="col-md-6 mb-2">
              <label for="license-dob" class="form-label">Nacimiento</label>
              <input type="date" class="form-control form-control-sm" id="license-dob">
            </div>
            <div class="col-md-6 mb-2">
              <label for="license-expiry" class="form-label">Vencimiento</label>
              <input type="date" class="form-control form-control-sm" id="license-expiry">
            </div>
          </div>
          <div class="mb-3">
            <label for="license-notes" class="form-label">Notas Adicionales</label>
            <textarea class="form-control form-control-sm" id="license-notes" rows="2"></textarea>
          </div>
          <div id="identify-license-error" class="alert alert-danger d-none mt-2 p-2" role="alert"></div>
        </form>
      </div><!-- fin modal-body -->
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancelar</button>
        <button type="button" class="btn btn-primary btn-sm" onclick="handleIdentifyLicense()">Registrar Identificación</button>
      </div>
    </div>
  </div>
</div>
