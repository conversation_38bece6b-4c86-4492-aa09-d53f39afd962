<!-- app/templates/simulation/_modal_identify_person.html -->
<div class="modal fade" id="identifyPersonModal" tabindex="-1" aria-labelledby="identifyPersonModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="identifyPersonModalLabel">Identificar Persona (Sim)</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Cerrar"></button>
      </div>
      <div class="modal-body">
        <form id="identify-person-form">

          <!-- Botón iniciar -->
          <div class="mb-3 text-center">
            <button type="button" class="btn btn-outline-primary" id="btn-scan-dni">
              <i class="bi bi-qr-code-scan me-2"></i>Escanear DNI
            </button>
          </div>

          <!-- Selector cámara -->
          <select id="camera-selection-dni" class="form-select form-select-sm mb-2 d-none" aria-label="Seleccionar cámara DNI"></select>

          <!-- Contenedor Scanner -->
          <div id="scanner-container-person" class="mb-3" style="width: 100%; aspect-ratio: 1 / 1; max-height: 400px; display: none; border: 1px solid #ccc; background-color: #f8f9fa; position: relative; overflow: hidden;">
            <video id="reader-person" style="width: 100%; height: 100%; object-fit: cover;" playsinline></video>
            <div id="scan-region-overlay" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 85%; height: 25%; border: 3px solid rgba(255, 0, 0, 0.7); box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5); pointer-events: none; z-index: 10;"></div>
            <button type="button" class="btn btn-sm btn-danger position-absolute bottom-0 end-0 m-2" id="btn-stop-scan-person" aria-label="Detener escaneo DNI" style="z-index: 11;">
               <i class="bi bi-stop-circle"></i> Detener
            </button>
          </div>

          <!-- Campo raw -->
          <div class="mb-2">
            <label for="person-dni-raw" class="form-label">String DNI (Raw)</label>
            <input type="text" class="form-control form-control-sm" id="person-dni-raw" placeholder="Datos crudos del escaneo..." readonly>
            <small class="form-text text-muted">Datos completos del escáner.</small>
          </div>
          <hr>

          <!-- Campos DNI, Apellido, Nombre -->
          <div class="row">
            <div class="col-md-6 mb-2">
              <label for="person-dni" class="form-label">DNI <span class="text-danger">*</span></label>
              <input type="text" class="form-control form-control-sm" id="person-dni" required>
            </div>
            <div class="col-md-6 mb-2">
              <label for="person-lastname" class="form-label">Apellido <span class="text-danger">*</span></label>
              <input type="text" class="form-control form-control-sm" id="person-lastname" required>
            </div>
          </div>
          <div class="mb-2">
            <label for="person-firstname" class="form-label">Nombres <span class="text-danger">*</span></label>
            <input type="text" class="form-control form-control-sm" id="person-firstname" required>
          </div>

          <!-- ***** NUEVOS CAMPOS ***** -->
          <div class="row">
            <div class="col-md-4 mb-2">
                <label for="person-gender" class="form-label">Género</label>
                <select class="form-select form-select-sm" id="person-gender">
                    <option value="" selected>Seleccionar...</option>
                    <option value="M">Masculino</option>
                    <option value="F">Femenino</option>
                    <option value="X">Otro/X</option>
                </select>
            </div>
            <div class="col-md-5 mb-2">
                <label for="person-dob" class="form-label">Fecha Nacimiento</label>
                <input type="date" class="form-control form-control-sm" id="person-dob">
            </div>
            <div class="col-md-3 mb-2">
                 <label for="person-age" class="form-label">Edad</label>
                 <input type="text" class="form-control form-control-sm" id="person-age" readonly tabindex="-1">
            </div>
          </div>
          <!-- ***** FIN NUEVOS CAMPOS ***** -->

          <!-- ***** AQUÍ YA NO ESTÁ LA DUPLICACIÓN ***** -->

          <div class="mb-3">
            <label for="person-notes" class="form-label">Notas Adicionales</label>
            <textarea class="form-control form-control-sm" id="person-notes" rows="2"></textarea>
          </div>
          <div id="identify-person-error" class="alert alert-danger d-none mt-2 p-2" role="alert"></div>
        </form>
      </div> <!-- Fin modal-body -->
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancelar</button>
        <button type="button" class="btn btn-primary btn-sm" onclick="handleIdentifyPerson()">Registrar Identificación</button>
      </div>
    </div> <!-- Fin modal-content -->
  </div> <!-- Fin modal-dialog -->
</div> <!-- Fin modal -->