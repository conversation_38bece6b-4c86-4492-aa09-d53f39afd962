<!-- app/templates/simulation/_modal_identify_vehicle.html -->
<div class="modal fade" id="identifyVehicleModal" tabindex="-1" aria-labelledby="identifyVehicleModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="identifyVehicleModalLabel">Identificar Vehículo (Sim)</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Cerrar"></button>
      </div>
      <div class="modal-body">
        <form id="identify-vehicle-form">

          <!-- Botón para iniciar escaneo -->
          <div class="mb-3 text-center">
            <button type="button" class="btn btn-outline-primary" id="btn-scan-vehicle">
               <i class="bi bi-upc-scan me-2"></i>Escanear Cédula/QR
            </button>
          </div>

          <!-- Selector de c<PERSON> -->
          <select id="camera-selection-vehicle" class="form-select form-select-sm mb-2 d-none" aria-label="Seleccionar cámara vehículo"></select>

          <!-- Contenedor del escáner -->
          <div id="scanner-container-vehicle" class="mb-3" style="width: 100%; aspect-ratio: 1 / 1; max-height: 400px; display: none; border: 1px solid #ccc; background-color: #f8f9fa; position: relative;">
            <div id="reader-vehicle" style="width: 100%; height: 100%;"></div> <!-- html5-qrcode usa div, ZXing usa video -->
             <button type="button" class="btn btn-sm btn-danger position-absolute bottom-0 end-0 m-2" id="btn-stop-scan-vehicle" aria-label="Detener escaneo vehículo">
               <i class="bi bi-stop-circle"></i> Detener
             </button>
          </div>

          <hr>

          <!-- Campos de datos del vehículo -->
          <div class="mb-2">
            <label for="vehicle-plate" class="form-label">Patente/Dominio <span class="text-danger">*</span></label>
            <input type="text" class="form-control form-control-sm text-uppercase" id="vehicle-plate" required pattern="([A-Z]{2}[0-9]{3}[A-Z]{2})|([A-Z]{3}[0-9]{3})|([0-9]{3}[A-Z]{3})" title="Formato: AA123CD, ABC123 o 123ABC">
          </div>

          <!-- ***** NUEVO CAMPO PARA NÚMERO DE CREDENCIAL ***** -->
          <div class="mb-2">
            <label for="vehicle-credential" class="form-label">N° Credencial</label>
            <input type="text" class="form-control form-control-sm" id="vehicle-credential">
          </div>
          <!-- *************************************************** -->

          <div class="row">
            <div class="col-md-6 mb-2">
              <label for="vehicle-brand" class="form-label">Marca</label>
              <input type="text" class="form-control form-control-sm" id="vehicle-brand">
            </div>
            <div class="col-md-6 mb-2">
              <label for="vehicle-model" class="form-label">Modelo</label>
              <input type="text" class="form-control form-control-sm" id="vehicle-model">
            </div>
          </div>

          <!-- Campo para Chasis -->
          <div class="mb-2">
            <label for="vehicle-chassis" class="form-label">N° Chasis (Últ. dígitos)</label>
            <input type="text" class="form-control form-control-sm" id="vehicle-chassis">
          </div>

          <div class="row">
            <div class="col-md-6 mb-2">
              <label for="vehicle-type" class="form-label">Tipo</label>
              <input type="text" class="form-control form-control-sm" id="vehicle-type" placeholder="Auto, Moto, Camioneta...">
            </div>
            <div class="col-md-6 mb-2">
              <label for="vehicle-color" class="form-label">Color</label>
              <input type="text" class="form-control form-control-sm" id="vehicle-color">
            </div>
          </div>

          <div class="mb-3">
             <!-- Ahora Notas es solo para texto adicional o el QR raw si falla el parseo -->
            <label for="vehicle-notes" class="form-label">Notas Adicionales</label>
            <textarea class="form-control form-control-sm" id="vehicle-notes" rows="2" placeholder="Observaciones adicionales..."></textarea>
          </div>

          <div id="identify-vehicle-error" class="alert alert-danger d-none mt-2 p-2" role="alert"></div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancelar</button>
        <button type="button" class="btn btn-primary btn-sm" onclick="handleIdentifyVehicle()">Registrar Identificación</button>
      </div>
    </div>
  </div>
</div>