<!-- templates/simulation/simulator.html -->
<!doctype html>
<html lang="es">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Simulador de Agente - OPERA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; background-color: #f8f9fa; }
        .action-card { margin-bottom: 20px; }
        .token-info { font-size: 0.8em; color: #6c757d; word-break: break-all; }
        .shift-info { font-weight: bold; color: green; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4 text-center">Simulador de Agente</h1>
        <p class="text-center text-muted">Use esta página para simular acciones de un agente y probar el backend/dashboard.</p>
        <p class="text-center text-danger fw-bold">¡RECORDATORIO: Deshabilitar la ruta /_simulate antes de producción!</p>

        {# Mostrar mensajes flash #}
        {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
            {% for category, message in messages %}
              <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
              </div>
            {% endfor %}
          {% endif %}
        {% endwith %}

        <hr>

        {# --- Sección de Login de Agente --- #}
        <div class="card action-card">
            <div class="card-header">1. Obtener Token de Agente</div>
            <div class="card-body">
                <form action="{{ url_for('simulation.login_agent') }}" method="POST">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-4">
                            <label for="agent_folio_login" class="form-label">Seleccionar Agente</label>
                            <select id="agent_folio_login" name="agent_folio" class="form-select" required>
                                <option value="">-- Elige un Agente --</option>
                                {% for agent in agents %}
                                    <option value="{{ agent.folio }}">
                                        {{ agent.folio }} - {{ agent.name }}
                                        {% if agent.folio in agent_tokens %}(Token Obtenido){% endif %}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="agent_password" class="form-label">Contraseña</label>
                            <input type="password" id="agent_password" name="agent_password" class="form-control" required placeholder="Contraseña del agente">
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary w-100">Obtener/Actualizar Token</button>
                        </div>
                    </div>
                </form>
                {% if agent_tokens %}
                <div class="mt-3">
                    <p class="mb-1"><strong>Tokens Actuales (solo para debug):</strong></p>
                    {% for folio, token in agent_tokens.items() %}
                        <p class="token-info"><b>{{ folio }}:</b> {{ token[:15] }}...{{ token[-15:] }}</p>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>

        <hr>

        {# --- Sección de Acciones de Turno/GPS --- #}
         <div class="card action-card">
             <div class="card-header">2. Acciones del Agente</div>
             <div class="card-body">
                 <form id="action-form" method="POST"> {# Se usará JS para cambiar la acción #}
                     <div class="row g-3 align-items-end">
                         <div class="col-md-4">
                             <label for="agent_folio_action" class="form-label">Agente para Acción</label>
                             <select id="agent_folio_action" name="agent_folio_action" class="form-select" required>
                                 <option value="">-- Elige Agente con Token --</option>
                                 {% for folio in agent_tokens.keys() %}
                                     <option value="{{ folio }}">
                                         {{ folio }}
                                         {% if folio in active_shifts %}
                                             <span class="shift-info">(Turno ID: {{ active_shifts[folio] }})</span>
                                         {% endif %}
                                     </option>
                                 {% endfor %}
                             </select>
                         </div>
                         <div class="col-md-8">
                             <p class="mb-1"><strong>Selecciona Acción:</strong></p>
                             <div class="btn-group w-100" role="group" aria-label="Acciones de agente">
                                 <button type="button" class="btn btn-success" onclick="submitAction('{{ url_for('simulation.start_agent_shift') }}')">Iniciar Turno</button>
                                 <button type="button" class="btn btn-danger" onclick="submitAction('{{ url_for('simulation.end_agent_shift') }}')">Finalizar Turno</button>
                                 <button type="button" class="btn btn-info" onclick="submitAction('{{ url_for('simulation.send_agent_gps') }}')">Enviar GPS</button>
                                 {# <button type="button" class="btn btn-secondary" disabled>Identificar Persona</button> #}
                                 {# <button type="button" class="btn btn-secondary" disabled>Identificar Vehículo</button> #}
                             </div>
                         </div>
                     </div>
                     <hr>
                     {# --- Campos Adicionales (visibles según acción) --- #}
                     <div class="row g-3 mt-2">
                         <div class="col-md-4" id="mobility-group">
                             <label for="mobility_type" class="form-label">Tipo Movilidad (al Iniciar)</label>
                             <select id="mobility_type" name="mobility_type" class="form-select">
                                 <option value="Peatonal">Peatonal</option>
                                 <option value="Móvil">Móvil</option>
                                 <option value="Moto">Moto</option>
                                 <option value="Bici">Bici</option>
                             </select>
                         </div>
                          <div class="col-md-4" id="latitude-group">
                              <label for="latitude" class="form-label">Latitud (para GPS)</label>
                              <input type="number" step="any" id="latitude" name="latitude" class="form-control" placeholder="-40.8095">
                          </div>
                          <div class="col-md-4" id="longitude-group">
                              <label for="longitude" class="form-label">Longitud (para GPS)</label>
                              <input type="number" step="any" id="longitude" name="longitude" class="form-control" placeholder="-62.9960">
                          </div>
                     </div>
                 </form>
             </div>
         </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function submitAction(actionUrl) {
            const form = document.getElementById('action-form');
            form.action = actionUrl; // Cambia la URL a la que se enviará el form
            // Validar que se haya seleccionado un agente
            const agentSelect = document.getElementById('agent_folio_action');
            if (!agentSelect.value) {
                alert('Por favor, selecciona un agente para realizar la acción.');
                return;
            }
            // Podrías añadir validación específica aquí (ej: lat/lon para GPS)
            form.submit(); // Envía el formulario al endpoint correcto
        }
    </script>
</body>
</html>