# app/utils/stats_calculator.py

from haversine import haversine, Unit
from app import db
from app.models.shift import Shift
from app.models.location import GPSTrackPoint
from app.models.identification import IdentifiedPerson, IdentifiedVehicle
from sqlalchemy import func # Para usar func.count

def calculate_shift_stats(shift_id: int) -> dict:
    """
    Calcula la distancia recorrida y el número de identificaciones para un turno.

    Args:
        shift_id: El ID del turno (Shift).

    Returns:
        Un diccionario con 'total_distance_km', 'person_identifications',
        'vehicle_identifications', o None si el turno no existe.
        Retorna 0.0 para distancia si no hay suficientes puntos GPS.
    """
    shift = Shift.query.get(shift_id)
    if not shift:
        return None

    # --- Calcular Distancia ---
    total_distance = 0.0
    # Obtener todos los puntos GPS para este turno, ordenados por tiempo
    gps_points = GPSTrackPoint.query.filter_by(shift_id=shift_id)\
                                     .order_by(GPSTrackPoint.timestamp.asc()).all()

    if len(gps_points) > 1:
        # Iterar por pares de puntos consecutivos
        for i in range(len(gps_points) - 1):
            point1 = (gps_points[i].latitude, gps_points[i].longitude)
            point2 = (gps_points[i+1].latitude, gps_points[i+1].longitude)
            # Calcular distancia entre los dos puntos en kilómetros
            distance_segment = haversine(point1, point2, unit=Unit.KILOMETERS)
            total_distance += distance_segment

    # --- Contar Identificaciones (Usando relaciones si están definidas o consulta directa) ---

    # Opción A: Usando relaciones (si son 'dynamic' o si no te importa cargar todo)
    # person_count = shift.identifications_person.count() # Si la relación es dynamic
    # vehicle_count = shift.identifications_vehicle.count() # Si la relación es dynamic

    # Opción B: Usando consulta directa (más eficiente si no necesitas los objetos)
    person_count = db.session.query(func.count(IdentifiedPerson.id))\
                             .filter(IdentifiedPerson.shift_id == shift_id).scalar() or 0
    vehicle_count = db.session.query(func.count(IdentifiedVehicle.id))\
                              .filter(IdentifiedVehicle.shift_id == shift_id).scalar() or 0

    # --- Devolver Resultados ---
    stats = {
        "shift_id": shift_id,
        "total_distance_km": round(total_distance, 2), # Redondear a 2 decimales
        "person_identifications": person_count,
        "vehicle_identifications": vehicle_count
    }

    # --- Opcional: Actualizar campos en el modelo Shift ---
    # Si añadiste los campos al modelo Shift, puedes actualizarlos aquí,
    # especialmente si llamas a esta función al finalizar el turno.
    # shift.total_distance_km = stats["total_distance_km"]
    # shift.person_id_count = stats["person_identifications"]
    # shift.vehicle_id_count = stats["vehicle_identifications"]
    # db.session.add(shift)
    # db.session.commit() # Cuidado con commits dentro de funciones llamadas por rutas

    return stats
