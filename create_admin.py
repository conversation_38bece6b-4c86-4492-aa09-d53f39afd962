# create_admin.py
# Ruta sugerida: raíz del proyecto

from app import create_app, db
from app.models.user import User

app = create_app('development')  # O 'default', según tu entorno

with app.app_context():
    admin_folio = '0001'  # Identificador único (folio)
    password = 'admin123'     # Contraseña inicial
    nombre = 'Administrador General'

    # Verificar si ya existe
    existing_user = User.query.filter_by(folio=admin_folio).first()

    if existing_user:
        print(f"ℹ️  Ya existe un usuario con folio '{admin_folio}'")
    else:
        user = User(
            folio=admin_folio,
            name=nombre,
            role='supervisor',
            unit_id='central',  # Ajustalo si es necesario
            is_active=True
        )
        user.set_password(password)  # Usar hashing seguro
        db.session.add(user)
        db.session.commit()
        print(f"✅ Usuario '{admin_folio}' creado con rol 'administrador'")
