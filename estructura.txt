opera_app/
├── app/                      # Directorio principal de la aplicación Flask
│   ├── __init__.py           # Fábrica de la aplicación Flask, inicializa extensiones
│   ├── config.py             # Configuraciones (DB URI, Secret Key, etc.)
│   ├── models/               # Modelos SQLAlchemy (tablas de la BD)
│   │   ├── __init__.py
│   │   ├── user.py           # Modelo User (Agente, Supervisor)
│   │   ├── shift.py          # Modelo Shift (Jornada)
│   │   ├── novelty.py        # Modelo Novedad y NovedadReadStatus
│   │   ├── message.py        # Modelo OperatorMessage y NotificationStatus (NUEVO)
│   │   ├── location.py       # Modelo para puntos GPS del recorrido
│   │   ├── identification.py # Modelos para Persona y Vehiculo identificados
│   │   └── ...               # Otros modelos necesarios
│   ├── schemas/              # Esquemas Marshmallow (para serialización/validación)
│   │   ├── __init__.py
│   │   └── ...               # Esquemas para cada modelo (UserSchema, ShiftSchema, etc.)
│   ├── routes/               # Blueprints para organizar las rutas de la API
│   │   ├── __init__.py
│   │   ├── auth.py           # Rutas para login/logout
│   │   ├── shifts.py         # Rutas para iniciar/finalizar jornada, registrar ubicación
│   │   ├── novelties.py      # Rutas para gestión y visualización de novedades
│   │   ├── messages.py       # Rutas para mensajes del operador y notificaciones (NUEVO)
│   │   ├── identification.py # Rutas para registro de identificaciones
│   │   ├── background_check.py # Rutas para consulta de antecedentes (simulada o real)
│   │   └── dashboard.py      # Rutas para el tablero de supervisor (si se hace con Jinja2)
│   ├── services/             # Lógica de negocio específica
│   │   ├── __init__.py
│   │   └── dni_parser.py     # Servicio para parsear el string del DNI
│   │   └── map_service.py    # Lógica relacionada con mapas (si es necesario en backend)
│   ├── static/               # Archivos estáticos (CSS, JS, imágenes) si se usa Jinja2
│   ├── templates/   # Carpeta principal de plantillas
│   │   ├── base.html         # Plantilla base común
│   │   ├── index.html        # Página principal (opcional)
│   │   ├── auth/             # Plantillas de autenticación
│   │   │   └── login.html
│   │   ├── dashboard/        # Plantillas del tablero supervisor
│   │   │   ├── index.html         # Vista principal del dashboard
│   │   │   ├── live_map.html      # Mapa en tiempo real
│   │   │   ├── shifts.html        # Historial de turnos
│   │   │   ├── shift_detail.html  # Detalle de un turno
│   │   │   ├── identifications.html # Listado de identificaciones
│   │   │   └── novelties.html     # Gestión de novedades
│   │   └── includes/         # Fragmentos reutilizables
│   │       ├── _navbar.html       # Barra de navegación
│   │       └── _messages.html     # Para mensajes flash
│   └── utils/                # Funciones de utilidad
│       └── decorators.py     # Decoradores personalizados (ej: @supervisor_required)
├── migrations/               # Directorio de migraciones Alembic
├── tests/                    # Pruebas unitarias y de integración
├── .env                      # Variables de entorno (NO subir a Git)
├── .flaskenv                 # Variables de entorno específicas de Flask (FLASK_APP, FLASK_ENV)
├── requirements.txt          # Dependencias de Python
├── run.py                    # Script para ejecutar la aplicación (desarrollo)
├── ESTRUCTURA_BASE_DATOS.md  # Documentación completa de la base de datos (NUEVO)
├── SISTEMA_NOTIFICACIONES.md # Documentación del sistema de notificaciones (NUEVO)
└── android_backup/           # Backup completo de archivos Android modificados (NUEVO)

## 🔔 NUEVAS FUNCIONALIDADES - Sistema de Notificaciones

### 📱 Componentes Agregados:

#### Servidor (Flask):
- app/models/message.py       # Modelos OperatorMessage y NotificationStatus
- app/routes/messages.py      # Endpoints para mensajes y notificaciones
- Modificado: app/routes/dashboard.py # Persistencia de mensajes del operador

#### Base de Datos:
- operator_messages           # Tabla para mensajes del operador
- notification_status         # Tabla para estado de notificaciones

#### Android:
- Sistema de polling cada 60s # Verificación de notificaciones pendientes
- Notificaciones locales      # Aparecen incluso con teléfono bloqueado
- Autenticación JWT temporal  # Para requests de polling

### 🔄 Flujo de Notificaciones:
1. Operador envía mensaje → Guarda en BD + WebSocket
2. Si WebSocket falla → Polling lo detecta → Notificación local
3. Novedades nuevas → Polling verifica → Notifica si hay cambios

### 📊 Endpoints Nuevos:
- GET  /api/messages/unread                    # Mensajes no leídos
- GET  /api/messages/notification-summary     # Resumen de notificaciones
- POST /api/messages/<id>/read                 # Marcar mensaje como leído
- GET  /api/messages/check-new-novelties       # Verificar novedades nuevas
- POST /api/messages/update-novelty-status     # Actualizar estado de novedades

### ✅ Estado: Implementado y funcionando
- Servidor: Tablas creadas, endpoints activos
- Android: Polling funcionando, notificaciones operativas
- Probado: Mensajes del operador y novedades nuevas