¡Perfecto! Asumiendo que ya tienes la estructura de carpetas y todos los archivos .py y .html que hemos generado en su lugar, y que pip install -r requirements.txt se completó sin errores dentro de tu entorno virtual, estos son los siguientes pasos cruciales:

[root@vps-3984380-x /home/<USER>/Operativo] # sudo -i -u postgres
postgres@vps-3984380-x:~$ psql
psql (14.17 (Ubuntu 14.17-0ubuntu0.22.04.1))
Type "help" for help.

postgres=# CREATE USER joacoabe WITH PASSWORD 'isaias52';
ERROR:  role "joacoabe" already exists
postgres=# CREATE DATABASE opera OWNER joacoabe;
CREATE DATABASE
postgres=# GRANT ALL PRIVILEGES ON DATABASE opera TO joacoabe;
GRANT
postgres=# \q
postgres@vps-3984380-x:~$ exit
logout
[root@vps-3984380-x /home/<USER>/Operativo] #



Paso 1: Configurar Variables de Entorno (.env)

Localiza el archivo .env en la raíz de tu proyecto.

Ábrelo y edítalo:

DATABASE_URL: Reemplaza postgresql://opera_user:tu_contraseña_segura@localhost:5432/opera_db con tus credenciales reales de PostgreSQL.

opera_user: El usuario que creaste (o vas a crear) en PostgreSQL.

tu_contraseña_segura: La contraseña de ese usuario.

localhost: El host donde corre tu servidor PostgreSQL (si es local, suele ser localhost o 127.0.0.1).

5432: El puerto estándar de PostgreSQL.

opera_db: El nombre de la base de datos que creaste (o vas a crear).

SECRET_KEY: Reemplaza tu_flask_secret_key_muy_segura con una cadena larga y aleatoria de caracteres. Puedes generar una ejecutando en tu terminal:

python -c 'import secrets; print(secrets.token_hex(24))'
Use code with caution.
Bash
Copia y pega la salida aquí.

JWT_SECRET_KEY: Reemplaza tu_jwt_secret_key_muy_segura con otra cadena larga y aleatoria (usa el mismo comando python -c ... para generar una diferente).

Guarda el archivo .env.

IMPORTANTE: Asegúrate de que este archivo .env esté listado en tu .gitignore para no subir tus credenciales y secretos a un repositorio Git.

Paso 2: Preparar la Base de Datos PostgreSQL

Asegúrate de que PostgreSQL esté instalado y ejecutándose en tu máquina o servidor.

Conéctate a PostgreSQL usando una herramienta como psql (línea de comandos) o una GUI como pgAdmin o DBeaver.

Crea la Base de Datos (si aún no existe), usando el nombre que pusiste en DATABASE_URL:

CREATE DATABASE opera_db;
Use code with caution.
SQL
Crea el Usuario (si aún no existe), usando el nombre y contraseña que pusiste en DATABASE_URL:

CREATE USER opera_user WITH PASSWORD 'tu_contraseña_segura';
Use code with caution.
SQL
(¡Usa la contraseña real que definiste!).

Otorga Permisos al usuario sobre la base de datos:

GRANT ALL PRIVILEGES ON DATABASE opera_db TO opera_user;
-- Puede que también necesites darle permisos sobre el esquema public (a veces necesario)
GRANT USAGE ON SCHEMA public TO opera_user;
GRANT CREATE ON SCHEMA public TO opera_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO opera_user; -- Para tablas futuras
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO opera_user; -- Para secuencias futuras
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO opera_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO opera_user;
Use code with caution.
SQL
(Nota: Los comandos GRANT exactos pueden variar ligeramente según la configuración de tu PostgreSQL, pero GRANT ALL PRIVILEGES ON DATABASE suele ser suficiente inicialmente).

Paso 3: Inicializar y Aplicar Migraciones de Base de Datos

Asegúrate de que tu entorno virtual esté activado.

Asegúrate de estar en la carpeta raíz de tu proyecto (opera_app) en la terminal.

Inicializar el repositorio de migraciones (SOLO si no existe la carpeta migrations):

flask db init
Use code with caution.
Bash
Esto crea la carpeta migrations y algunos archivos dentro. Haz esto solo la primera vez. Si la carpeta ya existe, omite este paso.

Generar el script de migración inicial: Flask-Migrate comparará tus modelos SQLAlchemy (en app/models/) con el estado actual de la base de datos (que está vacía) y generará un script para crear todas las tablas.

flask db migrate -m "Crear estructura inicial de la base de datos"
Use code with caution.
Bash
Deberías ver un mensaje indicando que se generó un nuevo archivo de migración en migrations/versions/. Puedes inspeccionar este archivo .py si quieres ver el código Python que crea las tablas.

Aplicar la migración a la base de datos: Este comando ejecuta el script generado en el paso anterior sobre tu base de datos PostgreSQL real, creando las tablas.

flask db upgrade
Use code with caution.
Bash
Si todo va bien, verás logs indicando que la migración se aplicó correctamente. Ahora tus tablas existen en la base de datos opera_db.

Paso 4: Crear un Usuario Inicial (Supervisor)

Para poder iniciar sesión en el dashboard web, necesitas al menos un usuario con rol supervisor o comando. Puedes crear un comando Flask para esto.

Añade un comando a run.py (o a un archivo manage.py si lo prefieres):

# run.py (Añade esto al final del archivo)
import click
from app import db # Importa db
from app.models.user import User # Importa el modelo User

@app.cli.command("create-user")
@click.option('--folio', prompt=True, help='Folio único del usuario.')
@click.option('--password', prompt=True, hide_input=True, confirmation_prompt=True, help='Contraseña del usuario.')
@click.option('--name', prompt='Nombre completo', help='Nombre completo del usuario.')
@click.option('--role', prompt='Rol (agente, supervisor, comando)', default='agente', type=click.Choice(['agente', 'supervisor', 'comando'], case_sensitive=False), help='Rol del usuario.')
@click.option('--unit', prompt='Unidad Operativa (opcional)', default=None, help='ID o nombre de la unidad.')
def create_user(folio, password, name, role, unit):
    """Crea un nuevo usuario en la base de datos."""
    if User.query.filter_by(folio=folio).first():
        click.echo(f"Error: El folio '{folio}' ya existe.")
        return

    user = User(folio=folio, name=name, role=role, unit_id=unit)
    user.set_password(password) # Hashea la contraseña
    db.session.add(user)
    try:
        db.session.commit()
        click.echo(f"Usuario '{folio}' ({role}) creado exitosamente.")
    except Exception as e:
        db.session.rollback()
        click.echo(f"Error al crear usuario: {e}")
Use code with caution.
Python
Ejecuta el comando en tu terminal (con el entorno virtual activado):

flask create-user
Use code with caution.
Bash
Te pedirá interactivamente el folio, password (y confirmación), nombre, rol (escribe supervisor o comando), y unidad (opcional).

Usa estos datos para iniciar sesión en el dashboard web más adelante.

Paso 5: Ejecutar la Aplicación Flask

Inicia el servidor de desarrollo Flask:

flask run
Use code with caution.
Bash
Flask usará la configuración de .flaskenv (FLASK_APP=run.py, FLASK_ENV=development).

Deberías ver una salida indicando que el servidor está corriendo, usualmente en http://127.0.0.1:5000/ (o el puerto que hayas configurado).

Paso 6: Acceder al Dashboard

Abre tu navegador web.

Ve a la URL del login del dashboard. Según la configuración del blueprint dashboard_web_bp en app/routes/dashboard.py, la URL sería: http://127.0.0.1:5000/dashboard/login

Intenta iniciar sesión con el folio y password del usuario supervisor que creaste en el Paso 4.

¡Si todo ha ido bien, deberías poder iniciar sesión y ver la página principal del dashboard! Si encuentras errores, revisa la salida en la terminal donde corre flask run para obtener pistas sobre qué podría estar fallando (errores de conexión a la BD, errores en el código, etc.).