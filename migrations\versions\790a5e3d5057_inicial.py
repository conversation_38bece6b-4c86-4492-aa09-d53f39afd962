"""Inicial

Revision ID: 790a5e3d5057
Revises: 
Create Date: 2025-04-08 11:41:57.907819

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '790a5e3d5057'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('folio', sa.String(length=80), nullable=False),
    sa.Column('password_hash', sa.String(length=128), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=True),
    sa.Column('role', sa.String(length=20), nullable=False),
    sa.Column('unit_id', sa.String(length=50), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_users_folio'), ['folio'], unique=True)
        batch_op.create_index(batch_op.f('ix_users_role'), ['role'], unique=False)

    op.create_table('novelties',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('image_url', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('created_by_user_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['created_by_user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('novelties', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_novelties_created_at'), ['created_at'], unique=False)

    op.create_table('shifts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('start_time', sa.DateTime(), nullable=False),
    sa.Column('end_time', sa.DateTime(), nullable=True),
    sa.Column('mobility_type', sa.String(length=50), nullable=False),
    sa.Column('estimated_duration', sa.Integer(), nullable=True),
    sa.Column('unit_id', sa.String(length=50), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('total_distance_km', sa.Float(), nullable=True),
    sa.Column('person_id_count', sa.Integer(), nullable=True),
    sa.Column('vehicle_id_count', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('shifts', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_shifts_end_time'), ['end_time'], unique=False)
        batch_op.create_index(batch_op.f('ix_shifts_start_time'), ['start_time'], unique=False)
        batch_op.create_index(batch_op.f('ix_shifts_user_id'), ['user_id'], unique=False)

    op.create_table('gps_track_points',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('shift_id', sa.Integer(), nullable=False),
    sa.Column('latitude', sa.Float(), nullable=False),
    sa.Column('longitude', sa.Float(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('accuracy', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['shift_id'], ['shifts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('gps_track_points', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_gps_track_points_shift_id'), ['shift_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_gps_track_points_timestamp'), ['timestamp'], unique=False)

    op.create_table('identified_persons',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('shift_id', sa.Integer(), nullable=False),
    sa.Column('officer_id', sa.Integer(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.Column('location_lat', sa.Float(), nullable=True),
    sa.Column('location_lon', sa.Float(), nullable=True),
    sa.Column('dni_id_tramite', sa.String(length=50), nullable=True),
    sa.Column('last_name', sa.String(length=100), nullable=True),
    sa.Column('first_names', sa.String(length=150), nullable=True),
    sa.Column('gender', sa.String(length=10), nullable=True),
    sa.Column('dni_number', sa.String(length=20), nullable=True),
    sa.Column('dni_copy', sa.String(length=10), nullable=True),
    sa.Column('dob', sa.String(length=20), nullable=True),
    sa.Column('issue_date', sa.String(length=20), nullable=True),
    sa.Column('cuil_or_id', sa.String(length=50), nullable=True),
    sa.Column('dni_raw_string', sa.Text(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['officer_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['shift_id'], ['shifts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('identified_persons', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_identified_persons_dni_number'), ['dni_number'], unique=False)
        batch_op.create_index(batch_op.f('ix_identified_persons_last_name'), ['last_name'], unique=False)
        batch_op.create_index(batch_op.f('ix_identified_persons_officer_id'), ['officer_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_identified_persons_shift_id'), ['shift_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_identified_persons_timestamp'), ['timestamp'], unique=False)

    op.create_table('identified_vehicles',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('shift_id', sa.Integer(), nullable=False),
    sa.Column('officer_id', sa.Integer(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.Column('location_lat', sa.Float(), nullable=True),
    sa.Column('location_lon', sa.Float(), nullable=True),
    sa.Column('plate', sa.String(length=20), nullable=False),
    sa.Column('vehicle_type', sa.String(length=50), nullable=True),
    sa.Column('brand', sa.String(length=50), nullable=True),
    sa.Column('model', sa.String(length=50), nullable=True),
    sa.Column('color', sa.String(length=30), nullable=True),
    sa.Column('image_url', sa.String(length=255), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['officer_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['shift_id'], ['shifts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('identified_vehicles', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_identified_vehicles_officer_id'), ['officer_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_identified_vehicles_plate'), ['plate'], unique=False)
        batch_op.create_index(batch_op.f('ix_identified_vehicles_shift_id'), ['shift_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_identified_vehicles_timestamp'), ['timestamp'], unique=False)

    op.create_table('novelty_read_status',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('novelty_id', sa.Integer(), nullable=False),
    sa.Column('read_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['novelty_id'], ['novelties.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'novelty_id', name='_user_novelty_uc')
    )
    with op.batch_alter_table('novelty_read_status', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_novelty_read_status_novelty_id'), ['novelty_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_novelty_read_status_user_id'), ['user_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('novelty_read_status', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_novelty_read_status_user_id'))
        batch_op.drop_index(batch_op.f('ix_novelty_read_status_novelty_id'))

    op.drop_table('novelty_read_status')
    with op.batch_alter_table('identified_vehicles', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_identified_vehicles_timestamp'))
        batch_op.drop_index(batch_op.f('ix_identified_vehicles_shift_id'))
        batch_op.drop_index(batch_op.f('ix_identified_vehicles_plate'))
        batch_op.drop_index(batch_op.f('ix_identified_vehicles_officer_id'))

    op.drop_table('identified_vehicles')
    with op.batch_alter_table('identified_persons', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_identified_persons_timestamp'))
        batch_op.drop_index(batch_op.f('ix_identified_persons_shift_id'))
        batch_op.drop_index(batch_op.f('ix_identified_persons_officer_id'))
        batch_op.drop_index(batch_op.f('ix_identified_persons_last_name'))
        batch_op.drop_index(batch_op.f('ix_identified_persons_dni_number'))

    op.drop_table('identified_persons')
    with op.batch_alter_table('gps_track_points', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_gps_track_points_timestamp'))
        batch_op.drop_index(batch_op.f('ix_gps_track_points_shift_id'))

    op.drop_table('gps_track_points')
    with op.batch_alter_table('shifts', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_shifts_user_id'))
        batch_op.drop_index(batch_op.f('ix_shifts_start_time'))
        batch_op.drop_index(batch_op.f('ix_shifts_end_time'))

    op.drop_table('shifts')
    with op.batch_alter_table('novelties', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_novelties_created_at'))

    op.drop_table('novelties')
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_users_role'))
        batch_op.drop_index(batch_op.f('ix_users_folio'))

    op.drop_table('users')
    # ### end Alembic commands ###
