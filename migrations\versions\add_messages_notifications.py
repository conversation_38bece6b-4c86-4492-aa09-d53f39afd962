"""Add messages and notifications tables

Revision ID: add_messages_notifications
Revises: 
Create Date: 2025-05-26 09:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'add_messages_notifications'
down_revision = None  # Cambiar por la última revisión si existe
branch_labels = None
depends_on = None


def upgrade():
    # Crear tabla operator_messages
    op.create_table('operator_messages',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('sender_user_id', sa.Integer(), nullable=False),
        sa.Column('recipient_folio', sa.String(length=80), nullable=False),
        sa.Column('message_type', sa.String(length=20), nullable=False),
        sa.Column('is_read', sa.<PERSON>(), nullable=True),
        sa.Column('sent_via_websocket', sa.<PERSON>(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('read_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['recipient_folio'], ['users.folio'], ),
        sa.ForeignKeyConstraint(['sender_user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Crear índices para operator_messages
    op.create_index(op.f('ix_operator_messages_sender_user_id'), 'operator_messages', ['sender_user_id'], unique=False)
    op.create_index(op.f('ix_operator_messages_recipient_folio'), 'operator_messages', ['recipient_folio'], unique=False)
    op.create_index(op.f('ix_operator_messages_message_type'), 'operator_messages', ['message_type'], unique=False)
    op.create_index(op.f('ix_operator_messages_is_read'), 'operator_messages', ['is_read'], unique=False)
    op.create_index(op.f('ix_operator_messages_created_at'), 'operator_messages', ['created_at'], unique=False)
    
    # Crear tabla notification_status
    op.create_table('notification_status',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_folio', sa.String(length=80), nullable=False),
        sa.Column('last_seen_novelty_id', sa.Integer(), nullable=True),
        sa.Column('last_check_at', sa.DateTime(), nullable=True),
        sa.Column('last_notification_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['last_seen_novelty_id'], ['novelties.id'], ),
        sa.ForeignKeyConstraint(['user_folio'], ['users.folio'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Crear índices para notification_status
    op.create_index(op.f('ix_notification_status_user_folio'), 'notification_status', ['user_folio'], unique=False)


def downgrade():
    # Eliminar índices de notification_status
    op.drop_index(op.f('ix_notification_status_user_folio'), table_name='notification_status')
    
    # Eliminar tabla notification_status
    op.drop_table('notification_status')
    
    # Eliminar índices de operator_messages
    op.drop_index(op.f('ix_operator_messages_created_at'), table_name='operator_messages')
    op.drop_index(op.f('ix_operator_messages_is_read'), table_name='operator_messages')
    op.drop_index(op.f('ix_operator_messages_message_type'), table_name='operator_messages')
    op.drop_index(op.f('ix_operator_messages_recipient_folio'), table_name='operator_messages')
    op.drop_index(op.f('ix_operator_messages_sender_user_id'), table_name='operator_messages')
    
    # Eliminar tabla operator_messages
    op.drop_table('operator_messages')
