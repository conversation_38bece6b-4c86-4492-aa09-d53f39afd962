"""crear tabla novelty_read_statuses

Revision ID: d169b696ef37
Revises: 9c1b10e08aa5
Create Date: 2025-04-08 15:11:15.515461

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'd169b696ef37'
down_revision = '9c1b10e08aa5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('novelty_read_statuses',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('novelty_id', sa.Integer(), nullable=False),
    sa.Column('read_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['novelty_id'], ['novelties.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('novelty_read_status', schema=None) as batch_op:
        batch_op.drop_index('ix_novelty_read_status_novelty_id')
        batch_op.drop_index('ix_novelty_read_status_user_id')

    op.drop_table('novelty_read_status')
    with op.batch_alter_table('novelties', schema=None) as batch_op:
        batch_op.alter_column('image_url',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.String(length=500),
               existing_nullable=True)
        batch_op.drop_index('ix_novelties_created_at')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('novelties', schema=None) as batch_op:
        batch_op.create_index('ix_novelties_created_at', ['created_at'], unique=False)
        batch_op.alter_column('image_url',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=255),
               existing_nullable=True)

    op.create_table('novelty_read_status',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('novelty_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('read_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['novelty_id'], ['novelties.id'], name='novelty_read_status_novelty_id_fkey'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='novelty_read_status_user_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='novelty_read_status_pkey'),
    sa.UniqueConstraint('user_id', 'novelty_id', name='_user_novelty_uc')
    )
    with op.batch_alter_table('novelty_read_status', schema=None) as batch_op:
        batch_op.create_index('ix_novelty_read_status_user_id', ['user_id'], unique=False)
        batch_op.create_index('ix_novelty_read_status_novelty_id', ['novelty_id'], unique=False)

    op.drop_table('novelty_read_statuses')
    # ### end Alembic commands ###
