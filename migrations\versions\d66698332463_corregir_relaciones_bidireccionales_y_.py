"""Corregir relaciones bidireccionales y orden de importación

Revision ID: d66698332463
Revises: d169b696ef37
Create Date: 2025-04-09 14:18:10.159107

"""
from alembic import op
import sqlalchemy as sa
# from sqlalchemy.dialects import postgresql # No se usa explícitamente, se puede quitar

# revision identifiers, used by Alembic.
revision = 'd66698332463'
down_revision = 'd169b696ef37' # Asegúrate que este sea el ID de tu migración ANTERIOR
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - CORREGIDO ###
    print("Creando tabla 'novelty_read_status' y sus índices...")
    op.create_table('novelty_read_status',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('novelty_id', sa.Integer(), nullable=False),
        sa.Column('read_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['novelty_id'], ['novelties.id'], name=op.f('fk_novelty_read_status_novelty_id_novelties'), ondelete='CASCADE'), # Nombre FK explícito
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_novelty_read_status_user_id_users')), # Nombre FK explícito
        sa.PrimaryKeyConstraint('id', name=op.f('pk_novelty_read_status')), # Nombre PK explícito
        sa.UniqueConstraint('user_id', 'novelty_id', name=op.f('uq_novelty_read_status_user_id_novelty_id')) # Nombre UC explícito
    )
    with op.batch_alter_table('novelty_read_status', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_novelty_read_status_novelty_id'), ['novelty_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_novelty_read_status_user_id'), ['user_id'], unique=False)
    print("Tabla 'novelty_read_status' creada.")

    # --- Eliminación de tabla antigua (plural) ---
    # ¡¡DESCOMENTA la siguiente línea SOLO SI estás SEGURO de que quieres eliminarla!!
    # Si no estás seguro, déjala comentada. Si no existe, no dará error.
    # op.drop_table('novelty_read_statuses')
    # print("Intentando eliminar tabla 'novelty_read_statuses' (si existe)...") # Mensaje informativo

    # --- NO eliminar tablas de PostGIS ---
    print("Omitiendo drop_table para 'layer', 'topology', 'spatial_ref_sys' (PostGIS).")
    # op.drop_table('layer') # COMENTADO/ELIMINADO
    # op.drop_table('topology') # COMENTADO/ELIMINADO
    # op.drop_table('spatial_ref_sys') # COMENTADO/ELIMINADO

    # --- Alteraciones en la tabla 'novelties' ---
    print("Modificando tabla 'novelties'...")
    with op.batch_alter_table('novelties', schema=None) as batch_op:
        batch_op.alter_column('image_url',
               existing_type=sa.VARCHAR(length=500), # Asume que el tipo anterior era 500
               type_=sa.String(length=255),
               existing_nullable=True)
        batch_op.create_index(batch_op.f('ix_novelties_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_novelties_created_by_user_id'), ['created_by_user_id'], unique=False)
        # Podrías necesitar ajustar la constraint FK si cambiaste la relación
        # batch_op.drop_constraint(...) # Si existía una FK vieja
        # batch_op.create_foreign_key(batch_op.f('fk_novelties_created_by_user_id_users'), 'users', ['created_by_user_id'], ['id'])
    print("Tabla 'novelties' modificada.")

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - CORREGIDO ###
    print("Revirtiendo cambios en 'novelties'...")
    with op.batch_alter_table('novelties', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_novelties_created_by_user_id'))
        batch_op.drop_index(batch_op.f('ix_novelties_created_at'))
        batch_op.alter_column('image_url',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=500), # Volver al tipo anterior (si era 500)
               existing_nullable=True)
        # Si añadiste una FK nueva en upgrade, quítala aquí
        # batch_op.drop_constraint(batch_op.f('fk_novelties_created_by_user_id_users'), type_='foreignkey')

    print("Revirtiendo cambios en 'novelty_read_status'...")
    with op.batch_alter_table('novelty_read_status', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_novelty_read_status_user_id'))
        batch_op.drop_index(batch_op.f('ix_novelty_read_status_novelty_id'))
    op.drop_table('novelty_read_status')
    print("Tabla 'novelty_read_status' eliminada.")

    # --- NO recrear tablas de PostGIS ---
    print("Omitiendo create_table para 'layer', 'topology', 'spatial_ref_sys' (PostGIS).")
    # op.create_table('spatial_ref_sys', ... ) # COMENTADO/ELIMINADO
    # op.create_table('topology', ... ) # COMENTADO/ELIMINADO
    # op.create_table('layer', ... ) # COMENTADO/ELIMINADO

    # --- Recreación de tabla antigua (plural) ---
    # ¡¡DESCOMENTA las siguientes líneas SOLO SI eliminaste la tabla en upgrade
    # y necesitas poder recrearla si haces downgrade!!
    # print("Recreando tabla 'novelty_read_statuses' (si fue eliminada)...")
    # op.create_table('novelty_read_statuses',
    #     sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    #     sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    #     sa.Column('novelty_id', sa.INTEGER(), autoincrement=False, nullable=False),
    #     sa.Column('read_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    #     sa.ForeignKeyConstraint(['novelty_id'], ['novelties.id'], name='novelty_read_statuses_novelty_id_fkey'), # Nombres originales?
    #     sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='novelty_read_statuses_user_id_fkey'), # Nombres originales?
    #     sa.PrimaryKeyConstraint('id', name='novelty_read_statuses_pkey') # Nombre original?
    # )

    # ### end Alembic commands ###