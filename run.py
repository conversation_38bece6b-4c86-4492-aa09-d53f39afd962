import os
import eventlet
import eventlet.wsgi
from dotenv import load_dotenv
from flask_migrate import Migrate

# 1) Carga .env
load_dotenv()

# 2) Importa tu app y extensiones
from app import create_app, db, socketio

# 3) Crea la app y Migra
app = create_app(os.getenv('FLASK_ENV') or 'default')
migrate = Migrate(app, db)

if __name__ == '__main__':
    # === DEFINIR basedir AQUÍ ===
    basedir = os.path.abspath(os.path.dirname(__file__))

    port = int(os.environ.get('FLASK_RUN_PORT', 5005))

    # === RUTAS A LOS CERTS DE LETSENCRYPT ===
    #certfile = os.path.join(basedir,       # opcional si quieres relativo
                            #'/etc/letsencrypt/live/patagoniaservers.com.ar/fullchain.pem')
    #keyfile  = os.path.join(basedir,
                            #'/etc/letsencrypt/live/patagoniaservers.com.ar/privkey.pem')
    # (o simplemente:)
    certfile = '/etc/letsencrypt/live/patagoniaservers.com.ar/fullchain.pem'
    keyfile  = '/etc/letsencrypt/live/patagoniaservers.com.ar/privkey.pem'

    # 4) Creamos el listener TCP
    listener = eventlet.listen(('0.0.0.0', port))

    # 5) Envolvemos en SSL si existen los archivos
    if os.path.isfile(certfile) and os.path.isfile(keyfile):
        listener = eventlet.wrap_ssl(
            listener,
            certfile=certfile,
            keyfile=keyfile,
            server_side=True
        )
        print(f"INFO: Sirviendo HTTPS y WebSockets en 0.0.0.0:{port}")
    else:
        print(f"WARN: No hallé los certificados de Let's Encrypt, sirviendo HTTP en 0.0.0.0:{port}")

    # 6) Arrancamos el servidor Eventlet (SocketIO ya está montado)
    print("INFO: Iniciando Eventlet WSGI server con soporte SocketIO...")
    eventlet.wsgi.server(listener, app)
